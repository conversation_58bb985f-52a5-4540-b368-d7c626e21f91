{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": true, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": true, "isRuntimeInvisibleParameterAnnotationsKept": true, "isRuntimeInvisibleTypeAnnotationsKept": true, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "28", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 39830227100, "numberOfThreads": 20}, "dexFiles": [{"checksum": "fa7de92457ebcd87ee156a00f82749fdd07519fcb683d4472489a325ac284e35", "startup": false}, {"checksum": "d087ca70f407a596bdc1cadd1dad4996274038b16cad3a88f6e66c264382f90e", "startup": false}], "stats": {"noObfuscationPercentage": 91.92, "noOptimizationPercentage": 92.02, "noShrinkingPercentage": 91.93}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.12.14"}