[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 882, "crc": 870080169}, {"key": "com/google/gson/FieldNamingPolicy$5.class", "name": "com/google/gson/FieldNamingPolicy$5.class", "size": 1017, "crc": 206073640}, {"key": "com/google/gson/Gson$5.class", "name": "com/google/gson/Gson$5.class", "size": 2590, "crc": 447976073}, {"key": "com/google/gson/JsonDeserializer.class", "name": "com/google/gson/JsonDeserializer.class", "size": 510, "crc": 640240277}, {"key": "com/google/gson/JsonStreamParser.class", "name": "com/google/gson/JsonStreamParser.class", "size": 2725, "crc": 215691837}, {"key": "com/google/gson/Gson$FutureTypeAdapter.class", "name": "com/google/gson/Gson$FutureTypeAdapter.class", "size": 1719, "crc": 2076925212}, {"key": "com/google/gson/Gson.class", "name": "com/google/gson/Gson.class", "size": 22265, "crc": -1400961661}, {"key": "com/google/gson/FieldNamingStrategy.class", "name": "com/google/gson/FieldNamingStrategy.class", "size": 207, "crc": -1209840204}, {"key": "com/google/gson/Gson$3.class", "name": "com/google/gson/Gson$3.class", "size": 1928, "crc": -1807591796}, {"key": "com/google/gson/JsonSerializer.class", "name": "com/google/gson/JsonSerializer.class", "size": 437, "crc": 497710243}, {"key": "com/google/gson/FieldNamingPolicy$3.class", "name": "com/google/gson/FieldNamingPolicy$3.class", "size": 938, "crc": 6834735}, {"key": "com/google/gson/JsonNull.class", "name": "com/google/gson/JsonNull.class", "size": 966, "crc": 894375213}, {"key": "com/google/gson/InstanceCreator.class", "name": "com/google/gson/InstanceCreator.class", "size": 302, "crc": -1748279143}, {"key": "com/google/gson/JsonSerializationContext.class", "name": "com/google/gson/JsonSerializationContext.class", "size": 301, "crc": -253257634}, {"key": "com/google/gson/FieldNamingPolicy$1.class", "name": "com/google/gson/FieldNamingPolicy$1.class", "size": 711, "crc": -108713395}, {"key": "com/google/gson/JsonElement.class", "name": "com/google/gson/JsonElement.class", "size": 3908, "crc": -802638270}, {"key": "com/google/gson/Gson$1.class", "name": "com/google/gson/Gson$1.class", "size": 2020, "crc": 2050337351}, {"key": "com/google/gson/FieldNamingPolicy$6.class", "name": "com/google/gson/FieldNamingPolicy$6.class", "size": 1017, "crc": 452061678}, {"key": "com/google/gson/TypeAdapter$1.class", "name": "com/google/gson/TypeAdapter$1.class", "size": 1665, "crc": 264646329}, {"key": "com/google/gson/Gson$4.class", "name": "com/google/gson/Gson$4.class", "size": 1746, "crc": -1165109500}, {"key": "com/google/gson/stream/JsonReader$1.class", "name": "com/google/gson/stream/JsonReader$1.class", "size": 1369, "crc": 1723221886}, {"key": "com/google/gson/stream/JsonReader.class", "name": "com/google/gson/stream/JsonReader.class", "size": 19781, "crc": 591881913}, {"key": "com/google/gson/stream/JsonToken.class", "name": "com/google/gson/stream/JsonToken.class", "size": 1465, "crc": 856310567}, {"key": "com/google/gson/stream/MalformedJsonException.class", "name": "com/google/gson/stream/MalformedJsonException.class", "size": 838, "crc": 862652569}, {"key": "com/google/gson/stream/JsonWriter.class", "name": "com/google/gson/stream/JsonWriter.class", "size": 8565, "crc": 1699328385}, {"key": "com/google/gson/stream/JsonScope.class", "name": "com/google/gson/stream/JsonScope.class", "size": 612, "crc": 457418579}, {"key": "com/google/gson/FieldNamingPolicy$4.class", "name": "com/google/gson/FieldNamingPolicy$4.class", "size": 1017, "crc": -1660956807}, {"key": "com/google/gson/JsonIOException.class", "name": "com/google/gson/JsonIOException.class", "size": 731, "crc": -697275368}, {"key": "com/google/gson/reflect/TypeToken.class", "name": "com/google/gson/reflect/TypeToken.class", "size": 8611, "crc": -1550202026}, {"key": "com/google/gson/TypeAdapter.class", "name": "com/google/gson/TypeAdapter.class", "size": 3559, "crc": -1360343631}, {"key": "com/google/gson/JsonPrimitive.class", "name": "com/google/gson/JsonPrimitive.class", "size": 6024, "crc": 371124452}, {"key": "com/google/gson/internal/ConstructorConstructor$1.class", "name": "com/google/gson/internal/ConstructorConstructor$1.class", "size": 1268, "crc": -1148586322}, {"key": "com/google/gson/internal/ConstructorConstructor$3.class", "name": "com/google/gson/internal/ConstructorConstructor$3.class", "size": 2127, "crc": -406997406}, {"key": "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite.class", "name": "com/google/gson/internal/Streams$AppendableWriter$CurrentWrite.class", "size": 950, "crc": 1269395967}, {"key": "com/google/gson/internal/ConstructorConstructor$7.class", "name": "com/google/gson/internal/ConstructorConstructor$7.class", "size": 1025, "crc": 444454290}, {"key": "com/google/gson/internal/Excluder$1.class", "name": "com/google/gson/internal/Excluder$1.class", "size": 2345, "crc": 555017936}, {"key": "com/google/gson/internal/ConstructorConstructor$5.class", "name": "com/google/gson/internal/ConstructorConstructor$5.class", "size": 1741, "crc": 1216243831}, {"key": "com/google/gson/internal/$Gson$Types.class", "name": "com/google/gson/internal/$Gson$Types.class", "size": 11501, "crc": 1875549493}, {"key": "com/google/gson/internal/ConstructorConstructor.class", "name": "com/google/gson/internal/ConstructorConstructor.class", "size": 5977, "crc": -159926590}, {"key": "com/google/gson/internal/ConstructorConstructor$2.class", "name": "com/google/gson/internal/ConstructorConstructor$2.class", "size": 1271, "crc": 1258576938}, {"key": "com/google/gson/internal/LinkedTreeMap$KeySet.class", "name": "com/google/gson/internal/LinkedTreeMap$KeySet.class", "size": 1678, "crc": 1585450408}, {"key": "com/google/gson/internal/LinkedHashTreeMap$EntrySet$1.class", "name": "com/google/gson/internal/LinkedHashTreeMap$EntrySet$1.class", "size": 1563, "crc": -1702472017}, {"key": "com/google/gson/internal/GsonBuildConfig.class", "name": "com/google/gson/internal/GsonBuildConfig.class", "size": 394, "crc": 1105224797}, {"key": "com/google/gson/internal/LinkedHashTreeMap$AvlIterator.class", "name": "com/google/gson/internal/LinkedHashTreeMap$AvlIterator.class", "size": 1676, "crc": -889493895}, {"key": "com/google/gson/internal/LinkedTreeMap$1.class", "name": "com/google/gson/internal/LinkedTreeMap$1.class", "size": 906, "crc": 1165824200}, {"key": "com/google/gson/internal/LazilyParsedNumber.class", "name": "com/google/gson/internal/LazilyParsedNumber.class", "size": 1959, "crc": 806386224}, {"key": "com/google/gson/internal/LinkedHashTreeMap.class", "name": "com/google/gson/internal/LinkedHashTreeMap.class", "size": 13897, "crc": 910850865}, {"key": "com/google/gson/internal/JsonReaderInternalAccess.class", "name": "com/google/gson/internal/JsonReaderInternalAccess.class", "size": 484, "crc": -971838539}, {"key": "com/google/gson/internal/reflect/PreJava9ReflectionAccessor.class", "name": "com/google/gson/internal/reflect/PreJava9ReflectionAccessor.class", "size": 656, "crc": -1757522108}, {"key": "com/google/gson/internal/reflect/UnsafeReflectionAccessor.class", "name": "com/google/gson/internal/reflect/UnsafeReflectionAccessor.class", "size": 3157, "crc": 542269868}, {"key": "com/google/gson/internal/reflect/ReflectionAccessor.class", "name": "com/google/gson/internal/reflect/ReflectionAccessor.class", "size": 883, "crc": 1211536977}, {"key": "com/google/gson/internal/LinkedTreeMap$EntrySet.class", "name": "com/google/gson/internal/LinkedTreeMap$EntrySet.class", "size": 2058, "crc": -2052535670}, {"key": "com/google/gson/internal/ConstructorConstructor$4.class", "name": "com/google/gson/internal/ConstructorConstructor$4.class", "size": 1022, "crc": **********}, {"key": "com/google/gson/internal/ObjectConstructor.class", "name": "com/google/gson/internal/ObjectConstructor.class", "size": 262, "crc": 782083352}, {"key": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator.class", "name": "com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator.class", "size": 2024, "crc": **********}, {"key": "com/google/gson/internal/PreJava9DateFormatProvider.class", "name": "com/google/gson/internal/PreJava9DateFormatProvider.class", "size": 1943, "crc": 783350989}, {"key": "com/google/gson/internal/$Gson$Preconditions.class", "name": "com/google/gson/internal/$Gson$Preconditions.class", "size": 892, "crc": **********}, {"key": "com/google/gson/internal/LinkedHashTreeMap$KeySet.class", "name": "com/google/gson/internal/LinkedHashTreeMap$KeySet.class", "size": 1722, "crc": -**********}, {"key": "com/google/gson/internal/ConstructorConstructor$6.class", "name": "com/google/gson/internal/ConstructorConstructor$6.class", "size": 1028, "crc": -925590615}, {"key": "com/google/gson/internal/LinkedHashTreeMap$EntrySet.class", "name": "com/google/gson/internal/LinkedHashTreeMap$EntrySet.class", "size": 2114, "crc": -456144976}, {"key": "com/google/gson/internal/Streams.class", "name": "com/google/gson/internal/Streams.class", "size": 2402, "crc": 530714930}, {"key": "com/google/gson/internal/ConstructorConstructor$12.class", "name": "com/google/gson/internal/ConstructorConstructor$12.class", "size": 1030, "crc": 60544170}, {"key": "com/google/gson/internal/UnsafeAllocator.class", "name": "com/google/gson/internal/UnsafeAllocator.class", "size": 3098, "crc": -776711208}, {"key": "com/google/gson/internal/ConstructorConstructor$10.class", "name": "com/google/gson/internal/ConstructorConstructor$10.class", "size": 1045, "crc": -1591424853}, {"key": "com/google/gson/internal/UnsafeAllocator$4.class", "name": "com/google/gson/internal/UnsafeAllocator$4.class", "size": 1083, "crc": -576303828}, {"key": "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$ParameterizedTypeImpl.class", "size": 3176, "crc": 997312308}, {"key": "com/google/gson/internal/ConstructorConstructor$8.class", "name": "com/google/gson/internal/ConstructorConstructor$8.class", "size": 1024, "crc": -624779848}, {"key": "com/google/gson/internal/LinkedHashTreeMap$Node.class", "name": "com/google/gson/internal/LinkedHashTreeMap$Node.class", "size": 3463, "crc": 297332779}, {"key": "com/google/gson/internal/ConstructorConstructor$14.class", "name": "com/google/gson/internal/ConstructorConstructor$14.class", "size": 2015, "crc": 591459576}, {"key": "com/google/gson/internal/LinkedTreeMap.class", "name": "com/google/gson/internal/LinkedTreeMap.class", "size": 11246, "crc": 1510688138}, {"key": "com/google/gson/internal/UnsafeAllocator$2.class", "name": "com/google/gson/internal/UnsafeAllocator$2.class", "size": 1238, "crc": 1693306185}, {"key": "com/google/gson/internal/Primitives.class", "name": "com/google/gson/internal/Primitives.class", "size": 2812, "crc": -1602782202}, {"key": "com/google/gson/internal/LinkedTreeMap$KeySet$1.class", "name": "com/google/gson/internal/LinkedTreeMap$KeySet$1.class", "size": 1312, "crc": 993827674}, {"key": "com/google/gson/internal/ConstructorConstructor$11.class", "name": "com/google/gson/internal/ConstructorConstructor$11.class", "size": 1024, "crc": 1216414205}, {"key": "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$GenericArrayTypeImpl.class", "size": 1536, "crc": 1503999764}, {"key": "com/google/gson/internal/LinkedHashTreeMap$1.class", "name": "com/google/gson/internal/LinkedHashTreeMap$1.class", "size": 922, "crc": -1652325077}, {"key": "com/google/gson/internal/ConstructorConstructor$9.class", "name": "com/google/gson/internal/ConstructorConstructor$9.class", "size": 1047, "crc": -126981341}, {"key": "com/google/gson/internal/LinkedHashTreeMap$AvlBuilder.class", "name": "com/google/gson/internal/LinkedHashTreeMap$AvlBuilder.class", "size": 2444, "crc": 778436097}, {"key": "com/google/gson/internal/$Gson$Types$WildcardTypeImpl.class", "name": "com/google/gson/internal/$Gson$Types$WildcardTypeImpl.class", "size": 2350, "crc": 1463900226}, {"key": "com/google/gson/internal/LinkedHashTreeMap$KeySet$1.class", "name": "com/google/gson/internal/LinkedHashTreeMap$KeySet$1.class", "size": 1364, "crc": 1416268044}, {"key": "com/google/gson/internal/ConstructorConstructor$13.class", "name": "com/google/gson/internal/ConstructorConstructor$13.class", "size": 1045, "crc": -709282407}, {"key": "com/google/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator.class", "name": "com/google/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator.class", "size": 2076, "crc": 1224800457}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter.class", "size": 5317, "crc": 1266687131}, {"key": "com/google/gson/internal/bind/TypeAdapters$8.class", "name": "com/google/gson/internal/bind/TypeAdapters$8.class", "size": 1832, "crc": 994331330}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter.class", "size": 3193, "crc": 1392416310}, {"key": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory.class", "size": 4068, "crc": 568103208}, {"key": "com/google/gson/internal/bind/TypeAdapters$13.class", "name": "com/google/gson/internal/bind/TypeAdapters$13.class", "size": 1754, "crc": -585812922}, {"key": "com/google/gson/internal/bind/TypeAdapters$35$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$35$1.class", "size": 2116, "crc": -1747968418}, {"key": "com/google/gson/internal/bind/TypeAdapters$11.class", "name": "com/google/gson/internal/bind/TypeAdapters$11.class", "size": 1946, "crc": 1111591306}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter$1.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter$1.class", "size": 1151, "crc": 1589912939}, {"key": "com/google/gson/internal/bind/TimeTypeAdapter$1.class", "name": "com/google/gson/internal/bind/TimeTypeAdapter$1.class", "size": 1164, "crc": -1081019378}, {"key": "com/google/gson/internal/bind/TypeAdapters$35.class", "name": "com/google/gson/internal/bind/TypeAdapters$35.class", "size": 2071, "crc": -1829158283}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$1.class", "size": 2990, "crc": 605859577}, {"key": "com/google/gson/internal/bind/JsonTreeReader.class", "name": "com/google/gson/internal/bind/JsonTreeReader.class", "size": 7816, "crc": -652972306}, {"key": "com/google/gson/internal/bind/TypeAdapters$31.class", "name": "com/google/gson/internal/bind/TypeAdapters$31.class", "size": 1427, "crc": -1207127605}, {"key": "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.class", "name": "com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.class", "size": 2720, "crc": -1446795452}, {"key": "com/google/gson/internal/bind/DateTypeAdapter.class", "name": "com/google/gson/internal/bind/DateTypeAdapter.class", "size": 3681, "crc": 2125517497}, {"key": "com/google/gson/internal/bind/TypeAdapters$28.class", "name": "com/google/gson/internal/bind/TypeAdapters$28.class", "size": 2415, "crc": 1249905307}, {"key": "com/google/gson/internal/bind/TypeAdapters$15.class", "name": "com/google/gson/internal/bind/TypeAdapters$15.class", "size": 2273, "crc": 545421824}, {"key": "com/google/gson/internal/bind/TypeAdapters$17.class", "name": "com/google/gson/internal/bind/TypeAdapters$17.class", "size": 1961, "crc": 1682733703}, {"key": "com/google/gson/internal/bind/util/ISO8601Utils.class", "name": "com/google/gson/internal/bind/util/ISO8601Utils.class", "size": 7300, "crc": -1401836953}, {"key": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter.class", "name": "com/google/gson/internal/bind/TypeAdapters$EnumTypeAdapter.class", "size": 3682, "crc": -2046310868}, {"key": "com/google/gson/internal/bind/SqlDateTypeAdapter$1.class", "name": "com/google/gson/internal/bind/SqlDateTypeAdapter$1.class", "size": 1176, "crc": 416632394}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$Adapter.class", "size": 4037, "crc": -1804828811}, {"key": "com/google/gson/internal/bind/TimeTypeAdapter.class", "name": "com/google/gson/internal/bind/TimeTypeAdapter.class", "size": 2468, "crc": 1085431468}, {"key": "com/google/gson/internal/bind/TypeAdapters$33.class", "name": "com/google/gson/internal/bind/TypeAdapters$33.class", "size": 1978, "crc": -1883541448}, {"key": "com/google/gson/internal/bind/TypeAdapters$10.class", "name": "com/google/gson/internal/bind/TypeAdapters$10.class", "size": 2695, "crc": -1810558558}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$GsonContextImpl.class", "size": 2087, "crc": -485952640}, {"key": "com/google/gson/internal/bind/TypeAdapters$34.class", "name": "com/google/gson/internal/bind/TypeAdapters$34.class", "size": 1989, "crc": -1014927884}, {"key": "com/google/gson/internal/bind/TypeAdapters$36.class", "name": "com/google/gson/internal/bind/TypeAdapters$36.class", "size": 1148, "crc": -725343298}, {"key": "com/google/gson/internal/bind/JsonTreeReader$1.class", "name": "com/google/gson/internal/bind/JsonTreeReader$1.class", "size": 749, "crc": 871014646}, {"key": "com/google/gson/internal/bind/TypeAdapters$9.class", "name": "com/google/gson/internal/bind/TypeAdapters$9.class", "size": 1613, "crc": 1657725623}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$SingleTypeFactory.class", "size": 2646, "crc": 1296847537}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField.class", "size": 1006, "crc": 749952838}, {"key": "com/google/gson/internal/bind/TypeAdapters$26$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$26$1.class", "size": 1862, "crc": -1970223578}, {"key": "com/google/gson/internal/bind/ObjectTypeAdapter$2.class", "name": "com/google/gson/internal/bind/ObjectTypeAdapter$2.class", "size": 959, "crc": -905950961}, {"key": "com/google/gson/internal/bind/TypeAdapters$12.class", "name": "com/google/gson/internal/bind/TypeAdapters$12.class", "size": 1753, "crc": -197380626}, {"key": "com/google/gson/internal/bind/TypeAdapters$16.class", "name": "com/google/gson/internal/bind/TypeAdapters$16.class", "size": 1868, "crc": 101631472}, {"key": "com/google/gson/internal/bind/DateTypeAdapter$1.class", "name": "com/google/gson/internal/bind/DateTypeAdapter$1.class", "size": 1165, "crc": 622232773}, {"key": "com/google/gson/internal/bind/TypeAdapters$32.class", "name": "com/google/gson/internal/bind/TypeAdapters$32.class", "size": 1797, "crc": -515713920}, {"key": "com/google/gson/internal/bind/TreeTypeAdapter$1.class", "name": "com/google/gson/internal/bind/TreeTypeAdapter$1.class", "size": 253, "crc": 1572567365}, {"key": "com/google/gson/internal/bind/TypeAdapters$30.class", "name": "com/google/gson/internal/bind/TypeAdapters$30.class", "size": 1488, "crc": 10592079}, {"key": "com/google/gson/internal/bind/TypeAdapters$14.class", "name": "com/google/gson/internal/bind/TypeAdapters$14.class", "size": 2277, "crc": 1003042131}, {"key": "com/google/gson/internal/bind/TypeAdapters$29.class", "name": "com/google/gson/internal/bind/TypeAdapters$29.class", "size": 4849, "crc": -1996044532}, {"key": "com/google/gson/internal/bind/TypeAdapters$25.class", "name": "com/google/gson/internal/bind/TypeAdapters$25.class", "size": 1591, "crc": 843429999}, {"key": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/ReflectiveTypeAdapterFactory.class", "size": 8880, "crc": 2108457855}, {"key": "com/google/gson/internal/bind/TypeAdapters$18.class", "name": "com/google/gson/internal/bind/TypeAdapters$18.class", "size": 1961, "crc": 578909642}, {"key": "com/google/gson/internal/bind/TypeAdapters$3.class", "name": "com/google/gson/internal/bind/TypeAdapters$3.class", "size": 1903, "crc": 1597021604}, {"key": "com/google/gson/internal/bind/TypeAdapters$1.class", "name": "com/google/gson/internal/bind/TypeAdapters$1.class", "size": 1730, "crc": 1495702394}, {"key": "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.class", "size": 7522, "crc": -1239707063}, {"key": "com/google/gson/internal/bind/TypeAdapters$27.class", "name": "com/google/gson/internal/bind/TypeAdapters$27.class", "size": 2939, "crc": 1925867447}, {"key": "com/google/gson/internal/bind/TypeAdapters$23.class", "name": "com/google/gson/internal/bind/TypeAdapters$23.class", "size": 1895, "crc": 708924200}, {"key": "com/google/gson/internal/bind/TypeAdapters$5.class", "name": "com/google/gson/internal/bind/TypeAdapters$5.class", "size": 1975, "crc": 1933036755}, {"key": "com/google/gson/internal/bind/ArrayTypeAdapter$1.class", "name": "com/google/gson/internal/bind/ArrayTypeAdapter$1.class", "size": 1963, "crc": -1765171309}, {"key": "com/google/gson/internal/bind/TypeAdapters$7.class", "name": "com/google/gson/internal/bind/TypeAdapters$7.class", "size": 1949, "crc": 1380142382}, {"key": "com/google/gson/internal/bind/CollectionTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/CollectionTypeAdapterFactory.class", "size": 2663, "crc": -2053143539}, {"key": "com/google/gson/internal/bind/TypeAdapters$21.class", "name": "com/google/gson/internal/bind/TypeAdapters$21.class", "size": 1942, "crc": -1672492228}, {"key": "com/google/gson/internal/bind/MapTypeAdapterFactory.class", "name": "com/google/gson/internal/bind/MapTypeAdapterFactory.class", "size": 3508, "crc": -201749663}, {"key": "com/google/gson/internal/bind/TypeAdapters.class", "name": "com/google/gson/internal/bind/TypeAdapters.class", "size": 9936, "crc": 1398042907}, {"key": "com/google/gson/internal/bind/SqlDateTypeAdapter.class", "name": "com/google/gson/internal/bind/SqlDateTypeAdapter.class", "size": 2470, "crc": 2115050998}, {"key": "com/google/gson/internal/bind/TypeAdapters$26.class", "name": "com/google/gson/internal/bind/TypeAdapters$26.class", "size": 1529, "crc": -1057830985}, {"key": "com/google/gson/internal/bind/JsonTreeWriter.class", "name": "com/google/gson/internal/bind/JsonTreeWriter.class", "size": 5374, "crc": 1684709614}, {"key": "com/google/gson/internal/bind/TypeAdapters$19.class", "name": "com/google/gson/internal/bind/TypeAdapters$19.class", "size": 1875, "crc": -767322385}, {"key": "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.class", "name": "com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.class", "size": 3663, "crc": 623473170}, {"key": "com/google/gson/internal/bind/TypeAdapters$24.class", "name": "com/google/gson/internal/bind/TypeAdapters$24.class", "size": 1854, "crc": 513968942}, {"key": "com/google/gson/internal/bind/JsonTreeWriter$1.class", "name": "com/google/gson/internal/bind/JsonTreeWriter$1.class", "size": 824, "crc": 1626160856}, {"key": "com/google/gson/internal/bind/TypeAdapters$2.class", "name": "com/google/gson/internal/bind/TypeAdapters$2.class", "size": 3142, "crc": 212524099}, {"key": "com/google/gson/internal/bind/TypeAdapters$6.class", "name": "com/google/gson/internal/bind/TypeAdapters$6.class", "size": 1946, "crc": -2090378403}, {"key": "com/google/gson/internal/bind/TypeAdapters$20.class", "name": "com/google/gson/internal/bind/TypeAdapters$20.class", "size": 1870, "crc": -887924603}, {"key": "com/google/gson/internal/bind/TypeAdapters$22.class", "name": "com/google/gson/internal/bind/TypeAdapters$22.class", "size": 2142, "crc": -685908452}, {"key": "com/google/gson/internal/bind/TypeAdapters$4.class", "name": "com/google/gson/internal/bind/TypeAdapters$4.class", "size": 1878, "crc": -1708086788}, {"key": "com/google/gson/internal/bind/ArrayTypeAdapter.class", "name": "com/google/gson/internal/bind/ArrayTypeAdapter.class", "size": 3254, "crc": -1301626698}, {"key": "com/google/gson/internal/UnsafeAllocator$3.class", "name": "com/google/gson/internal/UnsafeAllocator$3.class", "size": 1117, "crc": -2085304297}, {"key": "com/google/gson/internal/LinkedTreeMap$EntrySet$1.class", "name": "com/google/gson/internal/LinkedTreeMap$EntrySet$1.class", "size": 1511, "crc": 562560208}, {"key": "com/google/gson/internal/Excluder.class", "name": "com/google/gson/internal/Excluder.class", "size": 7224, "crc": -196509711}, {"key": "com/google/gson/internal/Streams$AppendableWriter.class", "name": "com/google/gson/internal/Streams$AppendableWriter.class", "size": 1336, "crc": 677469306}, {"key": "com/google/gson/internal/UnsafeAllocator$1.class", "name": "com/google/gson/internal/UnsafeAllocator$1.class", "size": 1195, "crc": 1687917976}, {"key": "com/google/gson/internal/JavaVersion.class", "name": "com/google/gson/internal/JavaVersion.class", "size": 1999, "crc": -1019040875}, {"key": "com/google/gson/internal/LinkedTreeMap$Node.class", "name": "com/google/gson/internal/LinkedTreeMap$Node.class", "size": 3354, "crc": -861744721}, {"key": "com/google/gson/FieldNamingPolicy$2.class", "name": "com/google/gson/FieldNamingPolicy$2.class", "size": 837, "crc": 1482825256}, {"key": "com/google/gson/Gson$2.class", "name": "com/google/gson/Gson$2.class", "size": 2023, "crc": 1016961716}, {"key": "com/google/gson/annotations/SerializedName.class", "name": "com/google/gson/annotations/SerializedName.class", "size": 566, "crc": 1396312547}, {"key": "com/google/gson/annotations/Expose.class", "name": "com/google/gson/annotations/Expose.class", "size": 523, "crc": 2069862632}, {"key": "com/google/gson/annotations/JsonAdapter.class", "name": "com/google/gson/annotations/JsonAdapter.class", "size": 548, "crc": -988546776}, {"key": "com/google/gson/annotations/Until.class", "name": "com/google/gson/annotations/Until.class", "size": 456, "crc": -343075969}, {"key": "com/google/gson/annotations/Since.class", "name": "com/google/gson/annotations/Since.class", "size": 456, "crc": -499458582}, {"key": "com/google/gson/TypeAdapterFactory.class", "name": "com/google/gson/TypeAdapterFactory.class", "size": 384, "crc": 464430588}, {"key": "com/google/gson/LongSerializationPolicy.class", "name": "com/google/gson/LongSerializationPolicy.class", "size": 1507, "crc": -*********}, {"key": "com/google/gson/FieldNamingPolicy.class", "name": "com/google/gson/FieldNamingPolicy.class", "size": 3456, "crc": 1809005365}, {"key": "com/google/gson/LongSerializationPolicy$1.class", "name": "com/google/gson/LongSerializationPolicy$1.class", "size": 735, "crc": -1116315370}, {"key": "com/google/gson/DefaultDateTypeAdapter.class", "name": "com/google/gson/DefaultDateTypeAdapter.class", "size": 6560, "crc": -*********}, {"key": "com/google/gson/JsonSyntaxException.class", "name": "com/google/gson/JsonSyntaxException.class", "size": 743, "crc": *********}, {"key": "com/google/gson/JsonArray.class", "name": "com/google/gson/JsonArray.class", "size": 5649, "crc": 1652882322}, {"key": "com/google/gson/JsonParseException.class", "name": "com/google/gson/JsonParseException.class", "size": 732, "crc": -*********}, {"key": "com/google/gson/LongSerializationPolicy$2.class", "name": "com/google/gson/LongSerializationPolicy$2.class", "size": 876, "crc": -*********}, {"key": "com/google/gson/JsonParser.class", "name": "com/google/gson/JsonParser.class", "size": 2757, "crc": 2025507240}, {"key": "com/google/gson/GsonBuilder.class", "name": "com/google/gson/GsonBuilder.class", "size": 9796, "crc": 1680643681}, {"key": "com/google/gson/FieldAttributes.class", "name": "com/google/gson/FieldAttributes.class", "size": 2185, "crc": *********}, {"key": "com/google/gson/JsonDeserializationContext.class", "name": "com/google/gson/JsonDeserializationContext.class", "size": 413, "crc": *********}, {"key": "com/google/gson/JsonObject.class", "name": "com/google/gson/JsonObject.class", "size": 4250, "crc": -856750390}, {"key": "com/google/gson/ExclusionStrategy.class", "name": "com/google/gson/ExclusionStrategy.class", "size": 291, "crc": 1157328748}, {"key": "META-INF/maven/com.google.code.gson/gson/pom.xml", "name": "META-INF/maven/com.google.code.gson/gson/pom.xml", "size": 2527, "crc": -1032208218}, {"key": "META-INF/maven/com.google.code.gson/gson/pom.properties", "name": "META-INF/maven/com.google.code.gson/gson/pom.properties", "size": 109, "crc": -364851618}]