[{"key": "androidx/compose/runtime/saveable/ListSaverKt$listSaver$1.class", "name": "androidx/compose/runtime/saveable/ListSaverKt$listSaver$1.class", "size": 3244, "crc": -179959789}, {"key": "androidx/compose/runtime/saveable/ListSaverKt.class", "name": "androidx/compose/runtime/saveable/ListSaverKt.class", "size": 2301, "crc": -1252013382}, {"key": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$1.class", "name": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$1.class", "size": 3704, "crc": -1935441816}, {"key": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$2.class", "name": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$2.class", "size": 3068, "crc": 114243231}, {"key": "androidx/compose/runtime/saveable/MapSaverKt.class", "name": "androidx/compose/runtime/saveable/MapSaverKt.class", "size": 2002, "crc": -1164909316}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$1.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$1.class", "size": 3456, "crc": 60073526}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$2.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$2.class", "size": 3104, "crc": -640177545}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$rememberSaveable$1$1.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$rememberSaveable$1$1.class", "size": 2626, "crc": 509369653}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt.class", "size": 12516, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableHolder$valueProvider$1.class", "name": "androidx/compose/runtime/saveable/SaveableHolder$valueProvider$1.class", "size": 2890, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableHolder.class", "name": "androidx/compose/runtime/saveable/SaveableHolder.class", "size": 6238, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolder.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolder.class", "size": 1173, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$1.class", "size": 2155, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$2.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$2.class", "size": 1812, "crc": -875970692}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion.class", "size": 1424, "crc": 311452730}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1$invoke$$inlined$onDispose$1.class", "size": 3152, "crc": 405480672}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1.class", "size": 4340, "crc": 547930482}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$canBeSaved$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$canBeSaved$1.class", "size": 1710, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl.class", "size": 14351, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderKt$rememberSaveableStateHolder$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderKt$rememberSaveableStateHolder$1.class", "size": 1575, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderKt.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderKt.class", "size": 4531, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistry$Entry.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistry$Entry.class", "size": 607, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistry.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistry.class", "size": 1625, "crc": 524859353}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl$registerProvider$3.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl$registerProvider$3.class", "size": 2393, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl.class", "size": 11087, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryKt$LocalSaveableStateRegistry$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryKt$LocalSaveableStateRegistry$1.class", "size": 1296, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryKt.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryKt.class", "size": 4830, "crc": 404275690}, {"key": "androidx/compose/runtime/saveable/Saver.class", "name": "androidx/compose/runtime/saveable/Saver.class", "size": 1156, "crc": 80893814}, {"key": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$1.class", "name": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$1.class", "size": 1447, "crc": 30530134}, {"key": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$2.class", "name": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$2.class", "size": 1054, "crc": 1722243167}, {"key": "androidx/compose/runtime/saveable/SaverKt$Saver$1.class", "name": "androidx/compose/runtime/saveable/SaverKt$Saver$1.class", "size": 2305, "crc": -467125563}, {"key": "androidx/compose/runtime/saveable/SaverKt.class", "name": "androidx/compose/runtime/saveable/SaverKt.class", "size": 2864, "crc": -646654035}, {"key": "androidx/compose/runtime/saveable/SaverScope.class", "name": "androidx/compose/runtime/saveable/SaverScope.class", "size": 589, "crc": 413480341}, {"key": "META-INF/androidx.compose.runtime_runtime-saveable.version", "name": "META-INF/androidx.compose.runtime_runtime-saveable.version", "size": 6, "crc": 333960004}, {"key": "META-INF/runtime-saveable_release.kotlin_module", "name": "META-INF/runtime-saveable_release.kotlin_module", "size": 164, "crc": -574844071}]