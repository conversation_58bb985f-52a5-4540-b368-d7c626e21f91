1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="cn.ykload.flowmix"
4    android:versionCode="2"
5    android:versionName="Alpha 2" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="36" />
10
11    <!-- 音频效果权限 -->
12    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
12-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:22-77
13
14    <!-- 蓝牙权限 - 用于检测蓝牙音频设备 -->
15    <uses-permission
15-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.BLUETOOTH"
16-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:22-65
17        android:maxSdkVersion="30" />
17-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.BLUETOOTH_ADMIN"
19-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:22-71
20        android:maxSdkVersion="30" />
20-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- Android 12+ 蓝牙权限 -->
23    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
23-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:5-76
23-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:22-73
24    <uses-permission
24-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:5-18:31
25        android:name="android.permission.BLUETOOTH_SCAN"
25-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:22-70
26        android:usesPermissionFlags="neverForLocation" />
26-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:17:9-55
27
28    <!-- 网络权限 -->
29    <uses-permission android:name="android.permission.INTERNET" />
29-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:5-67
29-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:22-64
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:5-79
30-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:22-76
31
32    <!-- 文件读取权限 - Android 12及以下 -->
33    <uses-permission
33-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:5-26:38
34        android:name="android.permission.READ_EXTERNAL_STORAGE"
34-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:22-77
35        android:maxSdkVersion="32" />
35-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:26:9-35
36
37    <!-- Android 13+ 媒体文件权限 -->
38    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
38-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:5-75
38-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:22-72
39    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
39-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:5-76
39-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:22-73
40    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
40-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:5-75
40-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:22-72
41
42    <!-- 管理外部存储权限 - 用于访问所有文件 -->
43    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
43-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:5-35:40
43-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:22-79
44
45    <!-- 查询其他应用的权限 -->
46    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
46-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:5-39:53
46-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:22-74
47
48    <!-- 前台服务权限 - 用于保活服务 -->
49    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
49-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:5-77
49-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:22-74
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
50-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:5-92
50-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:22-89
51
52    <!-- 通知权限 - Android 13+ -->
53    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
53-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:5-77
53-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:22-74
54
55    <!-- 开机自启动权限 -->
56    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
56-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:5-81
56-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:22-78
57
58    <!-- 唤醒锁权限 - 用于保持CPU运行 -->
59    <uses-permission android:name="android.permission.WAKE_LOCK" />
59-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:5-68
59-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:22-65
60
61    <!-- 声明需要查询的特定应用 -->
62    <queries>
62-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:55:5-57:15
63        <package android:name="cn.ykload.seeq" />
63-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:9-50
63-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:18-47
64    </queries>
65
66    <permission
66-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
67        android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
71
72    <application
72-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:59:5-145:19
73        android:allowBackup="true"
73-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:60:9-35
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
75        android:dataExtractionRules="@xml/data_extraction_rules"
75-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:61:9-65
76        android:extractNativeLibs="false"
77        android:fullBackupContent="@xml/backup_rules"
77-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:62:9-54
78        android:icon="@mipmap/ic_launcher"
78-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:63:9-43
79        android:label="@string/app_name"
79-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:64:9-41
80        android:roundIcon="@mipmap/ic_launcher_round"
80-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:65:9-54
81        android:supportsRtl="true"
81-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:66:9-35
82        android:theme="@style/Theme.Flowmix" >
82-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:67:9-45
83
84        <!-- 应用级元数据：声明为音频效果应用 -->
85        <meta-data
85-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:70:9-72:36
86            android:name="android.media.audio_effect_app"
86-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:71:13-58
87            android:value="true" />
87-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:72:13-33
88        <meta-data
88-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:73:9-75:39
89            android:name="android.media.audio_effect.equalizer"
89-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:74:13-64
90            android:value="Flowmix" />
90-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:75:13-36
91        <meta-data
91-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:76:9-78:61
92            android:name="android.media.audio_effect.description"
92-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:77:13-66
93            android:value="Flowmix - 全局 AutoEq 应用  \awa/" />
93-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:78:13-58
94
95        <activity
95-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:79:9-98:20
96            android:name="cn.ykload.flowmix.MainActivity"
96-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:80:13-41
97            android:exported="true"
97-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:81:13-36
98            android:label="@string/app_name"
98-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:82:13-45
99            android:launchMode="singleTop"
99-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:84:13-43
100            android:theme="@style/Theme.Flowmix" >
100-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:83:13-49
101            <intent-filter>
101-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:85:13-89:29
102                <action android:name="android.intent.action.MAIN" />
102-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:17-69
102-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:25-66
103
104                <category android:name="android.intent.category.LAUNCHER" />
104-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:17-77
104-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:27-74
105            </intent-filter>
106
107            <!-- 处理通知点击事件 -->
108            <intent-filter>
108-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:92:13-96:29
109                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_TOGGLE_FLOWMIX" />
109-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:17-114
109-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:25-111
110                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_OPEN_APP" />
110-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:17-108
110-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:25-105
111
112                <category android:name="android.intent.category.DEFAULT" />
112-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:17-76
112-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:27-73
113            </intent-filter>
114        </activity>
115
116        <!-- 均衡器Activity - 处理来自其他应用的音频效果控制请求 -->
117        <activity
117-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:101:9-123:20
118            android:name="cn.ykload.flowmix.EqualizerActivity"
118-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:102:13-46
119            android:exported="true"
119-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:103:13-36
120            android:label="Flowmix - 全局 AutoEq 应用  \awa/"
120-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:104:13-58
121            android:launchMode="singleTop"
121-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:106:13-43
122            android:theme="@style/Theme.Flowmix" >
122-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:105:13-49
123
124            <!-- 音频效果控制会话意图过滤器 - 注册为系统均衡器 -->
125            <intent-filter>
125-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:109:13-114:29
126                <action android:name="android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION" />
126-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:17-97
126-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:25-94
127                <action android:name="android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION" />
127-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:17-98
127-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:25-95
128                <action android:name="android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL" />
128-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:17-98
128-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:25-95
129
130                <category android:name="android.intent.category.DEFAULT" />
130-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:17-76
130-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:27-73
131            </intent-filter>
132
133            <!-- 元数据：标识为音频效果应用 -->
134            <meta-data
134-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:117:13-119:40
135                android:name="android.media.audio_effect"
135-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:118:17-58
136                android:value="true" />
136-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:119:17-37
137            <meta-data
137-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:73:9-75:39
138                android:name="android.media.audio_effect.equalizer"
138-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:74:13-64
139                android:value="true" />
139-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:75:13-36
140        </activity>
141
142        <!-- Flowmix 保活前台服务 -->
143        <service
143-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:126:9-131:44
144            android:name="cn.ykload.flowmix.service.FlowmixKeepAliveService"
144-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:127:13-60
145            android:enabled="true"
145-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:128:13-35
146            android:exported="false"
146-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:129:13-37
147            android:foregroundServiceType="mediaPlayback"
147-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:130:13-58
148            android:stopWithTask="false" />
148-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:131:13-41
149
150        <!-- 开机自启动接收器 -->
151        <receiver
151-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:134:9-144:20
152            android:name="cn.ykload.flowmix.receiver.BootReceiver"
152-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:135:13-50
153            android:enabled="true"
153-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:136:13-35
154            android:exported="true" >
154-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:137:13-36
155            <intent-filter android:priority="1000" >
155-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:13-143:29
155-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:28-51
156                <action android:name="android.intent.action.BOOT_COMPLETED" />
156-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:17-79
156-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:25-76
157                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
157-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:17-84
157-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:25-81
158                <action android:name="android.intent.action.PACKAGE_REPLACED" />
158-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:17-81
158-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:25-78
159
160                <data android:scheme="package" />
160-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:17-50
160-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:23-47
161            </intent-filter>
162        </receiver>
163
164        <provider
164-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
165            android:name="androidx.startup.InitializationProvider"
165-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
166            android:authorities="cn.ykload.flowmix.androidx-startup"
166-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
167            android:exported="false" >
167-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
168            <meta-data
168-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.emoji2.text.EmojiCompatInitializer"
169-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
170                android:value="androidx.startup" />
170-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
172-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
173                android:value="androidx.startup" />
173-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
176                android:value="androidx.startup" />
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
177        </provider>
178
179        <receiver
179-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
180            android:name="androidx.profileinstaller.ProfileInstallReceiver"
180-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
181            android:directBootAware="false"
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
182            android:enabled="true"
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
183            android:exported="true"
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
184            android:permission="android.permission.DUMP" >
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
186                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
189                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
192                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
193            </intent-filter>
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
195                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
196            </intent-filter>
197        </receiver>
198    </application>
199
200</manifest>
