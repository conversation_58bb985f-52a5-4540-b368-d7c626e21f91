[{"key": "androidx/compose/ui/AbsoluteAlignment.class", "name": "androidx/compose/ui/AbsoluteAlignment.class", "size": 3396, "crc": -1578835034}, {"key": "androidx/compose/ui/Actual_androidKt.class", "name": "androidx/compose/ui/Actual_androidKt.class", "size": 2372, "crc": -280287135}, {"key": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "size": 2305, "crc": -527585290}, {"key": "androidx/compose/ui/Actual_jvmKt.class", "name": "androidx/compose/ui/Actual_jvmKt.class", "size": 3828, "crc": -2002173463}, {"key": "androidx/compose/ui/Alignment$Companion.class", "name": "androidx/compose/ui/Alignment$Companion.class", "size": 5353, "crc": 197618866}, {"key": "androidx/compose/ui/Alignment$Horizontal.class", "name": "androidx/compose/ui/Alignment$Horizontal.class", "size": 1439, "crc": 1792336007}, {"key": "androidx/compose/ui/Alignment$Vertical.class", "name": "androidx/compose/ui/Alignment$Vertical.class", "size": 1298, "crc": -769869361}, {"key": "androidx/compose/ui/Alignment.class", "name": "androidx/compose/ui/Alignment.class", "size": 1271, "crc": -1795372123}, {"key": "androidx/compose/ui/AtomicReference_jvmKt.class", "name": "androidx/compose/ui/AtomicReference_jvmKt.class", "size": 525, "crc": -1383490606}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "size": 4176, "crc": 791698594}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment.class", "size": 5927, "crc": -1173780345}, {"key": "androidx/compose/ui/BiasAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAlignment$Horizontal.class", "size": 4183, "crc": 2061614984}, {"key": "androidx/compose/ui/BiasAlignment$Vertical.class", "name": "androidx/compose/ui/BiasAlignment$Vertical.class", "size": 4051, "crc": -50870614}, {"key": "androidx/compose/ui/BiasAlignment.class", "name": "androidx/compose/ui/BiasAlignment.class", "size": 5432, "crc": 596488698}, {"key": "androidx/compose/ui/CombinedAlignment.class", "name": "androidx/compose/ui/CombinedAlignment.class", "size": 3397, "crc": -508517980}, {"key": "androidx/compose/ui/CombinedModifier$toString$1.class", "name": "androidx/compose/ui/CombinedModifier$toString$1.class", "size": 1877, "crc": -712990368}, {"key": "androidx/compose/ui/CombinedModifier.class", "name": "androidx/compose/ui/CombinedModifier.class", "size": 3913, "crc": -2010546305}, {"key": "androidx/compose/ui/ComposeUiFlags.class", "name": "androidx/compose/ui/ComposeUiFlags.class", "size": 1933, "crc": -43934605}, {"key": "androidx/compose/ui/ComposedModifier.class", "name": "androidx/compose/ui/ComposedModifier.class", "size": 2215, "crc": -2015065860}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "size": 1662, "crc": -334009161}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "size": 2934, "crc": -922891883}, {"key": "androidx/compose/ui/ComposedModifierKt.class", "name": "androidx/compose/ui/ComposedModifierKt.class", "size": 10167, "crc": -2029095563}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "size": 3190, "crc": -370682795}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "size": 1841, "crc": -1137958795}, {"key": "androidx/compose/ui/KeyedComposedModifier1.class", "name": "androidx/compose/ui/KeyedComposedModifier1.class", "size": 2726, "crc": -1515780371}, {"key": "androidx/compose/ui/KeyedComposedModifier2.class", "name": "androidx/compose/ui/KeyedComposedModifier2.class", "size": 3072, "crc": -696038836}, {"key": "androidx/compose/ui/KeyedComposedModifier3.class", "name": "androidx/compose/ui/KeyedComposedModifier3.class", "size": 3374, "crc": 1122542572}, {"key": "androidx/compose/ui/KeyedComposedModifierN.class", "name": "androidx/compose/ui/KeyedComposedModifierN.class", "size": 2823, "crc": -173562214}, {"key": "androidx/compose/ui/Modifier$Companion.class", "name": "androidx/compose/ui/Modifier$Companion.class", "size": 2535, "crc": -360724623}, {"key": "androidx/compose/ui/Modifier$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$DefaultImpls.class", "size": 825, "crc": -1768096576}, {"key": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "size": 2350, "crc": 833926543}, {"key": "androidx/compose/ui/Modifier$Element.class", "name": "androidx/compose/ui/Modifier$Element.class", "size": 3156, "crc": -698023058}, {"key": "androidx/compose/ui/Modifier$Node.class", "name": "androidx/compose/ui/Modifier$Node.class", "size": 12483, "crc": -2043253403}, {"key": "androidx/compose/ui/Modifier.class", "name": "androidx/compose/ui/Modifier.class", "size": 2547, "crc": -1378072631}, {"key": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "name": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "size": 1337, "crc": -908758272}, {"key": "androidx/compose/ui/Modifier_jvmKt.class", "name": "androidx/compose/ui/Modifier_jvmKt.class", "size": 779, "crc": -169203616}, {"key": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "name": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "size": 2974, "crc": -2059324089}, {"key": "androidx/compose/ui/MotionDurationScale$Key.class", "name": "androidx/compose/ui/MotionDurationScale$Key.class", "size": 1021, "crc": 251265595}, {"key": "androidx/compose/ui/MotionDurationScale.class", "name": "androidx/compose/ui/MotionDurationScale.class", "size": 1479, "crc": -2096112389}, {"key": "androidx/compose/ui/SensitiveContentKt.class", "name": "androidx/compose/ui/SensitiveContentKt.class", "size": 1179, "crc": -1518871941}, {"key": "androidx/compose/ui/SensitiveContentNode.class", "name": "androidx/compose/ui/SensitiveContentNode.class", "size": 4213, "crc": -466886553}, {"key": "androidx/compose/ui/SensitiveNodeElement.class", "name": "androidx/compose/ui/SensitiveNodeElement.class", "size": 3742, "crc": 614816688}, {"key": "androidx/compose/ui/SessionMutex$Session.class", "name": "androidx/compose/ui/SessionMutex$Session.class", "size": 1359, "crc": 63700726}, {"key": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "name": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "size": 5315, "crc": 1431429156}, {"key": "androidx/compose/ui/SessionMutex.class", "name": "androidx/compose/ui/SessionMutex.class", "size": 6650, "crc": -349457193}, {"key": "androidx/compose/ui/UiComposable.class", "name": "androidx/compose/ui/UiComposable.class", "size": 1018, "crc": 1638483244}, {"key": "androidx/compose/ui/ZIndexElement.class", "name": "androidx/compose/ui/ZIndexElement.class", "size": 3786, "crc": 2118620983}, {"key": "androidx/compose/ui/ZIndexModifierKt.class", "name": "androidx/compose/ui/ZIndexModifierKt.class", "size": 963, "crc": -1016912756}, {"key": "androidx/compose/ui/ZIndexNode$measure$1.class", "name": "androidx/compose/ui/ZIndexNode$measure$1.class", "size": 1867, "crc": 152451160}, {"key": "androidx/compose/ui/ZIndexNode.class", "name": "androidx/compose/ui/ZIndexNode.class", "size": 3002, "crc": 710821789}, {"key": "androidx/compose/ui/autofill/AndroidAutofill.class", "name": "androidx/compose/ui/autofill/AndroidAutofill.class", "size": 5968, "crc": 645763602}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager$onFocusChanged$2$1.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager$onFocusChanged$2$1.class", "size": 2262, "crc": -2143289450}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager$onSemanticsChanged$1.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager$onSemanticsChanged$1.class", "size": 2267, "crc": -713206838}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager$requestAutofill$1.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager$requestAutofill$1.class", "size": 2540, "crc": 1531366057}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager.class", "size": 17350, "crc": -1038817669}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager_androidKt.class", "size": 2136, "crc": 1450761600}, {"key": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "size": 5122, "crc": -1024211179}, {"key": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "size": 8896, "crc": 2120173327}, {"key": "androidx/compose/ui/autofill/AndroidContentDataType.class", "name": "androidx/compose/ui/autofill/AndroidContentDataType.class", "size": 2331, "crc": -1754925598}, {"key": "androidx/compose/ui/autofill/AndroidContentType.class", "name": "androidx/compose/ui/autofill/AndroidContentType.class", "size": 1825, "crc": 1233393318}, {"key": "androidx/compose/ui/autofill/Autofill.class", "name": "androidx/compose/ui/autofill/Autofill.class", "size": 1106, "crc": 863478646}, {"key": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "size": 8770, "crc": 694216499}, {"key": "androidx/compose/ui/autofill/AutofillApi27Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi27Helper.class", "size": 1471, "crc": -1635092360}, {"key": "androidx/compose/ui/autofill/AutofillApi28Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi28Helper.class", "size": 1254, "crc": -164912571}, {"key": "androidx/compose/ui/autofill/AutofillCallback.class", "name": "androidx/compose/ui/autofill/AutofillCallback.class", "size": 2750, "crc": -569059885}, {"key": "androidx/compose/ui/autofill/AutofillManager.class", "name": "androidx/compose/ui/autofill/AutofillManager.class", "size": 785, "crc": -1775779893}, {"key": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "name": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "size": 2375, "crc": -697046237}, {"key": "androidx/compose/ui/autofill/AutofillNode.class", "name": "androidx/compose/ui/autofill/AutofillNode.class", "size": 5672, "crc": -1375692325}, {"key": "androidx/compose/ui/autofill/AutofillSemanticCallback.class", "name": "androidx/compose/ui/autofill/AutofillSemanticCallback.class", "size": 3212, "crc": 1250812639}, {"key": "androidx/compose/ui/autofill/AutofillTree.class", "name": "androidx/compose/ui/autofill/AutofillTree.class", "size": 2591, "crc": 1332092572}, {"key": "androidx/compose/ui/autofill/AutofillType.class", "name": "androidx/compose/ui/autofill/AutofillType.class", "size": 4414, "crc": -63631277}, {"key": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "name": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "size": 1863, "crc": -1481734782}, {"key": "androidx/compose/ui/autofill/ContentDataType.class", "name": "androidx/compose/ui/autofill/ContentDataType.class", "size": 830, "crc": 137345289}, {"key": "androidx/compose/ui/autofill/ContentDataType_androidKt.class", "name": "androidx/compose/ui/autofill/ContentDataType_androidKt.class", "size": 1334, "crc": 1177047998}, {"key": "androidx/compose/ui/autofill/ContentType$Companion.class", "name": "androidx/compose/ui/autofill/ContentType$Companion.class", "size": 8287, "crc": 1791552495}, {"key": "androidx/compose/ui/autofill/ContentType.class", "name": "androidx/compose/ui/autofill/ContentType.class", "size": 1011, "crc": 1952381809}, {"key": "androidx/compose/ui/autofill/ContentType_androidKt.class", "name": "androidx/compose/ui/autofill/ContentType_androidKt.class", "size": 2556, "crc": 1631193339}, {"key": "androidx/compose/ui/autofill/PlatformAutofillManager.class", "name": "androidx/compose/ui/autofill/PlatformAutofillManager.class", "size": 1484, "crc": 1722669503}, {"key": "androidx/compose/ui/autofill/PlatformAutofillManagerImpl.class", "name": "androidx/compose/ui/autofill/PlatformAutofillManagerImpl.class", "size": 3312, "crc": 1954452809}, {"key": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt$populate$5.class", "name": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt$populate$5.class", "size": 2129, "crc": -974814550}, {"key": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt.class", "name": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt.class", "size": 16335, "crc": -9065434}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$Companion.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$Companion.class", "size": 1055, "crc": -1959122241}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "size": 2257, "crc": 1297707774}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "size": 9288, "crc": 1312170028}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$WhenMappings.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$WhenMappings.class", "size": 948, "crc": 2074558771}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "size": 1992, "crc": -985284464}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$sendContentCaptureAppearEvents$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$sendContentCaptureAppearEvents$1.class", "size": 2604, "crc": -1857124097}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$updateBuffersOnAppeared$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$updateBuffersOnAppeared$1.class", "size": 1952, "crc": -1884218713}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "size": 44513, "crc": -145537034}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureEvent.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureEvent.class", "size": 4335, "crc": 1476329547}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureEventType.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureEventType.class", "size": 1989, "crc": -100812254}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "size": 1266, "crc": 1169639759}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "size": 957, "crc": 797030347}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$modifier$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$modifier$1.class", "size": 2793, "crc": -1235732146}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$1$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$1$1.class", "size": 1514, "crc": -2000326758}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$dragAndDropSourceScope$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$dragAndDropSourceScope$1.class", "size": 2817, "crc": 1507768}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager.class", "size": 8659, "crc": 2045701459}, {"key": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "name": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "size": 6957, "crc": -1456283975}, {"key": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "name": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "size": 1130, "crc": -360254394}, {"key": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "name": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "size": 1438, "crc": 98619887}, {"key": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "size": 2033, "crc": 1002819962}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "size": 974, "crc": -641397362}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "size": 979, "crc": -1884651304}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "size": 4752, "crc": 646308095}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$drag$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$drag$1.class", "size": 2431, "crc": 1495314609}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "size": 2721, "crc": -2092975010}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "size": 4222, "crc": 37237380}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$startDragAndDropTransfer$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$startDragAndDropTransfer$1.class", "size": 4550, "crc": 1069223166}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "size": 15132, "crc": -2030365203}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "size": 2312, "crc": -1322740356}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropTargetModifierNode$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropTargetModifierNode$1.class", "size": 2336, "crc": 2093256549}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "size": 2682, "crc": 1096270313}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "size": 10994, "crc": 865385966}, {"key": "androidx/compose/ui/draganddrop/DragAndDropSourceModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropSourceModifierNode.class", "size": 905, "crc": 766940427}, {"key": "androidx/compose/ui/draganddrop/DragAndDropStartTransferScope.class", "name": "androidx/compose/ui/draganddrop/DragAndDropStartTransferScope.class", "size": 1289, "crc": -954529929}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "size": 1470, "crc": -1655335709}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTargetModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTargetModifierNode.class", "size": 606, "crc": -521690440}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "size": 1830, "crc": -2101123673}, {"key": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "size": 3650, "crc": -1629517687}, {"key": "androidx/compose/ui/draw/AlphaKt.class", "name": "androidx/compose/ui/draw/AlphaKt.class", "size": 1141, "crc": 1383934435}, {"key": "androidx/compose/ui/draw/BlurKt$blur$1.class", "name": "androidx/compose/ui/draw/BlurKt$blur$1.class", "size": 2516, "crc": 1274147511}, {"key": "androidx/compose/ui/draw/BlurKt.class", "name": "androidx/compose/ui/draw/BlurKt.class", "size": 3876, "crc": 217181360}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "size": 1420, "crc": 887039352}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "size": 3594, "crc": -1528356215}, {"key": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "size": 963, "crc": -1851035617}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "size": 624, "crc": 2140724883}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "size": 1372, "crc": 2013298193}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "size": 1665, "crc": -2125167912}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "size": 9485, "crc": 1183809049}, {"key": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "size": 1843, "crc": 1691648645}, {"key": "androidx/compose/ui/draw/CacheDrawScope$record$1$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$record$1$1.class", "size": 3647, "crc": -1856547322}, {"key": "androidx/compose/ui/draw/CacheDrawScope.class", "name": "androidx/compose/ui/draw/CacheDrawScope.class", "size": 8459, "crc": 752473572}, {"key": "androidx/compose/ui/draw/ClipKt.class", "name": "androidx/compose/ui/draw/ClipKt.class", "size": 1371, "crc": 1218587630}, {"key": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "name": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "size": 2444, "crc": -1084123497}, {"key": "androidx/compose/ui/draw/DrawBehindElement.class", "name": "androidx/compose/ui/draw/DrawBehindElement.class", "size": 4728, "crc": 1015779166}, {"key": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "size": 2458, "crc": -1175559043}, {"key": "androidx/compose/ui/draw/DrawCacheModifier.class", "name": "androidx/compose/ui/draw/DrawCacheModifier.class", "size": 2214, "crc": 503135405}, {"key": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "size": 2413, "crc": -956560061}, {"key": "androidx/compose/ui/draw/DrawModifier.class", "name": "androidx/compose/ui/draw/DrawModifier.class", "size": 2133, "crc": 790599676}, {"key": "androidx/compose/ui/draw/DrawModifierKt.class", "name": "androidx/compose/ui/draw/DrawModifierKt.class", "size": 3182, "crc": -1782429055}, {"key": "androidx/compose/ui/draw/DrawResult.class", "name": "androidx/compose/ui/draw/DrawResult.class", "size": 1777, "crc": 1012408754}, {"key": "androidx/compose/ui/draw/DrawWithCacheElement.class", "name": "androidx/compose/ui/draw/DrawWithCacheElement.class", "size": 5038, "crc": 53712635}, {"key": "androidx/compose/ui/draw/DrawWithContentElement.class", "name": "androidx/compose/ui/draw/DrawWithContentElement.class", "size": 4808, "crc": -124553539}, {"key": "androidx/compose/ui/draw/DrawWithContentModifier.class", "name": "androidx/compose/ui/draw/DrawWithContentModifier.class", "size": 2147, "crc": 960069146}, {"key": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "size": 1988, "crc": -1677865297}, {"key": "androidx/compose/ui/draw/PainterElement.class", "name": "androidx/compose/ui/draw/PainterElement.class", "size": 8190, "crc": -810084873}, {"key": "androidx/compose/ui/draw/PainterModifierKt.class", "name": "androidx/compose/ui/draw/PainterModifierKt.class", "size": 2722, "crc": 740714485}, {"key": "androidx/compose/ui/draw/PainterNode$measure$1.class", "name": "androidx/compose/ui/draw/PainterNode$measure$1.class", "size": 1821, "crc": 1127274309}, {"key": "androidx/compose/ui/draw/PainterNode.class", "name": "androidx/compose/ui/draw/PainterNode.class", "size": 21724, "crc": 821258003}, {"key": "androidx/compose/ui/draw/RotateKt.class", "name": "androidx/compose/ui/draw/RotateKt.class", "size": 1160, "crc": -1507776618}, {"key": "androidx/compose/ui/draw/ScaleKt.class", "name": "androidx/compose/ui/draw/ScaleKt.class", "size": 1420, "crc": -1192488125}, {"key": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "name": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "size": 4481, "crc": 891600742}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "size": 2098, "crc": 1320097584}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "size": 7581, "crc": -1308776269}, {"key": "androidx/compose/ui/draw/ShadowKt.class", "name": "androidx/compose/ui/draw/ShadowKt.class", "size": 3989, "crc": 1900168928}, {"key": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "size": 9859, "crc": -1429160720}, {"key": "androidx/compose/ui/focus/CancelIndicatingFocusBoundaryScope.class", "name": "androidx/compose/ui/focus/CancelIndicatingFocusBoundaryScope.class", "size": 1645, "crc": -990090827}, {"key": "androidx/compose/ui/focus/CustomDestinationResult.class", "name": "androidx/compose/ui/focus/CustomDestinationResult.class", "size": 2030, "crc": -1782743759}, {"key": "androidx/compose/ui/focus/FocusChangedElement.class", "name": "androidx/compose/ui/focus/FocusChangedElement.class", "size": 4623, "crc": -1764921083}, {"key": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "name": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "size": 1323, "crc": -434673984}, {"key": "androidx/compose/ui/focus/FocusChangedNode.class", "name": "androidx/compose/ui/focus/FocusChangedNode.class", "size": 2287, "crc": 1804392577}, {"key": "androidx/compose/ui/focus/FocusDirection$Companion.class", "name": "androidx/compose/ui/focus/FocusDirection$Companion.class", "size": 2142, "crc": -435063748}, {"key": "androidx/compose/ui/focus/FocusDirection.class", "name": "androidx/compose/ui/focus/FocusDirection.class", "size": 3318, "crc": -1186803801}, {"key": "androidx/compose/ui/focus/FocusEnterExitScope.class", "name": "androidx/compose/ui/focus/FocusEnterExitScope.class", "size": 1147, "crc": 1479055202}, {"key": "androidx/compose/ui/focus/FocusEventElement.class", "name": "androidx/compose/ui/focus/FocusEventElement.class", "size": 4589, "crc": -1620272440}, {"key": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "size": 2482, "crc": 195654683}, {"key": "androidx/compose/ui/focus/FocusEventModifier.class", "name": "androidx/compose/ui/focus/FocusEventModifier.class", "size": 2271, "crc": -1991335400}, {"key": "androidx/compose/ui/focus/FocusEventModifierKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierKt.class", "size": 1313, "crc": 16079247}, {"key": "androidx/compose/ui/focus/FocusEventModifierNode.class", "name": "androidx/compose/ui/focus/FocusEventModifierNode.class", "size": 765, "crc": -82480202}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "size": 943, "crc": -456880538}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "size": 9580, "crc": 1181597428}, {"key": "androidx/compose/ui/focus/FocusEventNode.class", "name": "androidx/compose/ui/focus/FocusEventNode.class", "size": 2019, "crc": -1695280699}, {"key": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "name": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "size": 1114, "crc": -69118599}, {"key": "androidx/compose/ui/focus/FocusInteropUtils.class", "name": "androidx/compose/ui/focus/FocusInteropUtils.class", "size": 1071, "crc": -1389814750}, {"key": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "name": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "size": 4630, "crc": -346381287}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidationLegacy$1.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidationLegacy$1.class", "size": 1410, "crc": 1071952344}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager$setUpOnRequestApplyChangesListener$1.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager$setUpOnRequestApplyChangesListener$1.class", "size": 1394, "crc": 1009215783}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager.class", "size": 28705, "crc": 666273566}, {"key": "androidx/compose/ui/focus/FocusListener.class", "name": "androidx/compose/ui/focus/FocusListener.class", "size": 756, "crc": 2124632110}, {"key": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "size": 523, "crc": 1838648718}, {"key": "androidx/compose/ui/focus/FocusManager.class", "name": "androidx/compose/ui/focus/FocusManager.class", "size": 1139, "crc": -770124410}, {"key": "androidx/compose/ui/focus/FocusModifierKt.class", "name": "androidx/compose/ui/focus/FocusModifierKt.class", "size": 1397, "crc": 2009169669}, {"key": "androidx/compose/ui/focus/FocusOrder.class", "name": "androidx/compose/ui/focus/FocusOrder.class", "size": 3606, "crc": 157526230}, {"key": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "size": 2482, "crc": 1920475531}, {"key": "androidx/compose/ui/focus/FocusOrderModifier.class", "name": "androidx/compose/ui/focus/FocusOrderModifier.class", "size": 2281, "crc": -609118802}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "size": 1660, "crc": 807279063}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "size": 1702, "crc": -1789589}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "size": 3304, "crc": 1573409087}, {"key": "androidx/compose/ui/focus/FocusOrderToProperties.class", "name": "androidx/compose/ui/focus/FocusOrderToProperties.class", "size": 2105, "crc": -1694292940}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "size": 1302, "crc": -1085360575}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchRotaryEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchRotaryEvent$1.class", "size": 1332, "crc": -746875785}, {"key": "androidx/compose/ui/focus/FocusOwner.class", "name": "androidx/compose/ui/focus/FocusOwner.class", "size": 5860, "crc": -451082988}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "size": 948, "crc": 499085082}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$clearFocus$clearedFocusSuccessfully$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$clearFocus$clearedFocusSuccessfully$1.class", "size": 1172, "crc": -2092602809}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$1.class", "size": 1515, "crc": 209807438}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$2.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$2.class", "size": 1209, "crc": 492103291}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$3.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$3.class", "size": 1490, "crc": 1564711300}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "size": 2607, "crc": -1463699623}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "size": 2698, "crc": -642752647}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "size": 1970, "crc": 2116401762}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "size": 1495, "crc": -1021531340}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl.class", "size": 79955, "crc": -755872383}, {"key": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "name": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "size": 1218, "crc": -1251703882}, {"key": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "name": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "size": 1755, "crc": -1506739346}, {"key": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "name": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "size": 1752, "crc": -741781743}, {"key": "androidx/compose/ui/focus/FocusProperties$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusProperties$onEnter$1.class", "size": 1432, "crc": 2131190883}, {"key": "androidx/compose/ui/focus/FocusProperties$onExit$1.class", "name": "androidx/compose/ui/focus/FocusProperties$onExit$1.class", "size": 1429, "crc": 1851323587}, {"key": "androidx/compose/ui/focus/FocusProperties.class", "name": "androidx/compose/ui/focus/FocusProperties.class", "size": 5997, "crc": 1440177239}, {"key": "androidx/compose/ui/focus/FocusPropertiesElement.class", "name": "androidx/compose/ui/focus/FocusPropertiesElement.class", "size": 4169, "crc": -1623389678}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$onEnter$1.class", "size": 1389, "crc": 89674836}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$onExit$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$onExit$1.class", "size": 1387, "crc": 880187029}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "size": 5260, "crc": -1990102813}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "size": 1842, "crc": 956070165}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt$toUsingEnterExitScope$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt$toUsingEnterExitScope$1.class", "size": 2621, "crc": 515242995}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt.class", "size": 2514, "crc": 944189354}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "size": 803, "crc": -78266114}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "size": 1146, "crc": 505291229}, {"key": "androidx/compose/ui/focus/FocusPropertiesNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesNode.class", "size": 1772, "crc": 43947326}, {"key": "androidx/compose/ui/focus/FocusPropertiesScope.class", "name": "androidx/compose/ui/focus/FocusPropertiesScope.class", "size": 677, "crc": -812076643}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "size": 2820, "crc": 1896400852}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion.class", "size": 1880, "crc": 937241957}, {"key": "androidx/compose/ui/focus/FocusRequester$requestFocus$1.class", "name": "androidx/compose/ui/focus/FocusRequester$requestFocus$1.class", "size": 1434, "crc": -2137080813}, {"key": "androidx/compose/ui/focus/FocusRequester.class", "name": "androidx/compose/ui/focus/FocusRequester.class", "size": 20782, "crc": -656421033}, {"key": "androidx/compose/ui/focus/FocusRequesterElement.class", "name": "androidx/compose/ui/focus/FocusRequesterElement.class", "size": 5382, "crc": 1510205132}, {"key": "androidx/compose/ui/focus/FocusRequesterKt.class", "name": "androidx/compose/ui/focus/FocusRequesterKt.class", "size": 1152, "crc": -116301597}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "size": 2522, "crc": 221312650}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier.class", "size": 2304, "crc": -1900702714}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "size": 1097, "crc": -1749503550}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "size": 519, "crc": -1275018789}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "size": 1745, "crc": 526068723}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "size": 30572, "crc": 1501231654}, {"key": "androidx/compose/ui/focus/FocusRequesterNode.class", "name": "androidx/compose/ui/focus/FocusRequesterNode.class", "size": 2871, "crc": 1224546}, {"key": "androidx/compose/ui/focus/FocusRestorerElement.class", "name": "androidx/compose/ui/focus/FocusRestorerElement.class", "size": 4134, "crc": 2043989950}, {"key": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "name": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "size": 1313, "crc": 969749034}, {"key": "androidx/compose/ui/focus/FocusRestorerKt.class", "name": "androidx/compose/ui/focus/FocusRestorerKt.class", "size": 15337, "crc": 1789602848}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "size": 2862, "crc": 16238778}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "size": 2400, "crc": 1850701775}, {"key": "androidx/compose/ui/focus/FocusRestorerNode.class", "name": "androidx/compose/ui/focus/FocusRestorerNode.class", "size": 3850, "crc": 522075625}, {"key": "androidx/compose/ui/focus/FocusState.class", "name": "androidx/compose/ui/focus/FocusState.class", "size": 554, "crc": 1285893758}, {"key": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "size": 865, "crc": -2113613411}, {"key": "androidx/compose/ui/focus/FocusStateImpl.class", "name": "androidx/compose/ui/focus/FocusStateImpl.class", "size": 2892, "crc": -848185604}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "size": 2167, "crc": -1929493385}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNodeKt$FocusTargetModifierNode$1.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNodeKt$FocusTargetModifierNode$1.class", "size": 1682, "crc": 1711049008}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "size": 2817, "crc": 907543220}, {"key": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "name": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "size": 2832, "crc": 1834649149}, {"key": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "size": 1351, "crc": -1919304693}, {"key": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "name": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "size": 1795, "crc": -822033198}, {"key": "androidx/compose/ui/focus/FocusTargetNode$requestFocus$1$1.class", "name": "androidx/compose/ui/focus/FocusTargetNode$requestFocus$1$1.class", "size": 1474, "crc": 1700402819}, {"key": "androidx/compose/ui/focus/FocusTargetNode.class", "name": "androidx/compose/ui/focus/FocusTargetNode.class", "size": 44231, "crc": -917277053}, {"key": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "size": 2438, "crc": -290352918}, {"key": "androidx/compose/ui/focus/FocusTransactionManager.class", "name": "androidx/compose/ui/focus/FocusTransactionManager.class", "size": 10965, "crc": 2066323637}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "size": 928, "crc": -2120525599}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "size": 1401, "crc": 1813698715}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt.class", "size": 40266, "crc": 1197658051}, {"key": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "size": 1211, "crc": 1135174489}, {"key": "androidx/compose/ui/focus/FocusTraversalKt.class", "name": "androidx/compose/ui/focus/FocusTraversalKt.class", "size": 22924, "crc": -1043630054}, {"key": "androidx/compose/ui/focus/Focusability$Companion.class", "name": "androidx/compose/ui/focus/Focusability$Companion.class", "size": 1396, "crc": 1143578846}, {"key": "androidx/compose/ui/focus/Focusability.class", "name": "androidx/compose/ui/focus/Focusability.class", "size": 4589, "crc": -959708395}, {"key": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "name": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "size": 5148, "crc": 740345917}, {"key": "androidx/compose/ui/focus/InvalidateSemantics.class", "name": "androidx/compose/ui/focus/InvalidateSemantics.class", "size": 1497, "crc": 601324530}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "size": 952, "crc": -311315711}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 3903, "crc": 1737312963}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "size": 30112, "crc": -647138152}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "size": 952, "crc": 371064032}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 3917, "crc": 1644978416}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "size": 27122, "crc": 100575343}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "size": 4897, "crc": -361459032}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "size": 2162, "crc": 1659197796}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "size": 5423, "crc": 262878746}, {"key": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "name": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "size": 1451, "crc": 2048569201}, {"key": "androidx/compose/ui/graphics/CompositingStrategy.class", "name": "androidx/compose/ui/graphics/CompositingStrategy.class", "size": 2871, "crc": -1485671866}, {"key": "androidx/compose/ui/graphics/Fields.class", "name": "androidx/compose/ui/graphics/Fields.class", "size": 1850, "crc": 535799281}, {"key": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "name": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "size": 1749, "crc": 1022456299}, {"key": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "size": 13322, "crc": 191510549}, {"key": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "size": 9921, "crc": -1092124968}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "size": 5595, "crc": 1342576956}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "size": 8274, "crc": 354304637}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "size": 5066, "crc": 455588758}, {"key": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "size": 11331, "crc": -2130771676}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "size": 3136, "crc": 1816006166}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "size": 2234, "crc": -1638207345}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "size": 12558, "crc": 987404798}, {"key": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "name": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "size": 1089, "crc": -1212373782}, {"key": "androidx/compose/ui/graphics/TransformOrigin.class", "name": "androidx/compose/ui/graphics/TransformOrigin.class", "size": 5133, "crc": 2044565534}, {"key": "androidx/compose/ui/graphics/TransformOriginKt.class", "name": "androidx/compose/ui/graphics/TransformOriginKt.class", "size": 1641, "crc": -1516748757}, {"key": "androidx/compose/ui/graphics/vector/DrawCache.class", "name": "androidx/compose/ui/graphics/vector/DrawCache.class", "size": 10059, "crc": 1044050435}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "size": 1791, "crc": -1783808206}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent.class", "size": 15455, "crc": 248709523}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "size": 5291, "crc": -1847343928}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "size": 12088, "crc": -1974952949}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "size": 2358, "crc": 446912403}, {"key": "androidx/compose/ui/graphics/vector/ImageVector.class", "name": "androidx/compose/ui/graphics/vector/ImageVector.class", "size": 6510, "crc": -1577806927}, {"key": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "name": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "size": 8362, "crc": -198781949}, {"key": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "name": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "size": 1269, "crc": -154544870}, {"key": "androidx/compose/ui/graphics/vector/PathComponent.class", "name": "androidx/compose/ui/graphics/vector/PathComponent.class", "size": 10563, "crc": -1851622467}, {"key": "androidx/compose/ui/graphics/vector/VNode.class", "name": "androidx/compose/ui/graphics/vector/VNode.class", "size": 2433, "crc": 1847489415}, {"key": "androidx/compose/ui/graphics/vector/VectorApplier.class", "name": "androidx/compose/ui/graphics/vector/VectorApplier.class", "size": 3514, "crc": 1179105134}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "size": 1557, "crc": -1313356348}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "size": 4815, "crc": -971761760}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "size": 1190, "crc": -1029309324}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent.class", "size": 13708, "crc": -1794634947}, {"key": "androidx/compose/ui/graphics/vector/VectorComposable.class", "name": "androidx/compose/ui/graphics/vector/VectorComposable.class", "size": 1066, "crc": -674600627}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1.class", "size": 1407, "crc": -344272842}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "size": 1754, "crc": -1389476214}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "size": 1738, "crc": -1120271853}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "size": 1736, "crc": 380208309}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "size": 1736, "crc": 2065146696}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "size": 1736, "crc": -2131125119}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "size": 1736, "crc": -590351334}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "size": 1742, "crc": -1789734107}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "size": 1742, "crc": 760467197}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "size": 1994, "crc": -391117632}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "size": 2653, "crc": -1921861558}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1.class", "size": 1452, "crc": -2024287016}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "size": 1799, "crc": -2104556420}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "size": 1902, "crc": -1538159061}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "size": 1792, "crc": -1128062794}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "size": 1790, "crc": 1160520596}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "size": 1788, "crc": 2028570399}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "size": 1791, "crc": 930803612}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "size": 2034, "crc": -348714599}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "size": 1908, "crc": 1394914989}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "size": 1891, "crc": 1000549974}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "size": 1784, "crc": -703350713}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "size": 1893, "crc": 1032920209}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "size": 1786, "crc": -1143003706}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "size": 1790, "crc": 2065489854}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "size": 1904, "crc": 244993259}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "size": 2903, "crc": -238006846}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "size": 15471, "crc": 16935131}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "size": 1179, "crc": 190408459}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig.class", "size": 1410, "crc": 1062506055}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "size": 2206, "crc": 156506586}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup.class", "size": 6086, "crc": -988794538}, {"key": "androidx/compose/ui/graphics/vector/VectorKt.class", "name": "androidx/compose/ui/graphics/vector/VectorKt.class", "size": 6319, "crc": -507324264}, {"key": "androidx/compose/ui/graphics/vector/VectorNode.class", "name": "androidx/compose/ui/graphics/vector/VectorNode.class", "size": 1025, "crc": 595898807}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "size": 1552, "crc": 1819381620}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter.class", "size": 12286, "crc": -1074472128}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "size": 2877, "crc": -818583018}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "size": 2110, "crc": 1988124810}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "size": 945, "crc": -1762245930}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "size": 945, "crc": -1100097034}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1$invoke$$inlined$onDispose$1.class", "size": 2216, "crc": 420356987}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "size": 2900, "crc": 1277616775}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$composition$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$composition$1$1.class", "size": 4793, "crc": 399483159}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "size": 32243, "crc": 635829620}, {"key": "androidx/compose/ui/graphics/vector/VectorPath.class", "name": "androidx/compose/ui/graphics/vector/VectorPath.class", "size": 7070, "crc": 735269470}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "size": 1177, "crc": 1536068133}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "size": 1135, "crc": 1268970253}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "size": 1242, "crc": 730241841}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "size": 1126, "crc": 1930990973}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "size": 1126, "crc": 1880416210}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "size": 1132, "crc": -711349706}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "size": 1126, "crc": -613864138}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "size": 1126, "crc": -664442983}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "size": 1183, "crc": -2093985375}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "size": 1141, "crc": 8278525}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "size": 1153, "crc": -649702271}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "size": 1138, "crc": 1860652591}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "size": 1138, "crc": 1889094661}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "size": 1141, "crc": 1677912982}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "size": 1150, "crc": 1325996326}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "size": 1147, "crc": -563494771}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty.class", "size": 3632, "crc": 1927003527}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "size": 9232, "crc": 558122446}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "size": 9164, "crc": -164471109}, {"key": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "name": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "size": 18959, "crc": -25463498}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "size": 633, "crc": 1018064070}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "size": 3658, "crc": -953874756}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "size": 3286, "crc": 1765997153}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "size": 2591, "crc": -1354425540}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "size": 3062, "crc": -1937042390}, {"key": "androidx/compose/ui/input/InputMode$Companion.class", "name": "androidx/compose/ui/input/InputMode$Companion.class", "size": 1215, "crc": -418449423}, {"key": "androidx/compose/ui/input/InputMode.class", "name": "androidx/compose/ui/input/InputMode.class", "size": 2551, "crc": -75241549}, {"key": "androidx/compose/ui/input/InputModeManager.class", "name": "androidx/compose/ui/input/InputModeManager.class", "size": 663, "crc": -1307030400}, {"key": "androidx/compose/ui/input/InputModeManagerImpl.class", "name": "androidx/compose/ui/input/InputModeManagerImpl.class", "size": 3998, "crc": -242221664}, {"key": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "name": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "size": 3373, "crc": 870085236}, {"key": "androidx/compose/ui/input/key/Key$Companion.class", "name": "androidx/compose/ui/input/key/Key$Companion.class", "size": 49321, "crc": 187219006}, {"key": "androidx/compose/ui/input/key/Key.class", "name": "androidx/compose/ui/input/key/Key.class", "size": 33732, "crc": -27679473}, {"key": "androidx/compose/ui/input/key/KeyEvent.class", "name": "androidx/compose/ui/input/key/KeyEvent.class", "size": 2729, "crc": -1411805367}, {"key": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "name": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "size": 1393, "crc": 166561541}, {"key": "androidx/compose/ui/input/key/KeyEventType.class", "name": "androidx/compose/ui/input/key/KeyEventType.class", "size": 2705, "crc": 1618412446}, {"key": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "name": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "size": 2531, "crc": 1795948031}, {"key": "androidx/compose/ui/input/key/KeyInputElement.class", "name": "androidx/compose/ui/input/key/KeyInputElement.class", "size": 4351, "crc": -618238764}, {"key": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "name": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "size": 1560, "crc": -343622424}, {"key": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "name": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "size": 893, "crc": 2014572600}, {"key": "androidx/compose/ui/input/key/KeyInputNode.class", "name": "androidx/compose/ui/input/key/KeyInputNode.class", "size": 3211, "crc": -787670273}, {"key": "androidx/compose/ui/input/key/Key_androidKt.class", "name": "androidx/compose/ui/input/key/Key_androidKt.class", "size": 1678, "crc": -340428702}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "size": 6151, "crc": -1275839146}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "size": 1029, "crc": 163680815}, {"key": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "name": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "size": 1744, "crc": -1369873524}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "size": 2319, "crc": 334904328}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "size": 4081, "crc": 481174493}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "size": 1386, "crc": -1396946256}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "size": 1895, "crc": 1591546780}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "size": 1881, "crc": 975968134}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "size": 7584, "crc": 1692653412}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "size": 3976, "crc": -969490731}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "size": 1809, "crc": -1695343058}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "size": 1939, "crc": 143755572}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "size": 1904, "crc": -1828453818}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "size": 1475, "crc": -1163196917}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "size": 10280, "crc": -236414561}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt$findNearestAttachedAncestor$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt$findNearestAttachedAncestor$1.class", "size": 2126, "crc": 1906246847}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "size": 2522, "crc": -422905109}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "size": 2888, "crc": -1247295802}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "size": 3140, "crc": 178594671}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "size": 2445, "crc": -246489263}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "size": 2253, "crc": 630253120}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "size": 5122, "crc": 549129334}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "size": 7510, "crc": 1390316315}, {"key": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "size": 1398, "crc": 1195663387}, {"key": "androidx/compose/ui/input/pointer/ConsumedData.class", "name": "androidx/compose/ui/input/pointer/ConsumedData.class", "size": 3043, "crc": 1423776718}, {"key": "androidx/compose/ui/input/pointer/HistoricalChange.class", "name": "androidx/compose/ui/input/pointer/HistoricalChange.class", "size": 2675, "crc": -1115842123}, {"key": "androidx/compose/ui/input/pointer/HitPathTracker$addHitPath$1.class", "name": "androidx/compose/ui/input/pointer/HitPathTracker$addHitPath$1.class", "size": 1526, "crc": 1553503849}, {"key": "androidx/compose/ui/input/pointer/HitPathTracker.class", "name": "androidx/compose/ui/input/pointer/HitPathTracker.class", "size": 12116, "crc": 2143446212}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "size": 2242, "crc": 565856434}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "size": 2375, "crc": -2060551413}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "size": 2700, "crc": -605847331}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findOverridingAncestorNode$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findOverridingAncestorNode$1.class", "size": 2155, "crc": 1021432961}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode.class", "size": 11441, "crc": 336343787}, {"key": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "name": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "size": 4822, "crc": 2125402515}, {"key": "androidx/compose/ui/input/pointer/MatrixPositionCalculator.class", "name": "androidx/compose/ui/input/pointer/MatrixPositionCalculator.class", "size": 832, "crc": -1880274537}, {"key": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "name": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "size": 11533, "crc": -1680928261}, {"key": "androidx/compose/ui/input/pointer/MotionEventClassification.class", "name": "androidx/compose/ui/input/pointer/MotionEventClassification.class", "size": 651, "crc": -444158854}, {"key": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "name": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "size": 2573, "crc": 1170415675}, {"key": "androidx/compose/ui/input/pointer/Node.class", "name": "androidx/compose/ui/input/pointer/Node.class", "size": 25887, "crc": -1220020757}, {"key": "androidx/compose/ui/input/pointer/NodeParent.class", "name": "androidx/compose/ui/input/pointer/NodeParent.class", "size": 8602, "crc": -1293950283}, {"key": "androidx/compose/ui/input/pointer/PointerButtons.class", "name": "androidx/compose/ui/input/pointer/PointerButtons.class", "size": 2166, "crc": -1689217287}, {"key": "androidx/compose/ui/input/pointer/PointerEvent.class", "name": "androidx/compose/ui/input/pointer/PointerEvent.class", "size": 8823, "crc": 700823861}, {"key": "androidx/compose/ui/input/pointer/PointerEventKt.class", "name": "androidx/compose/ui/input/pointer/PointerEventKt.class", "size": 10002, "crc": -68866856}, {"key": "androidx/compose/ui/input/pointer/PointerEventPass.class", "name": "androidx/compose/ui/input/pointer/PointerEventPass.class", "size": 1954, "crc": -933551705}, {"key": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "size": 1679, "crc": 1992101932}, {"key": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "size": 2046, "crc": -271550685}, {"key": "androidx/compose/ui/input/pointer/PointerEventType.class", "name": "androidx/compose/ui/input/pointer/PointerEventType.class", "size": 3206, "crc": -1117958037}, {"key": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "size": 4578, "crc": 1489647582}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "size": 5307, "crc": -830824505}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "size": 2930, "crc": 475907203}, {"key": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "size": 1760, "crc": 1029621019}, {"key": "androidx/compose/ui/input/pointer/PointerIcon.class", "name": "androidx/compose/ui/input/pointer/PointerIcon.class", "size": 800, "crc": 895366475}, {"key": "androidx/compose/ui/input/pointer/PointerIconKt.class", "name": "androidx/compose/ui/input/pointer/PointerIconKt.class", "size": 2540, "crc": -1029434754}, {"key": "androidx/compose/ui/input/pointer/PointerIconService.class", "name": "androidx/compose/ui/input/pointer/PointerIconService.class", "size": 967, "crc": 1816456654}, {"key": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "size": 2048, "crc": -457431142}, {"key": "androidx/compose/ui/input/pointer/PointerId.class", "name": "androidx/compose/ui/input/pointer/PointerId.class", "size": 2156, "crc": -169989562}, {"key": "androidx/compose/ui/input/pointer/PointerInputChange.class", "name": "androidx/compose/ui/input/pointer/PointerInputChange.class", "size": 19285, "crc": -297718113}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "size": 1597, "crc": -1453949202}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "size": 5078, "crc": 405741163}, {"key": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "name": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "size": 1957, "crc": -2093895201}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "size": 8572, "crc": -137069788}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventHandler.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventHandler.class", "size": 996, "crc": -1361267320}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "size": 5956, "crc": -1702653007}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "size": 1821, "crc": -1974302702}, {"key": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "size": 2980, "crc": 481325336}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "size": 2566, "crc": -1944095803}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "size": 2261, "crc": 964311042}, {"key": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "name": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "size": 1382, "crc": -483381539}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "size": 4346, "crc": -273637634}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope.class", "size": 5527, "crc": 1111043712}, {"key": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "size": 13308, "crc": -1888242164}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "size": 2267, "crc": -730369370}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "size": 1763, "crc": 892557261}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$3.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$3.class", "size": 2600, "crc": -1213753431}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "size": 1697, "crc": 1994589384}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "size": 9059, "crc": -444425457}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "size": 3733, "crc": -548099675}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "size": 4420, "crc": -841019342}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "size": 2372, "crc": -2059533480}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "size": 3370, "crc": -406133116}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "size": 5349, "crc": 1887003540}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "size": 1968, "crc": 621139928}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "size": 5883, "crc": -733930133}, {"key": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "size": 5851, "crc": -517178035}, {"key": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "name": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "size": 2216, "crc": -1292910270}, {"key": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "size": 1719, "crc": -1189044098}, {"key": "androidx/compose/ui/input/pointer/PointerType.class", "name": "androidx/compose/ui/input/pointer/PointerType.class", "size": 2914, "crc": 778971695}, {"key": "androidx/compose/ui/input/pointer/PositionCalculator.class", "name": "androidx/compose/ui/input/pointer/PositionCalculator.class", "size": 709, "crc": -1317671084}, {"key": "androidx/compose/ui/input/pointer/ProcessResult.class", "name": "androidx/compose/ui/input/pointer/ProcessResult.class", "size": 2630, "crc": 240772403}, {"key": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "name": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "size": 2144, "crc": 709364993}, {"key": "androidx/compose/ui/input/pointer/StylusHoverIconModifierElement.class", "name": "androidx/compose/ui/input/pointer/StylusHoverIconModifierElement.class", "size": 6286, "crc": -300701481}, {"key": "androidx/compose/ui/input/pointer/StylusHoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/StylusHoverIconModifierNode.class", "size": 3016, "crc": -2129178978}, {"key": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "name": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "size": 5132, "crc": 1017714610}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt$sam$androidx_compose_ui_input_pointer_PointerInputEventHandler$0.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt$sam$androidx_compose_ui_input_pointer_PointerInputEventHandler$0.class", "size": 2305, "crc": -103191834}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "size": 7325, "crc": -976477009}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "size": 833, "crc": -36507166}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "size": 3176, "crc": 778003204}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$1.class", "size": 1514, "crc": 1256637550}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "size": 2566, "crc": 463223745}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "size": 4875, "crc": -1067468801}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "size": 2561, "crc": 550214930}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "size": 16525, "crc": 111556481}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "size": 965, "crc": -952126488}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "size": 2149, "crc": -2096605446}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "size": 5097, "crc": 1040621913}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$pointerInputHandler$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$pointerInputHandler$1.class", "size": 3189, "crc": 1879184009}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "size": 27641, "crc": 261771209}, {"key": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "name": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "size": 2950, "crc": -1411728531}, {"key": "androidx/compose/ui/input/pointer/util/ExperimentalVelocityTrackerApi.class", "name": "androidx/compose/ui/input/pointer/util/ExperimentalVelocityTrackerApi.class", "size": 878, "crc": 1554543820}, {"key": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "name": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "size": 4044, "crc": 300152879}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "size": 6175, "crc": 1320851835}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "size": 2107, "crc": -33200971}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "size": 939, "crc": 1817828408}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "size": 6645, "crc": -312084984}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "size": 12174, "crc": 2128694530}, {"key": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "name": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "size": 6069, "crc": -1698429946}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "size": 1663, "crc": -806141431}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "size": 880, "crc": 2025346744}, {"key": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "size": 3100, "crc": 180270589}, {"key": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "name": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "size": 3651, "crc": -1449833031}, {"key": "androidx/compose/ui/internal/InlineClassHelperKt.class", "name": "androidx/compose/ui/internal/InlineClassHelperKt.class", "size": 3865, "crc": -742672314}, {"key": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 550, "crc": -1728843251}, {"key": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "name": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "size": 871, "crc": 520133255}, {"key": "androidx/compose/ui/layout/AlignmentLine.class", "name": "androidx/compose/ui/layout/AlignmentLine.class", "size": 2093, "crc": 18460806}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "size": 1476, "crc": -325422954}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "size": 1474, "crc": -2045225678}, {"key": "androidx/compose/ui/layout/AlignmentLineKt.class", "name": "androidx/compose/ui/layout/AlignmentLineKt.class", "size": 2076, "crc": 1855896117}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "size": 983, "crc": -181563052}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "size": 2619, "crc": 559008961}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "size": 8799, "crc": -2120052396}, {"key": "androidx/compose/ui/layout/ApproachLayoutElement.class", "name": "androidx/compose/ui/layout/ApproachLayoutElement.class", "size": 9113, "crc": 722487817}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "size": 1798, "crc": -621116328}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "size": 1795, "crc": 180139875}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "size": 1890, "crc": 1189721777}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "size": 1798, "crc": 840655602}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "size": 1795, "crc": -1406638093}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "size": 6950, "crc": 698817810}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "size": 6733, "crc": -1578008487}, {"key": "androidx/compose/ui/layout/ApproachMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachMeasureScope.class", "size": 775, "crc": -2137433811}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "size": 3731, "crc": -509675753}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "size": 13803, "crc": -1348675394}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "size": 639, "crc": -1718548816}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "size": 2009, "crc": -695118986}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "size": 3265, "crc": 608237997}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "size": 1390, "crc": 156975569}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "size": 1243, "crc": -629903851}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "size": 1530, "crc": -1210387228}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda-1$1.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda-1$1.class", "size": 2195, "crc": 1711985082}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "size": 1526, "crc": -1237356803}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "size": 2673, "crc": 12116829}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "size": 3815, "crc": 228730153}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "size": 3514, "crc": -313377814}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "size": 3503, "crc": 431493726}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "size": 2666, "crc": -1827045957}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "size": 4019, "crc": 1887517713}, {"key": "androidx/compose/ui/layout/ContentScale$Companion.class", "name": "androidx/compose/ui/layout/ContentScale$Companion.class", "size": 3342, "crc": 1312766925}, {"key": "androidx/compose/ui/layout/ContentScale.class", "name": "androidx/compose/ui/layout/ContentScale.class", "size": 1025, "crc": -575527904}, {"key": "androidx/compose/ui/layout/ContentScaleKt.class", "name": "androidx/compose/ui/layout/ContentScaleKt.class", "size": 4966, "crc": 902262034}, {"key": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "size": 3578, "crc": -364200825}, {"key": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "size": 1681, "crc": -2124494455}, {"key": "androidx/compose/ui/layout/FixedScale.class", "name": "androidx/compose/ui/layout/FixedScale.class", "size": 3728, "crc": 573416187}, {"key": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "name": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "size": 2823, "crc": -2147239288}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "size": 791, "crc": 1519387101}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo.class", "size": 926, "crc": 1807730704}, {"key": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "name": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "size": 1243, "crc": -1968988922}, {"key": "androidx/compose/ui/layout/HorizontalRuler.class", "name": "androidx/compose/ui/layout/HorizontalRuler.class", "size": 3519, "crc": 1568130287}, {"key": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "size": 825, "crc": 1589692334}, {"key": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "size": 929, "crc": 828429367}, {"key": "androidx/compose/ui/layout/IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/IntrinsicMinMax.class", "size": 1828, "crc": -1709033051}, {"key": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "size": 1868, "crc": -808950986}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "size": 2595, "crc": -1943809324}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "size": 8292, "crc": 1344078664}, {"key": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "size": 2758, "crc": -1014306162}, {"key": "androidx/compose/ui/layout/LayoutCoordinates.class", "name": "androidx/compose/ui/layout/LayoutCoordinates.class", "size": 6140, "crc": -907014458}, {"key": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "size": 10807, "crc": 1915488383}, {"key": "androidx/compose/ui/layout/LayoutElement.class", "name": "androidx/compose/ui/layout/LayoutElement.class", "size": 5298, "crc": 202446096}, {"key": "androidx/compose/ui/layout/LayoutIdElement.class", "name": "androidx/compose/ui/layout/LayoutIdElement.class", "size": 3636, "crc": -1841995575}, {"key": "androidx/compose/ui/layout/LayoutIdKt.class", "name": "androidx/compose/ui/layout/LayoutIdKt.class", "size": 1541, "crc": -819456240}, {"key": "androidx/compose/ui/layout/LayoutIdModifier.class", "name": "androidx/compose/ui/layout/LayoutIdModifier.class", "size": 1940, "crc": 747956506}, {"key": "androidx/compose/ui/layout/LayoutIdParentData.class", "name": "androidx/compose/ui/layout/LayoutIdParentData.class", "size": 566, "crc": -946097934}, {"key": "androidx/compose/ui/layout/LayoutInfo.class", "name": "androidx/compose/ui/layout/LayoutInfo.class", "size": 2060, "crc": -1346116791}, {"key": "androidx/compose/ui/layout/LayoutInfo_androidKt.class", "name": "androidx/compose/ui/layout/LayoutInfo_androidKt.class", "size": 1217, "crc": -1578719726}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "size": 1563, "crc": -1161463965}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "size": 2295, "crc": -1816621882}, {"key": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "name": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "size": 6477, "crc": -2051556841}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "size": 5331, "crc": -1033218125}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "size": 5584, "crc": -1871315299}, {"key": "androidx/compose/ui/layout/LayoutKt.class", "name": "androidx/compose/ui/layout/LayoutKt.class", "size": 16109, "crc": -1223513045}, {"key": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "size": 3565, "crc": 783560171}, {"key": "androidx/compose/ui/layout/LayoutModifier.class", "name": "androidx/compose/ui/layout/LayoutModifier.class", "size": 4471, "crc": -994585984}, {"key": "androidx/compose/ui/layout/LayoutModifierImpl.class", "name": "androidx/compose/ui/layout/LayoutModifierImpl.class", "size": 3575, "crc": -1207239938}, {"key": "androidx/compose/ui/layout/LayoutModifierKt.class", "name": "androidx/compose/ui/layout/LayoutModifierKt.class", "size": 1618, "crc": 436110667}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$ApproachMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$ApproachMeasureScopeImpl.class", "size": 8358, "crc": 1812958309}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "size": 4950, "crc": -937387508}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "size": 4473, "crc": -815992850}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "size": 6997, "crc": 1720930784}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "size": 3898, "crc": 1218448968}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "size": 3937, "crc": -497078508}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "size": 6146, "crc": 717296981}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "size": 2498, "crc": 1319218307}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$1.class", "size": 1174, "crc": 699603662}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$2.class", "size": 7934, "crc": 548346574}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$3$1$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$3$1$1.class", "size": 4663, "crc": -325751469}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "size": 40038, "crc": 1023825857}, {"key": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "name": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "size": 2680, "crc": 1457370981}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "size": 12563, "crc": -1061316681}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "size": 1681, "crc": -1446161586}, {"key": "androidx/compose/ui/layout/LookaheadScope.class", "name": "androidx/compose/ui/layout/LookaheadScope.class", "size": 2803, "crc": -838455270}, {"key": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "name": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "size": 3792, "crc": 316883369}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1.class", "size": 1373, "crc": -854056578}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "size": 1515, "crc": -1954156293}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "size": 1733, "crc": -1922826459}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "size": 1982, "crc": -1312736748}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "size": 1951, "crc": -1816063186}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "size": 1847, "crc": -658263077}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt.class", "size": 11354, "crc": 1109577111}, {"key": "androidx/compose/ui/layout/Measurable.class", "name": "androidx/compose/ui/layout/Measurable.class", "size": 812, "crc": 706712405}, {"key": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "size": 1834, "crc": -371903321}, {"key": "androidx/compose/ui/layout/MeasurePolicy.class", "name": "androidx/compose/ui/layout/MeasurePolicy.class", "size": 8029, "crc": 362763121}, {"key": "androidx/compose/ui/layout/MeasureResult.class", "name": "androidx/compose/ui/layout/MeasureResult.class", "size": 1418, "crc": -1554600330}, {"key": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "size": 5696, "crc": 1824515000}, {"key": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "size": 3759, "crc": -39638360}, {"key": "androidx/compose/ui/layout/MeasureScope.class", "name": "androidx/compose/ui/layout/MeasureScope.class", "size": 8449, "crc": -1154493886}, {"key": "androidx/compose/ui/layout/MeasureScopeMarker.class", "name": "androidx/compose/ui/layout/MeasureScopeMarker.class", "size": 594, "crc": -818387478}, {"key": "androidx/compose/ui/layout/Measured.class", "name": "androidx/compose/ui/layout/Measured.class", "size": 1070, "crc": -1873306585}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4126, "crc": 1434516017}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "size": 3015, "crc": 83176321}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "size": 2077, "crc": 869561937}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 2122, "crc": -1918207453}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "size": 4775, "crc": -1880086616}, {"key": "androidx/compose/ui/layout/ModifierInfo.class", "name": "androidx/compose/ui/layout/ModifierInfo.class", "size": 2412, "crc": 1577036088}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "size": 10046, "crc": -867760852}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "size": 6387, "crc": -12393608}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "size": 1080, "crc": 2112216177}, {"key": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "size": 1614, "crc": 1712561386}, {"key": "androidx/compose/ui/layout/OnGlobalLayoutListenerKt.class", "name": "androidx/compose/ui/layout/OnGlobalLayoutListenerKt.class", "size": 2687, "crc": 2029438631}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "size": 3557, "crc": 2144577418}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "size": 2591, "crc": 1719556852}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "size": 2275, "crc": -1072583638}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "size": 1419, "crc": -1500752368}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "size": 2122, "crc": -765536859}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedElement.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedElement.class", "size": 5888, "crc": 2043802875}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedModifierKt.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedModifierKt.class", "size": 3648, "crc": -401479870}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedNode.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedNode.class", "size": 3740, "crc": 252237028}, {"key": "androidx/compose/ui/layout/OnPlacedElement.class", "name": "androidx/compose/ui/layout/OnPlacedElement.class", "size": 4598, "crc": 1669069032}, {"key": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "size": 2471, "crc": -1034639357}, {"key": "androidx/compose/ui/layout/OnPlacedModifier.class", "name": "androidx/compose/ui/layout/OnPlacedModifier.class", "size": 2179, "crc": 571674663}, {"key": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "name": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "size": 1359, "crc": -53270923}, {"key": "androidx/compose/ui/layout/OnPlacedNode.class", "name": "androidx/compose/ui/layout/OnPlacedNode.class", "size": 2058, "crc": -977310667}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "size": 2511, "crc": -953261117}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "size": 2107, "crc": -458136511}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "size": 1359, "crc": -2127665618}, {"key": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "name": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "size": 3195, "crc": -1528025213}, {"key": "androidx/compose/ui/layout/OnSizeChangedNode.class", "name": "androidx/compose/ui/layout/OnSizeChangedNode.class", "size": 3502, "crc": -2072531397}, {"key": "androidx/compose/ui/layout/OuterPlacementScope.class", "name": "androidx/compose/ui/layout/OuterPlacementScope.class", "size": 2133, "crc": -314507479}, {"key": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "size": 2491, "crc": 1450926656}, {"key": "androidx/compose/ui/layout/ParentDataModifier.class", "name": "androidx/compose/ui/layout/ParentDataModifier.class", "size": 2305, "crc": 213759482}, {"key": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "name": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "size": 578, "crc": -149158729}, {"key": "androidx/compose/ui/layout/PinnableContainer.class", "name": "androidx/compose/ui/layout/PinnableContainer.class", "size": 812, "crc": -840763181}, {"key": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "name": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "size": 1218, "crc": 619835108}, {"key": "androidx/compose/ui/layout/PinnableContainerKt.class", "name": "androidx/compose/ui/layout/PinnableContainerKt.class", "size": 1563, "crc": 1205296583}, {"key": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "name": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "size": 19993, "crc": -992497360}, {"key": "androidx/compose/ui/layout/Placeable.class", "name": "androidx/compose/ui/layout/Placeable.class", "size": 7189, "crc": -660452580}, {"key": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "name": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "size": 1390, "crc": -1389216771}, {"key": "androidx/compose/ui/layout/PlaceableKt.class", "name": "androidx/compose/ui/layout/PlaceableKt.class", "size": 2563, "crc": -2085957188}, {"key": "androidx/compose/ui/layout/PlacementScopeMarker.class", "name": "androidx/compose/ui/layout/PlacementScopeMarker.class", "size": 606, "crc": -2048815487}, {"key": "androidx/compose/ui/layout/Remeasurement.class", "name": "androidx/compose/ui/layout/Remeasurement.class", "size": 455, "crc": 1945025931}, {"key": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "size": 2521, "crc": -385262233}, {"key": "androidx/compose/ui/layout/RemeasurementModifier.class", "name": "androidx/compose/ui/layout/RemeasurementModifier.class", "size": 2224, "crc": 682998079}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "size": 1609, "crc": -334253276}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "size": 1871, "crc": -617278851}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$3.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$3.class", "size": 3203, "crc": -1510330371}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy.class", "size": 5308, "crc": 1841222856}, {"key": "androidx/compose/ui/layout/Ruler.class", "name": "androidx/compose/ui/layout/Ruler.class", "size": 1420, "crc": 2025043460}, {"key": "androidx/compose/ui/layout/RulerScope.class", "name": "androidx/compose/ui/layout/RulerScope.class", "size": 1166, "crc": 1174149108}, {"key": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "name": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "size": 1252, "crc": 1415880252}, {"key": "androidx/compose/ui/layout/ScaleFactor.class", "name": "androidx/compose/ui/layout/ScaleFactor.class", "size": 8522, "crc": -293109376}, {"key": "androidx/compose/ui/layout/ScaleFactorKt.class", "name": "androidx/compose/ui/layout/ScaleFactorKt.class", "size": 7683, "crc": -174245198}, {"key": "androidx/compose/ui/layout/SimplePlacementScope.class", "name": "androidx/compose/ui/layout/SimplePlacementScope.class", "size": 1401, "crc": 1008252686}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "size": 803, "crc": 1402737642}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "size": 2238, "crc": 1778634364}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "size": 1480, "crc": 119693439}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "size": 2488, "crc": 1949293180}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "size": 11976, "crc": 1349992548}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "size": 2095, "crc": 785956270}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "size": 2098, "crc": -1431578361}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "size": 2708, "crc": 174528926}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "size": 2875, "crc": -43597581}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "size": 7383, "crc": -558311824}, {"key": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "name": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "size": 1215, "crc": 789322698}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "size": 10775, "crc": 671407905}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "size": 1050, "crc": -800970921}, {"key": "androidx/compose/ui/layout/TestModifierUpdater.class", "name": "androidx/compose/ui/layout/TestModifierUpdater.class", "size": 1697, "crc": 1188311971}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "size": 1853, "crc": -278190541}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "size": 1907, "crc": 1785514347}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "size": 1741, "crc": 2132087238}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1.class", "size": 2228, "crc": 187925896}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "size": 6998, "crc": -1361094167}, {"key": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "name": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "size": 1239, "crc": -866394240}, {"key": "androidx/compose/ui/layout/VerticalRuler.class", "name": "androidx/compose/ui/layout/VerticalRuler.class", "size": 3508, "crc": -823246223}, {"key": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "name": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "size": 4244, "crc": -554295550}, {"key": "androidx/compose/ui/modifier/EmptyMap.class", "name": "androidx/compose/ui/modifier/EmptyMap.class", "size": 2384, "crc": 1720555359}, {"key": "androidx/compose/ui/modifier/ModifierLocal.class", "name": "androidx/compose/ui/modifier/ModifierLocal.class", "size": 1607, "crc": 1980132902}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "size": 2539, "crc": 1964330633}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "size": 2327, "crc": 1951272739}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "size": 2770, "crc": -678878633}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "size": 2939, "crc": 593959676}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "size": 2829, "crc": 598795211}, {"key": "androidx/compose/ui/modifier/ModifierLocalKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalKt.class", "size": 1122, "crc": 2129293752}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "size": 1260, "crc": -408993958}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager.class", "size": 14991, "crc": -1343090623}, {"key": "androidx/compose/ui/modifier/ModifierLocalMap.class", "name": "androidx/compose/ui/modifier/ModifierLocalMap.class", "size": 2102, "crc": -359916229}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "size": 9725, "crc": 475402965}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "size": 8101, "crc": 639611130}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "size": 2768, "crc": 272615548}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "size": 2559, "crc": **********}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "size": 3154, "crc": 987953120}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "size": 3637, "crc": 141847696}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "size": 3090, "crc": -695350172}, {"key": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "name": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "size": 816, "crc": -299664284}, {"key": "androidx/compose/ui/modifier/MultiLocalMap.class", "name": "androidx/compose/ui/modifier/MultiLocalMap.class", "size": 3449, "crc": **********}, {"key": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "name": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "size": 1250, "crc": 549866589}, {"key": "androidx/compose/ui/modifier/SingleLocalMap.class", "name": "androidx/compose/ui/modifier/SingleLocalMap.class", "size": 4902, "crc": **********}, {"key": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "name": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "size": 4943, "crc": -384513713}, {"key": "androidx/compose/ui/node/AlignmentLines.class", "name": "androidx/compose/ui/node/AlignmentLines.class", "size": 11390, "crc": -379863631}, {"key": "androidx/compose/ui/node/AlignmentLinesOwner.class", "name": "androidx/compose/ui/node/AlignmentLinesOwner.class", "size": 1926, "crc": -1162662381}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "size": 1278, "crc": 1809804688}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "size": 2664, "crc": 630158223}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "size": 1632, "crc": 2044710477}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "size": 1847, "crc": 669166838}, {"key": "androidx/compose/ui/node/BackwardsCompatNode.class", "name": "androidx/compose/ui/node/BackwardsCompatNode.class", "size": 31399, "crc": 366591737}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "size": 1423, "crc": -1425789414}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "size": 1477, "crc": 777312724}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "size": 1482, "crc": 142198808}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "size": 3231, "crc": -1798069489}, {"key": "androidx/compose/ui/node/CanFocusChecker.class", "name": "androidx/compose/ui/node/CanFocusChecker.class", "size": 2623, "crc": -1012126055}, {"key": "androidx/compose/ui/node/CenteredArray.class", "name": "androidx/compose/ui/node/CenteredArray.class", "size": 2733, "crc": 1432450091}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "size": 1600, "crc": -798294967}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "size": 1680, "crc": -1374093409}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "size": 1748, "crc": 1789975789}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "size": 1738, "crc": 433273610}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "size": 1663, "crc": -1655357302}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "size": 1792, "crc": -872834620}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "size": 1784, "crc": 2104261312}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "size": 1363, "crc": -1573417084}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "size": 6994, "crc": 2098913718}, {"key": "androidx/compose/ui/node/ComposeUiNode.class", "name": "androidx/compose/ui/node/ComposeUiNode.class", "size": 2653, "crc": 1416801265}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "size": 547, "crc": -1613838163}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "size": 3240, "crc": 1468342090}, {"key": "androidx/compose/ui/node/DelegatableNode$RegistrationHandle.class", "name": "androidx/compose/ui/node/DelegatableNode$RegistrationHandle.class", "size": 585, "crc": -1881165433}, {"key": "androidx/compose/ui/node/DelegatableNode.class", "name": "androidx/compose/ui/node/DelegatableNode.class", "size": 1085, "crc": 56376864}, {"key": "androidx/compose/ui/node/DelegatableNodeKt.class", "name": "androidx/compose/ui/node/DelegatableNodeKt.class", "size": 68798, "crc": 1115790354}, {"key": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "name": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "size": 2724, "crc": 1998198032}, {"key": "androidx/compose/ui/node/DelegatingNode.class", "name": "androidx/compose/ui/node/DelegatingNode.class", "size": 12569, "crc": 1366748407}, {"key": "androidx/compose/ui/node/DepthSortedSet.class", "name": "androidx/compose/ui/node/DepthSortedSet.class", "size": 6664, "crc": -1980933811}, {"key": "androidx/compose/ui/node/DepthSortedSetKt$DepthComparator$1.class", "name": "androidx/compose/ui/node/DepthSortedSetKt$DepthComparator$1.class", "size": 1549, "crc": -1264564009}, {"key": "androidx/compose/ui/node/DepthSortedSetKt.class", "name": "androidx/compose/ui/node/DepthSortedSetKt.class", "size": 993, "crc": -212786996}, {"key": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "name": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "size": 5009, "crc": -1239296578}, {"key": "androidx/compose/ui/node/DiffCallback.class", "name": "androidx/compose/ui/node/DiffCallback.class", "size": 662, "crc": -1022420167}, {"key": "androidx/compose/ui/node/DistanceAndFlags.class", "name": "androidx/compose/ui/node/DistanceAndFlags.class", "size": 4241, "crc": 1511067887}, {"key": "androidx/compose/ui/node/DpTouchBoundsExpansion$Companion.class", "name": "androidx/compose/ui/node/DpTouchBoundsExpansion$Companion.class", "size": 2860, "crc": 2102416057}, {"key": "androidx/compose/ui/node/DpTouchBoundsExpansion.class", "name": "androidx/compose/ui/node/DpTouchBoundsExpansion.class", "size": 7409, "crc": 493476747}, {"key": "androidx/compose/ui/node/DrawModifierNode.class", "name": "androidx/compose/ui/node/DrawModifierNode.class", "size": 909, "crc": 872385242}, {"key": "androidx/compose/ui/node/DrawModifierNodeKt.class", "name": "androidx/compose/ui/node/DrawModifierNodeKt.class", "size": 2102, "crc": -1168153669}, {"key": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "name": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "size": 815, "crc": 977771256}, {"key": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "name": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "size": 3941, "crc": -335603713}, {"key": "androidx/compose/ui/node/HitTestResult$SubList.class", "name": "androidx/compose/ui/node/HitTestResult$SubList.class", "size": 9262, "crc": -531731425}, {"key": "androidx/compose/ui/node/HitTestResult.class", "name": "androidx/compose/ui/node/HitTestResult.class", "size": 17055, "crc": 1570594721}, {"key": "androidx/compose/ui/node/HitTestResultKt.class", "name": "androidx/compose/ui/node/HitTestResultKt.class", "size": 1355, "crc": 1700377694}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "size": 1191, "crc": 24687520}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "size": 7071, "crc": 947820579}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator.class", "size": 14989, "crc": 751038490}, {"key": "androidx/compose/ui/node/IntStack.class", "name": "androidx/compose/ui/node/IntStack.class", "size": 4477, "crc": 1808453304}, {"key": "androidx/compose/ui/node/InternalCoreApi.class", "name": "androidx/compose/ui/node/InternalCoreApi.class", "size": 1014, "crc": -1327043932}, {"key": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "name": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "size": 775, "crc": 1891858322}, {"key": "androidx/compose/ui/node/IntrinsicsPolicy.class", "name": "androidx/compose/ui/node/IntrinsicsPolicy.class", "size": 6107, "crc": 1302054079}, {"key": "androidx/compose/ui/node/LayerPositionalProperties.class", "name": "androidx/compose/ui/node/LayerPositionalProperties.class", "size": 2957, "crc": -646169008}, {"key": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "name": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "size": 1113, "crc": 1481630429}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "size": 1639, "crc": 599319508}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "size": 1635, "crc": -1756912350}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "size": 1639, "crc": -564935976}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "size": 1635, "crc": -821748384}, {"key": "androidx/compose/ui/node/LayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNode.class", "size": 3258, "crc": 74484888}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "size": 1236, "crc": -1925037076}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "size": 6777, "crc": 1049496551}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "size": 2497, "crc": 622835313}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "size": 19271, "crc": -1779949644}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "size": 3652, "crc": -1670921372}, {"key": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "size": 2863, "crc": -1416926461}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "size": 1222, "crc": 2034472206}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "size": 1808, "crc": -678344642}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "size": 1973, "crc": 1389390735}, {"key": "androidx/compose/ui/node/LayoutNode$Companion.class", "name": "androidx/compose/ui/node/LayoutNode$Companion.class", "size": 2216, "crc": -1110146560}, {"key": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "name": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "size": 2167, "crc": 267922470}, {"key": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "name": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "size": 3172, "crc": 402885693}, {"key": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "name": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "size": 2051, "crc": 168260467}, {"key": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "name": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "size": 794, "crc": -1524275079}, {"key": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "name": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "size": 1347, "crc": -862330676}, {"key": "androidx/compose/ui/node/LayoutNode$calculateSemanticsConfiguration$1.class", "name": "androidx/compose/ui/node/LayoutNode$calculateSemanticsConfiguration$1.class", "size": 7811, "crc": 1786908616}, {"key": "androidx/compose/ui/node/LayoutNode.class", "name": "androidx/compose/ui/node/LayoutNode.class", "size": 84633, "crc": 332644633}, {"key": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "name": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "size": 2758, "crc": -1234987013}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScope$record$1.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScope$record$1.class", "size": 5924, "crc": 1568454403}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "size": 30207, "crc": -2095641430}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "size": 2306, "crc": 258759878}, {"key": "androidx/compose/ui/node/LayoutNodeKt.class", "name": "androidx/compose/ui/node/LayoutNodeKt.class", "size": 2995, "crc": 1532097597}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "size": 13525, "crc": 2046939573}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "size": 4516, "crc": 295038882}, {"key": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "name": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "size": 10329, "crc": 1367475570}, {"key": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "name": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "size": 4582, "crc": 532962477}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "size": 1741, "crc": -1236208981}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "size": 1008, "crc": -1449681394}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$3.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$3.class", "size": 1867, "crc": 384273049}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "size": 3457, "crc": 42722146}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$rulerScope$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$rulerScope$1.class", "size": 2468, "crc": 2083227576}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "size": 29503, "crc": 656719705}, {"key": "androidx/compose/ui/node/LookaheadDelegate.class", "name": "androidx/compose/ui/node/LookaheadDelegate.class", "size": 14457, "crc": -1431178212}, {"key": "androidx/compose/ui/node/LookaheadDelegateKt.class", "name": "androidx/compose/ui/node/LookaheadDelegateKt.class", "size": 2177, "crc": 351511136}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$PlacedState.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$PlacedState.class", "size": 2148, "crc": 28813342}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$WhenMappings.class", "size": 1402, "crc": -1102967127}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$1.class", "size": 1638, "crc": 1503105928}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$4.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$4.class", "size": 1717, "crc": 1349519729}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1.class", "size": 4881, "crc": 663184220}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$performMeasure$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$performMeasure$1.class", "size": 1796, "crc": 117840892}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$placeSelf$2.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$placeSelf$2.class", "size": 3934, "crc": 1002065308}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$remeasure$2.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$remeasure$2.class", "size": 1628, "crc": 886449493}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate.class", "size": 38269, "crc": -132830502}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "size": 1588, "crc": 1892846023}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "size": 1091, "crc": -1203211041}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "size": 30276, "crc": -678246240}, {"key": "androidx/compose/ui/node/MeasureBlocks.class", "name": "androidx/compose/ui/node/MeasureBlocks.class", "size": 2076, "crc": 1391606410}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$WhenMappings.class", "size": 1280, "crc": 2045700833}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$1.class", "size": 1638, "crc": -712669066}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$2.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$2.class", "size": 1721, "crc": 1709905475}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1.class", "size": 2157, "crc": 1833488076}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$performMeasureBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$performMeasureBlock$1.class", "size": 1583, "crc": -1947328650}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "size": 3404, "crc": 1296236326}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$remeasure$2.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$remeasure$2.class", "size": 1620, "crc": 1820001780}, {"key": "androidx/compose/ui/node/MeasurePassDelegate.class", "name": "androidx/compose/ui/node/MeasurePassDelegate.class", "size": 41418, "crc": -236535629}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "size": 756, "crc": 1421901252}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "size": 1101, "crc": 681372712}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "size": 4586, "crc": -703284436}, {"key": "androidx/compose/ui/node/MergedViewAdapter.class", "name": "androidx/compose/ui/node/MergedViewAdapter.class", "size": 5283, "crc": -278536055}, {"key": "androidx/compose/ui/node/ModifierNodeElement.class", "name": "androidx/compose/ui/node/ModifierNodeElement.class", "size": 4468, "crc": 1305966701}, {"key": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "name": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "size": 630, "crc": -1179601267}, {"key": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "name": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "size": 5097, "crc": 1190344798}, {"key": "androidx/compose/ui/node/MyersDiffKt.class", "name": "androidx/compose/ui/node/MyersDiffKt.class", "size": 8175, "crc": -1481099686}, {"key": "androidx/compose/ui/node/NodeChain$Differ.class", "name": "androidx/compose/ui/node/NodeChain$Differ.class", "size": 10057, "crc": 1556277750}, {"key": "androidx/compose/ui/node/NodeChain$Logger.class", "name": "androidx/compose/ui/node/NodeChain$Logger.class", "size": 1862, "crc": 726522300}, {"key": "androidx/compose/ui/node/NodeChain.class", "name": "androidx/compose/ui/node/NodeChain.class", "size": 42570, "crc": 637829529}, {"key": "androidx/compose/ui/node/NodeChainKt$SentinelHead$1.class", "name": "androidx/compose/ui/node/NodeChainKt$SentinelHead$1.class", "size": 866, "crc": -372471504}, {"key": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "name": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "size": 1972, "crc": 1369124982}, {"key": "androidx/compose/ui/node/NodeChainKt.class", "name": "androidx/compose/ui/node/NodeChainKt.class", "size": 5999, "crc": 2090756008}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "size": 6420, "crc": 805597781}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "size": 3576, "crc": -1764767350}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "size": 1546, "crc": 420421340}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "size": 3139, "crc": -101504621}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "size": 2151, "crc": -1896706710}, {"key": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "name": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "size": 1687, "crc": -1538726266}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "size": 3149, "crc": -470486074}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$drawBlockCallToDrawModifiers$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$drawBlockCallToDrawModifiers$1.class", "size": 1832, "crc": -1392947530}, {"key": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "size": 1386, "crc": 64175973}, {"key": "androidx/compose/ui/node/NodeCoordinator$outOfBoundsHit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$outOfBoundsHit$1.class", "size": 3502, "crc": 2024780502}, {"key": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "size": 3612, "crc": -361665544}, {"key": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "size": 1725, "crc": 1783221503}, {"key": "androidx/compose/ui/node/NodeCoordinator.class", "name": "androidx/compose/ui/node/NodeCoordinator.class", "size": 94739, "crc": -1778942978}, {"key": "androidx/compose/ui/node/NodeCoordinatorKt.class", "name": "androidx/compose/ui/node/NodeCoordinatorKt.class", "size": 4927, "crc": 1794571443}, {"key": "androidx/compose/ui/node/NodeKind.class", "name": "androidx/compose/ui/node/NodeKind.class", "size": 2532, "crc": 368697665}, {"key": "androidx/compose/ui/node/NodeKindKt.class", "name": "androidx/compose/ui/node/NodeKindKt.class", "size": 24291, "crc": 594323733}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "size": 1190, "crc": -2142226878}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4154, "crc": 549824053}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "size": 3057, "crc": 1514480126}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "size": 2097, "crc": 1895419504}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 2142, "crc": 1213668569}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "size": 1150, "crc": 1514714034}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "size": 7886, "crc": -941213135}, {"key": "androidx/compose/ui/node/Nodes.class", "name": "androidx/compose/ui/node/Nodes.class", "size": 7741, "crc": -1483365472}, {"key": "androidx/compose/ui/node/ObserverModifierNode.class", "name": "androidx/compose/ui/node/ObserverModifierNode.class", "size": 564, "crc": -1880358160}, {"key": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "name": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "size": 3122, "crc": -1799545863}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "size": 1663, "crc": 1272843289}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "size": 1404, "crc": 1908126490}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "size": 2499, "crc": 1464666404}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "size": 1897, "crc": 209867954}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "size": 1014, "crc": 1777528535}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher.class", "size": 5818, "crc": 1028237794}, {"key": "androidx/compose/ui/node/OnUnplacedModifierNode.class", "name": "androidx/compose/ui/node/OnUnplacedModifierNode.class", "size": 559, "crc": 1565567131}, {"key": "androidx/compose/ui/node/OwnedLayer.class", "name": "androidx/compose/ui/node/OwnedLayer.class", "size": 2831, "crc": -614748747}, {"key": "androidx/compose/ui/node/Owner$Companion.class", "name": "androidx/compose/ui/node/Owner$Companion.class", "size": 995, "crc": -930882280}, {"key": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "name": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "size": 568, "crc": -413266533}, {"key": "androidx/compose/ui/node/Owner.class", "name": "androidx/compose/ui/node/Owner.class", "size": 15577, "crc": -1838240058}, {"key": "androidx/compose/ui/node/OwnerScope.class", "name": "androidx/compose/ui/node/OwnerScope.class", "size": 453, "crc": -256369817}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "size": 1679, "crc": -1266758475}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "size": 1636, "crc": -422354860}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "size": 1652, "crc": 1903667541}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "size": 1683, "crc": -1583089753}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "size": 1651, "crc": -243389004}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "size": 1671, "crc": 1952373893}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "size": 1644, "crc": -1649584590}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "size": 1572, "crc": -1137559929}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "size": 7674, "crc": 1919111379}, {"key": "androidx/compose/ui/node/ParentDataModifierNode.class", "name": "androidx/compose/ui/node/ParentDataModifierNode.class", "size": 888, "crc": 1391242292}, {"key": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "name": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "size": 1022, "crc": -1553833770}, {"key": "androidx/compose/ui/node/PlaceableResult.class", "name": "androidx/compose/ui/node/PlaceableResult.class", "size": 3491, "crc": 307723553}, {"key": "androidx/compose/ui/node/PointerInputModifierNode.class", "name": "androidx/compose/ui/node/PointerInputModifierNode.class", "size": 2086, "crc": 1777832923}, {"key": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "name": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "size": 2524, "crc": 484746980}, {"key": "androidx/compose/ui/node/Ref.class", "name": "androidx/compose/ui/node/Ref.class", "size": 1236, "crc": 1478213580}, {"key": "androidx/compose/ui/node/RootForTest.class", "name": "androidx/compose/ui/node/RootForTest.class", "size": 1946, "crc": -521202349}, {"key": "androidx/compose/ui/node/SemanticsModifierNode.class", "name": "androidx/compose/ui/node/SemanticsModifierNode.class", "size": 1164, "crc": -1104785542}, {"key": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "name": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "size": 3981, "crc": -541989948}, {"key": "androidx/compose/ui/node/Snake.class", "name": "androidx/compose/ui/node/Snake.class", "size": 6893, "crc": -233789058}, {"key": "androidx/compose/ui/node/TailModifierNode.class", "name": "androidx/compose/ui/node/TailModifierNode.class", "size": 1597, "crc": 1864549492}, {"key": "androidx/compose/ui/node/TouchBoundsExpansion$Companion.class", "name": "androidx/compose/ui/node/TouchBoundsExpansion$Companion.class", "size": 4750, "crc": 267119839}, {"key": "androidx/compose/ui/node/TouchBoundsExpansion.class", "name": "androidx/compose/ui/node/TouchBoundsExpansion.class", "size": 4293, "crc": 1454824274}, {"key": "androidx/compose/ui/node/TouchBoundsExpansionKt.class", "name": "androidx/compose/ui/node/TouchBoundsExpansionKt.class", "size": 4520, "crc": 1255926118}, {"key": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "name": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "size": 2367, "crc": 658605543}, {"key": "androidx/compose/ui/node/TraversableNode$Companion.class", "name": "androidx/compose/ui/node/TraversableNode$Companion.class", "size": 848, "crc": -1264096088}, {"key": "androidx/compose/ui/node/TraversableNode.class", "name": "androidx/compose/ui/node/TraversableNode.class", "size": 944, "crc": 490170145}, {"key": "androidx/compose/ui/node/TraversableNodeKt.class", "name": "androidx/compose/ui/node/TraversableNodeKt.class", "size": 30591, "crc": 1672913297}, {"key": "androidx/compose/ui/node/TreeSet.class", "name": "androidx/compose/ui/node/TreeSet.class", "size": 1255, "crc": -1171110013}, {"key": "androidx/compose/ui/node/UiApplier.class", "name": "androidx/compose/ui/node/UiApplier.class", "size": 2781, "crc": -1006050351}, {"key": "androidx/compose/ui/node/ViewAdapter.class", "name": "androidx/compose/ui/node/ViewAdapter.class", "size": 1162, "crc": -325952558}, {"key": "androidx/compose/ui/node/ViewInterop_androidKt.class", "name": "androidx/compose/ui/node/ViewInterop_androidKt.class", "size": 4772, "crc": -1252642883}, {"key": "androidx/compose/ui/node/WeakReference.class", "name": "androidx/compose/ui/node/WeakReference.class", "size": 1053, "crc": 83825290}, {"key": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "name": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "size": 2439, "crc": 1232931993}, {"key": "androidx/compose/ui/platform/AbstractComposeView.class", "name": "androidx/compose/ui/platform/AbstractComposeView.class", "size": 14361, "crc": 560820018}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "size": 2187, "crc": 1764867475}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "size": 2178, "crc": 596755911}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "size": 3907, "crc": -355041734}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "size": 2005, "crc": -531193599}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "size": 4725, "crc": -398683351}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "size": 2005, "crc": 1724250257}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "size": 7448, "crc": -7813}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "size": 1889, "crc": 1294731584}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "size": 3281, "crc": -1993383456}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "size": 803, "crc": -1429920024}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "size": 2133, "crc": -277266267}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "size": 4451, "crc": -1002818575}, {"key": "androidx/compose/ui/platform/AccessibilityIterators.class", "name": "androidx/compose/ui/platform/AccessibilityIterators.class", "size": 1619, "crc": -133700592}, {"key": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "name": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "size": 601, "crc": 1838960230}, {"key": "androidx/compose/ui/platform/AccessibilityManager.class", "name": "androidx/compose/ui/platform/AccessibilityManager.class", "size": 1254, "crc": 318159416}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "size": 1031, "crc": -1848656947}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "size": 3077, "crc": 758657512}, {"key": "androidx/compose/ui/platform/AndroidClipboard.class", "name": "androidx/compose/ui/platform/AndroidClipboard.class", "size": 2782, "crc": 511809302}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager.class", "size": 5153, "crc": 1035931658}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "size": 8287, "crc": 33219022}, {"key": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "size": 2414, "crc": -604291578}, {"key": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "name": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "size": 1668, "crc": -545486578}, {"key": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "size": 2002, "crc": 1240693070}, {"key": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "size": 6167, "crc": 1699456518}, {"key": "androidx/compose/ui/platform/AndroidComposeView$bringIntoViewNode$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$bringIntoViewNode$1.class", "size": 2815, "crc": 242661296}, {"key": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "size": 1472, "crc": -2044881610}, {"key": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "size": 1707, "crc": 2109448059}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "size": 1540, "crc": -458939846}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropManager$1.class", "size": 2501, "crc": -1142459671}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$1.class", "size": 1619, "crc": -1632738430}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$2.class", "size": 2035, "crc": 1442598652}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$3.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$3.class", "size": 1635, "crc": 1374590972}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$4.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$4.class", "size": 1381, "crc": -1946917771}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$5.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$5.class", "size": 1436, "crc": 379072557}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$6.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$6.class", "size": 1392, "crc": -683830888}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$searchResult$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$searchResult$1.class", "size": 1884, "crc": -697869405}, {"key": "androidx/compose/ui/platform/AndroidComposeView$getFocusedRect$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$getFocusedRect$1.class", "size": 1513, "crc": 433336556}, {"key": "androidx/compose/ui/platform/AndroidComposeView$handleRotaryEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$handleRotaryEvent$1.class", "size": 1565, "crc": -1215966591}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "size": 1742, "crc": -1946954548}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "size": 1792, "crc": 1317866793}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "size": 6245, "crc": 331523225}, {"key": "androidx/compose/ui/platform/AndroidComposeView$onAttachedToWindow$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$onAttachedToWindow$1.class", "size": 1531, "crc": 1297700552}, {"key": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "size": 2513, "crc": 1390416059}, {"key": "androidx/compose/ui/platform/AndroidComposeView$removeAndroidView$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$removeAndroidView$1.class", "size": 2027, "crc": 1691910085}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "size": 1517, "crc": 1326671330}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$altFocus$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$altFocus$1.class", "size": 1535, "crc": 1889707109}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$focusSearchResult$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$focusSearchResult$1.class", "size": 1812, "crc": 762082637}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "size": 2157, "crc": -766365904}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "size": 1993, "crc": 2045582404}, {"key": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "size": 1580, "crc": 1054530409}, {"key": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "size": 2472, "crc": 1620130875}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "size": 1798, "crc": 1744634167}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "size": 2019, "crc": -75697812}, {"key": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "size": 1663, "crc": -1033826996}, {"key": "androidx/compose/ui/platform/AndroidComposeView.class", "name": "androidx/compose/ui/platform/AndroidComposeView.class", "size": 136102, "crc": -358075377}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "size": 3668, "crc": 1138027057}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "size": 3018, "crc": -251810435}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "size": 4216, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "size": 1576, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "size": 4338, "crc": -389383768}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "size": 2149, "crc": -207443918}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "size": 2163, "crc": 897673846}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "size": 2158, "crc": -**********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "size": 6095, "crc": 609411103}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "size": 1979, "crc": -812863302}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "size": 1972, "crc": 1844094178}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "size": 3302, "crc": -23507400}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "size": 118779, "crc": -1075909125}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1$1.class", "size": 1497, "crc": 2039145538}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1$2.class", "size": 1497, "crc": -1163438049}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$UnmergedConfigComparator$1.class", "size": 2982, "crc": -1776993069}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$WhenMappings.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$WhenMappings.class", "size": 989, "crc": 1111709712}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "size": 2400, "crc": -1464054285}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$geometryDepthFirstSearch$isTraversalGroup$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$geometryDepthFirstSearch$isTraversalGroup$1.class", "size": 1591, "crc": 1890220364}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$special$$inlined$thenBy$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$special$$inlined$thenBy$1.class", "size": 2816, "crc": -21328078}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$special$$inlined$thenBy$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$special$$inlined$thenBy$2.class", "size": 2859, "crc": -359025504}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "size": 27820, "crc": -980030268}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "size": 1436, "crc": 876102486}, {"key": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "name": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "size": 1177, "crc": 405693448}, {"key": "androidx/compose/ui/platform/AndroidComposeViewSensitiveContent35.class", "name": "androidx/compose/ui/platform/AndroidComposeViewSensitiveContent35.class", "size": 1272, "crc": -1407996628}, {"key": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "size": 1905, "crc": -2014587616}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "size": 2152, "crc": -1829945579}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "size": 1709, "crc": -2033679353}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "size": 2453, "crc": -77598464}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "size": 1347, "crc": 976128626}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "size": 1577, "crc": -1021437988}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "size": 8198, "crc": 594124618}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "size": 1399, "crc": 1303575736}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "size": 1351, "crc": 275195711}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "size": 1429, "crc": 1909131810}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "size": 1423, "crc": 47366740}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalSavedStateRegistryOwner$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalSavedStateRegistryOwner$1.class", "size": 1459, "crc": 1554577798}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "size": 1324, "crc": -1338369731}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "size": 2034, "crc": -19160186}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "size": 2459, "crc": 520136429}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "size": 3216, "crc": 1714791081}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "size": 3446, "crc": 367882006}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "size": 2238, "crc": -1203019727}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2720, "crc": 758322048}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "size": 3688, "crc": 705557396}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "size": 2128, "crc": 465339323}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2712, "crc": 1436701494}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "size": 3641, "crc": -1969069710}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "size": 1799, "crc": 678612194}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "size": 25474, "crc": -1337220564}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "size": 3060, "crc": 691062408}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "size": 1372, "crc": -438435466}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "size": 1944, "crc": 1140235786}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "size": 1811, "crc": -1754528139}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "size": 2332, "crc": -2111670460}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "size": 2090, "crc": 306756270}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "size": 6404, "crc": -2097087346}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "size": 5934, "crc": -50182953}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "size": 1381, "crc": -557917955}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar.class", "size": 5814, "crc": 38436813}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "size": 3201, "crc": 138755412}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "size": 2525, "crc": -653000660}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "size": 2724, "crc": -1823062183}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "size": 2030, "crc": 794699194}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "size": 3615, "crc": 4763408}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "size": 10140, "crc": 1544651343}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "size": 660, "crc": 1027595590}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "size": 1918, "crc": -385401904}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "size": 1923, "crc": 1232257386}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "size": 3128, "crc": -416102157}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "size": 7857, "crc": 619068635}, {"key": "androidx/compose/ui/platform/AndroidUriHandler.class", "name": "androidx/compose/ui/platform/AndroidUriHandler.class", "size": 2078, "crc": -838280986}, {"key": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "name": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "size": 2854, "crc": 865707202}, {"key": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "name": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "size": 1305, "crc": 2040878953}, {"key": "androidx/compose/ui/platform/AndroidViewsHandler.class", "name": "androidx/compose/ui/platform/AndroidViewsHandler.class", "size": 7708, "crc": -103925240}, {"key": "androidx/compose/ui/platform/AndroidWindowInfo_androidKt.class", "name": "androidx/compose/ui/platform/AndroidWindowInfo_androidKt.class", "size": 7663, "crc": 258535379}, {"key": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "name": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "size": 1174, "crc": 1247059032}, {"key": "androidx/compose/ui/platform/Api29Impl.class", "name": "androidx/compose/ui/platform/Api29Impl.class", "size": 1357, "crc": 1674852483}, {"key": "androidx/compose/ui/platform/BoundsHelper$Companion.class", "name": "androidx/compose/ui/platform/BoundsHelper$Companion.class", "size": 1860, "crc": 241656083}, {"key": "androidx/compose/ui/platform/BoundsHelper.class", "name": "androidx/compose/ui/platform/BoundsHelper.class", "size": 1000, "crc": 1839958339}, {"key": "androidx/compose/ui/platform/BoundsHelperApi16Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi16Impl.class", "size": 1838, "crc": 162630822}, {"key": "androidx/compose/ui/platform/BoundsHelperApi24Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi24Impl.class", "size": 2197, "crc": 829710816}, {"key": "androidx/compose/ui/platform/BoundsHelperApi28Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi28Impl.class", "size": 4758, "crc": 1886181258}, {"key": "androidx/compose/ui/platform/BoundsHelperApi29Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi29Impl.class", "size": 3168, "crc": -1881659300}, {"key": "androidx/compose/ui/platform/BoundsHelperApi30Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi30Impl.class", "size": 1536, "crc": 1975182556}, {"key": "androidx/compose/ui/platform/BringIntoViewOnScreenResponderNode.class", "name": "androidx/compose/ui/platform/BringIntoViewOnScreenResponderNode.class", "size": 3068, "crc": -647295485}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "size": 791, "crc": 1594110483}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "size": 3323, "crc": -1064962405}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "size": 2321, "crc": 258320193}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "size": 1970, "crc": 2007375635}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "size": 2116, "crc": -2049311228}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "size": 1758, "crc": -631946858}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "size": 2027, "crc": -1686502060}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "size": 4449, "crc": -1541930666}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "size": 5011, "crc": -1711656293}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "size": 4807, "crc": -1777979963}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "size": 4823, "crc": 109353093}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "size": 6110, "crc": -92313693}, {"key": "androidx/compose/ui/platform/ClipEntry.class", "name": "androidx/compose/ui/platform/ClipEntry.class", "size": 1620, "crc": -759697228}, {"key": "androidx/compose/ui/platform/ClipMetadata.class", "name": "androidx/compose/ui/platform/ClipMetadata.class", "size": 1144, "crc": -896896423}, {"key": "androidx/compose/ui/platform/Clipboard.class", "name": "androidx/compose/ui/platform/Clipboard.class", "size": 1397, "crc": -1534916774}, {"key": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "name": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "size": 1363, "crc": -940389136}, {"key": "androidx/compose/ui/platform/ClipboardManager.class", "name": "androidx/compose/ui/platform/ClipboardManager.class", "size": 2224, "crc": -1741326062}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda-1$1.class", "size": 2196, "crc": -533321577}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "size": 1529, "crc": 993593724}, {"key": "androidx/compose/ui/platform/ComposeView.class", "name": "androidx/compose/ui/platform/ComposeView.class", "size": 4767, "crc": 1706437928}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "size": 1245, "crc": -655649132}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "size": 1185, "crc": -2071916143}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillManager$1.class", "size": 1380, "crc": -50473092}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "size": 1360, "crc": 2123517937}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboard$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboard$1.class", "size": 1342, "crc": 1868504008}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "size": 1384, "crc": 304282050}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalCursorBlinkEnabled$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalCursorBlinkEnabled$1.class", "size": 1201, "crc": -319967647}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "size": 1318, "crc": -1349591523}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "size": 1351, "crc": -293475770}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "size": 1522, "crc": -1533350212}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "size": 1498, "crc": 1472832365}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "size": 1378, "crc": 1293410488}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "size": 1390, "crc": -691918712}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "size": 1371, "crc": 684828171}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "size": 1366, "crc": -1840180973}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "size": 1250, "crc": 525235003}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "size": 1231, "crc": 396739577}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "size": 1275, "crc": 1883969758}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "size": 1231, "crc": 1753505681}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "size": 1354, "crc": -343214861}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "size": 1348, "crc": 1090621031}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "size": 1390, "crc": -1093848374}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "size": 1348, "crc": 1894632834}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "size": 2326, "crc": 1261626857}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt.class", "size": 23232, "crc": -866830044}, {"key": "androidx/compose/ui/platform/DebugUtilsKt.class", "name": "androidx/compose/ui/platform/DebugUtilsKt.class", "size": 859, "crc": 1589311443}, {"key": "androidx/compose/ui/platform/DecodeHelper.class", "name": "androidx/compose/ui/platform/DecodeHelper.class", "size": 10383, "crc": -686168059}, {"key": "androidx/compose/ui/platform/DefaultHapticFeedback.class", "name": "androidx/compose/ui/platform/DefaultHapticFeedback.class", "size": 2569, "crc": -992200305}, {"key": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "size": 1651, "crc": 1213089637}, {"key": "androidx/compose/ui/platform/DeviceRenderNode.class", "name": "androidx/compose/ui/platform/DeviceRenderNode.class", "size": 4837, "crc": -552048668}, {"key": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "name": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "size": 14258, "crc": 1648229610}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "size": 3076, "crc": -1816326423}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "size": 1769, "crc": -1853639304}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "size": 1711, "crc": 1550516414}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "size": 9966, "crc": 1891031832}, {"key": "androidx/compose/ui/platform/DrawChildContainer.class", "name": "androidx/compose/ui/platform/DrawChildContainer.class", "size": 3517, "crc": -1457612071}, {"key": "androidx/compose/ui/platform/EncodeHelper.class", "name": "androidx/compose/ui/platform/EncodeHelper.class", "size": 10311, "crc": -977941009}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$Companion$FocusFinderThreadLocal$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$Companion$FocusFinderThreadLocal$1.class", "size": 1153, "crc": 536977123}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$Companion.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$Companion.class", "size": 1676, "crc": 368876526}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator$NextFocusGetter.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator$NextFocusGetter.class", "size": 1069, "crc": 2009795677}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator.class", "size": 6901, "crc": -302506637}, {"key": "androidx/compose/ui/platform/FocusFinderCompat.class", "name": "androidx/compose/ui/platform/FocusFinderCompat.class", "size": 9728, "crc": -2068255218}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findUserSetNextFocus$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findUserSetNextFocus$1.class", "size": 1670, "crc": 1030086424}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findViewInsideOutShouldExist$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findViewInsideOutShouldExist$1.class", "size": 1535, "crc": 1400813612}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt.class", "size": 5864, "crc": 1550114979}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "size": 6299, "crc": -728947007}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "size": 1756, "crc": 1740077953}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "size": 3116, "crc": 1667660113}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "size": 3818, "crc": 1631055691}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "size": 23997, "crc": 2075780181}, {"key": "androidx/compose/ui/platform/HapticDefaults.class", "name": "androidx/compose/ui/platform/HapticDefaults.class", "size": 1513, "crc": -1888230262}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "size": 3497, "crc": 831813168}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "size": 1090, "crc": 754217722}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "size": 2180, "crc": 324944812}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "size": 3150, "crc": 2098541915}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "size": 2216, "crc": 1368456751}, {"key": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "name": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "size": 4133, "crc": 1469027874}, {"key": "androidx/compose/ui/platform/InputMethodSession.class", "name": "androidx/compose/ui/platform/InputMethodSession.class", "size": 6794, "crc": -1881203189}, {"key": "androidx/compose/ui/platform/InspectableModifier$End.class", "name": "androidx/compose/ui/platform/InspectableModifier$End.class", "size": 949, "crc": -1266258032}, {"key": "androidx/compose/ui/platform/InspectableModifier.class", "name": "androidx/compose/ui/platform/InspectableModifier.class", "size": 2261, "crc": -238710460}, {"key": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "name": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "size": 1520, "crc": 944549212}, {"key": "androidx/compose/ui/platform/InspectableValue.class", "name": "androidx/compose/ui/platform/InspectableValue.class", "size": 1984, "crc": -2109350011}, {"key": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "size": 1400, "crc": 393335358}, {"key": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "size": 1769, "crc": -1701462351}, {"key": "androidx/compose/ui/platform/InspectableValueKt.class", "name": "androidx/compose/ui/platform/InspectableValueKt.class", "size": 4645, "crc": 367244991}, {"key": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "name": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "size": 1181, "crc": 159031030}, {"key": "androidx/compose/ui/platform/InspectionModeKt.class", "name": "androidx/compose/ui/platform/InspectionModeKt.class", "size": 1370, "crc": 2067670551}, {"key": "androidx/compose/ui/platform/InspectorInfo.class", "name": "androidx/compose/ui/platform/InspectorInfo.class", "size": 1887, "crc": -1050584080}, {"key": "androidx/compose/ui/platform/InspectorValueInfo.class", "name": "androidx/compose/ui/platform/InspectorValueInfo.class", "size": 2877, "crc": 823076060}, {"key": "androidx/compose/ui/platform/InvertMatrixKt.class", "name": "androidx/compose/ui/platform/InvertMatrixKt.class", "size": 6029, "crc": -82275050}, {"key": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "name": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "size": 3356, "crc": -704076654}, {"key": "androidx/compose/ui/platform/LayerMatrixCache.class", "name": "androidx/compose/ui/platform/LayerMatrixCache.class", "size": 5858, "crc": 1674883670}, {"key": "androidx/compose/ui/platform/LazyWindowInfo.class", "name": "androidx/compose/ui/platform/LazyWindowInfo.class", "size": 5929, "crc": -315797861}, {"key": "androidx/compose/ui/platform/LtrBoundsComparator.class", "name": "androidx/compose/ui/platform/LtrBoundsComparator.class", "size": 2004, "crc": 601691030}, {"key": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "name": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "size": 4704, "crc": -1262298362}, {"key": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "name": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "size": 2289, "crc": -1622421770}, {"key": "androidx/compose/ui/platform/MutableSpanStyle.class", "name": "androidx/compose/ui/platform/MutableSpanStyle.class", "size": 10211, "crc": -2007379579}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "size": 7684, "crc": -756251059}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "size": 9771, "crc": 2057728095}, {"key": "androidx/compose/ui/platform/NoHapticFeedback.class", "name": "androidx/compose/ui/platform/NoHapticFeedback.class", "size": 1116, "crc": 2132365699}, {"key": "androidx/compose/ui/platform/OutlineResolver.class", "name": "androidx/compose/ui/platform/OutlineResolver.class", "size": 18872, "crc": -2035412519}, {"key": "androidx/compose/ui/platform/OutlineVerificationHelper.class", "name": "androidx/compose/ui/platform/OutlineVerificationHelper.class", "size": 2677, "crc": 202111833}, {"key": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "size": 1315, "crc": 1672570017}, {"key": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "name": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "size": 853, "crc": -1812134463}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "size": 534, "crc": -678005874}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "size": 2257, "crc": -1077834930}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "size": 1368, "crc": -1632379442}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "size": 1729, "crc": -138816858}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "size": 1826, "crc": -860347037}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "size": 11790, "crc": -1200735816}, {"key": "androidx/compose/ui/platform/PlatformTextInputSession.class", "name": "androidx/compose/ui/platform/PlatformTextInputSession.class", "size": 1152, "crc": -1406414820}, {"key": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "name": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "size": 650, "crc": 2020392671}, {"key": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "size": 1316, "crc": -1294279023}, {"key": "androidx/compose/ui/platform/RenderNodeApi23.class", "name": "androidx/compose/ui/platform/RenderNodeApi23.class", "size": 16766, "crc": 1873061425}, {"key": "androidx/compose/ui/platform/RenderNodeApi29.class", "name": "androidx/compose/ui/platform/RenderNodeApi29.class", "size": 14205, "crc": 1295131168}, {"key": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "name": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "size": 1568, "crc": 1786314656}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "size": 1668, "crc": -864246295}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "size": 1029, "crc": 1968329953}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "size": 1229, "crc": -1094144112}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "size": 1860, "crc": -286700788}, {"key": "androidx/compose/ui/platform/RenderNodeLayer.class", "name": "androidx/compose/ui/platform/RenderNodeLayer.class", "size": 19392, "crc": 1935578394}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "size": 1110, "crc": -671365950}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "size": 1106, "crc": 1575286199}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "size": 1635, "crc": -1099022220}, {"key": "androidx/compose/ui/platform/RtlBoundsComparator.class", "name": "androidx/compose/ui/platform/RtlBoundsComparator.class", "size": 2004, "crc": -543167912}, {"key": "androidx/compose/ui/platform/ScrollObservationScope.class", "name": "androidx/compose/ui/platform/ScrollObservationScope.class", "size": 3738, "crc": -306754984}, {"key": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "name": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "size": 3813, "crc": -1976872234}, {"key": "androidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds.class", "name": "androidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds.class", "size": 1504, "crc": 29851869}, {"key": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "name": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "size": 13778, "crc": -684178516}, {"key": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "name": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "size": 10850, "crc": 1087663049}, {"key": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "size": 587, "crc": 1060651001}, {"key": "androidx/compose/ui/platform/SubcompositionKt.class", "name": "androidx/compose/ui/platform/SubcompositionKt.class", "size": 1362, "crc": -1831180111}, {"key": "androidx/compose/ui/platform/Synchronization_androidKt.class", "name": "androidx/compose/ui/platform/Synchronization_androidKt.class", "size": 2039, "crc": -561522769}, {"key": "androidx/compose/ui/platform/TestTagElement.class", "name": "androidx/compose/ui/platform/TestTagElement.class", "size": 2925, "crc": 1197908299}, {"key": "androidx/compose/ui/platform/TestTagKt.class", "name": "androidx/compose/ui/platform/TestTagKt.class", "size": 1036, "crc": -710086677}, {"key": "androidx/compose/ui/platform/TestTagNode.class", "name": "androidx/compose/ui/platform/TestTagNode.class", "size": 1697, "crc": -1229717653}, {"key": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "name": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "size": 2263, "crc": -1862380918}, {"key": "androidx/compose/ui/platform/TextToolbar.class", "name": "androidx/compose/ui/platform/TextToolbar.class", "size": 3979, "crc": 405829420}, {"key": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "name": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "size": 1846, "crc": -1806619189}, {"key": "androidx/compose/ui/platform/TextToolbarStatus.class", "name": "androidx/compose/ui/platform/TextToolbarStatus.class", "size": 1872, "crc": -118386849}, {"key": "androidx/compose/ui/platform/TopBottomBoundsComparator.class", "name": "androidx/compose/ui/platform/TopBottomBoundsComparator.class", "size": 2176, "crc": 164711627}, {"key": "androidx/compose/ui/platform/UriHandler.class", "name": "androidx/compose/ui/platform/UriHandler.class", "size": 570, "crc": -2102689201}, {"key": "androidx/compose/ui/platform/ValueElement.class", "name": "androidx/compose/ui/platform/ValueElement.class", "size": 2924, "crc": 1405654427}, {"key": "androidx/compose/ui/platform/ValueElementSequence.class", "name": "androidx/compose/ui/platform/ValueElementSequence.class", "size": 1957, "crc": -1120805899}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "size": 1382, "crc": -1132469183}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "size": 2149, "crc": -347004140}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "size": 1716, "crc": 271741812}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "size": 2491, "crc": -1401365256}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "size": 2677, "crc": -1679605559}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "size": 1951, "crc": -376974262}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "size": 3585, "crc": 760542689}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "size": 2261, "crc": -430653255}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "size": 2205, "crc": -1215015253}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "size": 1852, "crc": -1853432695}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "size": 4785, "crc": -1741787238}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "size": 5288, "crc": 552365902}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "size": 1796, "crc": 2118962419}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "size": 1665, "crc": -1364544336}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "size": 3426, "crc": 184175070}, {"key": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "size": 1386, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewConfiguration.class", "name": "androidx/compose/ui/platform/ViewConfiguration.class", "size": 3094, "crc": -657130481}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "size": 1576, "crc": -482894572}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "size": 1609, "crc": -810155874}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion.class", "size": 3951, "crc": 735357551}, {"key": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "size": 1205, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayer.class", "name": "androidx/compose/ui/platform/ViewLayer.class", "size": 22086, "crc": -519074136}, {"key": "androidx/compose/ui/platform/ViewLayerContainer.class", "name": "androidx/compose/ui/platform/ViewLayerContainer.class", "size": 1305, "crc": -733852994}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "size": 1306, "crc": -2081668449}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "size": 1517, "crc": -1719338046}, {"key": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "size": 1190, "crc": -1955470047}, {"key": "androidx/compose/ui/platform/ViewRootForInspector.class", "name": "androidx/compose/ui/platform/ViewRootForInspector.class", "size": 1507, "crc": 1110350229}, {"key": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "name": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "size": 1874, "crc": -360721664}, {"key": "androidx/compose/ui/platform/ViewRootForTest.class", "name": "androidx/compose/ui/platform/ViewRootForTest.class", "size": 1254, "crc": -1955564342}, {"key": "androidx/compose/ui/platform/WeakCache.class", "name": "androidx/compose/ui/platform/WeakCache.class", "size": 3862, "crc": -5506119}, {"key": "androidx/compose/ui/platform/WindowInfo.class", "name": "androidx/compose/ui/platform/WindowInfo.class", "size": 2649, "crc": -1297299365}, {"key": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "name": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "size": 1419, "crc": 904081953}, {"key": "androidx/compose/ui/platform/WindowInfoImpl.class", "name": "androidx/compose/ui/platform/WindowInfoImpl.class", "size": 4819, "crc": -1276799713}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "size": 1399, "crc": -176686860}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "size": 2117, "crc": 564707221}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "size": 4216, "crc": 2030731744}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "size": 1811, "crc": -1271215530}, {"key": "androidx/compose/ui/platform/WindowInfoKt.class", "name": "androidx/compose/ui/platform/WindowInfoKt.class", "size": 5529, "crc": 174399321}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "size": 1960, "crc": 366470443}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "size": 1129, "crc": 602953890}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "size": 1752, "crc": 236603001}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "size": 4240, "crc": -1520629320}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "size": 6315, "crc": 2086335731}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "size": 1816, "crc": 1356975684}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "size": 1218, "crc": 2053108938}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "size": 2187, "crc": -1874074002}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "size": 4284, "crc": -1703467875}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "size": 7153, "crc": 1199612727}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "size": 4004, "crc": 646469046}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "size": 6031, "crc": 509645759}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "size": 1707, "crc": -1503781191}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "size": 15095, "crc": 1379940295}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "size": 3602, "crc": 162887316}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "size": 3603, "crc": 894696566}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "size": 3120, "crc": -566499524}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "size": 7369, "crc": -1791386929}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "size": 3654, "crc": -527428366}, {"key": "androidx/compose/ui/platform/WrappedComposition.class", "name": "androidx/compose/ui/platform/WrappedComposition.class", "size": 5606, "crc": -969551564}, {"key": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "name": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "size": 1490, "crc": -1694852612}, {"key": "androidx/compose/ui/platform/Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/Wrapper_androidKt.class", "size": 5855, "crc": 1124039765}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "size": 1419, "crc": -301042470}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "size": 1703, "crc": 2121609468}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "size": 12837, "crc": -46504940}, {"key": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "size": 3167, "crc": 648090166}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "size": 1008, "crc": -622423714}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "size": 3246, "crc": 225615736}, {"key": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "size": 2364, "crc": -1304365344}, {"key": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "size": 9810, "crc": 1430483020}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNode.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNode.class", "size": 1311, "crc": 2117572385}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt$bringIntoView$2.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt$bringIntoView$2.class", "size": 2918, "crc": -1298799073}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt.class", "size": 9218, "crc": -2096653401}, {"key": "androidx/compose/ui/res/ColorResources_androidKt.class", "name": "androidx/compose/ui/res/ColorResources_androidKt.class", "size": 3476, "crc": 1609275239}, {"key": "androidx/compose/ui/res/FontResources_androidKt.class", "name": "androidx/compose/ui/res/FontResources_androidKt.class", "size": 5411, "crc": -774483949}, {"key": "androidx/compose/ui/res/ImageResources_androidKt.class", "name": "androidx/compose/ui/res/ImageResources_androidKt.class", "size": 5291, "crc": -1254355462}, {"key": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "name": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "size": 3256, "crc": -651489355}, {"key": "androidx/compose/ui/res/ImageVectorCache$Key.class", "name": "androidx/compose/ui/res/ImageVectorCache$Key.class", "size": 3171, "crc": -517812636}, {"key": "androidx/compose/ui/res/ImageVectorCache.class", "name": "androidx/compose/ui/res/ImageVectorCache.class", "size": 3251, "crc": 1513919000}, {"key": "androidx/compose/ui/res/PainterResources_androidKt.class", "name": "androidx/compose/ui/res/PainterResources_androidKt.class", "size": 10541, "crc": -1181410799}, {"key": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "name": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "size": 4940, "crc": -747463864}, {"key": "androidx/compose/ui/res/ResourceIdCache.class", "name": "androidx/compose/ui/res/ResourceIdCache.class", "size": 2737, "crc": 1729312189}, {"key": "androidx/compose/ui/res/ResourceResolutionException.class", "name": "androidx/compose/ui/res/ResourceResolutionException.class", "size": 1085, "crc": -1085434588}, {"key": "androidx/compose/ui/res/Resources_androidKt.class", "name": "androidx/compose/ui/res/Resources_androidKt.class", "size": 3014, "crc": 123503665}, {"key": "androidx/compose/ui/res/StringResources_androidKt.class", "name": "androidx/compose/ui/res/StringResources_androidKt.class", "size": 4387, "crc": -1800804184}, {"key": "androidx/compose/ui/res/VectorResources_androidKt.class", "name": "androidx/compose/ui/res/VectorResources_androidKt.class", "size": 9094, "crc": 1924174430}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "size": 743, "crc": 1007388533}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "size": 4212, "crc": 1698298858}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "size": 4675, "crc": -1389525897}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "size": 2456, "crc": -264756171}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "size": 1541, "crc": 33444240}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "size": 8038, "crc": 2075927045}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "size": 12871, "crc": -1117978689}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "size": 1735, "crc": -966767883}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "size": 3357, "crc": 1461256553}, {"key": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "name": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "size": 3129, "crc": -1736681461}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "size": 1923, "crc": -1733469124}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "size": 5555, "crc": 1934385483}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "size": 1769, "crc": 1069605330}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "size": 1860, "crc": -822842665}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "size": 1979, "crc": 2041804895}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "size": 9258, "crc": 1371668772}, {"key": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "name": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "size": 2479, "crc": 721461163}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "size": 10207, "crc": -615201705}, {"key": "androidx/compose/ui/semantics/AccessibilityAction.class", "name": "androidx/compose/ui/semantics/AccessibilityAction.class", "size": 2580, "crc": 70537431}, {"key": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "name": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "size": 6781, "crc": 1506210860}, {"key": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "name": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "size": 6050, "crc": 52864178}, {"key": "androidx/compose/ui/semantics/CollectionInfo.class", "name": "androidx/compose/ui/semantics/CollectionInfo.class", "size": 1056, "crc": -1575501291}, {"key": "androidx/compose/ui/semantics/CollectionItemInfo.class", "name": "androidx/compose/ui/semantics/CollectionItemInfo.class", "size": 1397, "crc": -1104837973}, {"key": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "name": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "size": 3429, "crc": 774772645}, {"key": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "name": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "size": 2627, "crc": -1659683683}, {"key": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "name": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "size": 2661, "crc": 1675886134}, {"key": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "name": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "size": 1326, "crc": -371445718}, {"key": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "name": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "size": 1260, "crc": -250260920}, {"key": "androidx/compose/ui/semantics/LiveRegionMode.class", "name": "androidx/compose/ui/semantics/LiveRegionMode.class", "size": 2655, "crc": 1043149891}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "size": 1228, "crc": -456036869}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "size": 4491, "crc": 488891630}, {"key": "androidx/compose/ui/semantics/Role$Companion.class", "name": "androidx/compose/ui/semantics/Role$Companion.class", "size": 2365, "crc": -1052462925}, {"key": "androidx/compose/ui/semantics/Role.class", "name": "androidx/compose/ui/semantics/Role.class", "size": 3518, "crc": -1501996537}, {"key": "androidx/compose/ui/semantics/ScrollAxisRange.class", "name": "androidx/compose/ui/semantics/ScrollAxisRange.class", "size": 2720, "crc": 687173108}, {"key": "androidx/compose/ui/semantics/SemanticsActions.class", "name": "androidx/compose/ui/semantics/SemanticsActions.class", "size": 17763, "crc": -810472614}, {"key": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "name": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "size": 15940, "crc": -1106485008}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "size": 1179, "crc": 2077033417}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "size": 1575, "crc": -1829336922}, {"key": "androidx/compose/ui/semantics/SemanticsInfo.class", "name": "androidx/compose/ui/semantics/SemanticsInfo.class", "size": 1222, "crc": -2116789784}, {"key": "androidx/compose/ui/semantics/SemanticsInfoKt.class", "name": "androidx/compose/ui/semantics/SemanticsInfoKt.class", "size": 3836, "crc": -1362608834}, {"key": "androidx/compose/ui/semantics/SemanticsListener.class", "name": "androidx/compose/ui/semantics/SemanticsListener.class", "size": 901, "crc": -1145593389}, {"key": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "name": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "size": 2992, "crc": 2038572992}, {"key": "androidx/compose/ui/semantics/SemanticsModifier.class", "name": "androidx/compose/ui/semantics/SemanticsModifier.class", "size": 2564, "crc": -920113262}, {"key": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "name": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "size": 5978, "crc": -499587957}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "size": 1780, "crc": -2049056964}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "size": 1705, "crc": 309270005}, {"key": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "size": 1799, "crc": 1888393577}, {"key": "androidx/compose/ui/semantics/SemanticsNode.class", "name": "androidx/compose/ui/semantics/SemanticsNode.class", "size": 22397, "crc": 882710622}, {"key": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "name": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "size": 13393, "crc": -1153258957}, {"key": "androidx/compose/ui/semantics/SemanticsOwner.class", "name": "androidx/compose/ui/semantics/SemanticsOwner.class", "size": 5594, "crc": 1030968906}, {"key": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "name": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "size": 5081, "crc": 1569560606}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "size": 1619, "crc": -317642333}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "size": 2503, "crc": 984119593}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "size": 1579, "crc": -338602939}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$HideFromAccessibility$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$HideFromAccessibility$1.class", "size": 1375, "crc": 405074687}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "size": 1363, "crc": -1556305790}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "size": 1545, "crc": -1509300628}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "size": 1541, "crc": -396584436}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$LinkTestMarker$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$LinkTestMarker$1.class", "size": 1361, "crc": -1285070371}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "size": 1516, "crc": -1549867691}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "size": 1536, "crc": 1884573050}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "size": 1382, "crc": -1272531697}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "size": 2622, "crc": -333150824}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "size": 1431, "crc": 16156351}, {"key": "androidx/compose/ui/semantics/SemanticsProperties.class", "name": "androidx/compose/ui/semantics/SemanticsProperties.class", "size": 16887, "crc": 1989803202}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "size": 1466, "crc": -1023805980}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "size": 1871, "crc": 1448188020}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "size": 2434, "crc": 941588469}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "size": 2022, "crc": 1778926538}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "size": 43600, "crc": 1284968346}, {"key": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "name": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "size": 2769, "crc": 480613723}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "size": 1302, "crc": 1642775282}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "size": 4706, "crc": -1999863338}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "size": 876, "crc": -1295020476}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt.class", "size": 848, "crc": -1172564704}, {"key": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy$measure$1.class", "name": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy$measure$1.class", "size": 1634, "crc": 709501020}, {"key": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy.class", "name": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy.class", "size": 2209, "crc": -128823318}, {"key": "androidx/compose/ui/spatial/RectList.class", "name": "androidx/compose/ui/spatial/RectList.class", "size": 24045, "crc": -1884705906}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierElement.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierElement.class", "size": 2340, "crc": -186355205}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierNode$onAttach$1.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierNode$onAttach$1.class", "size": 1441, "crc": 1415622934}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierNode.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierNode.class", "size": 6377, "crc": -251193637}, {"key": "androidx/compose/ui/spatial/RectListDebugger_androidKt$RectListDebugger$1.class", "name": "androidx/compose/ui/spatial/RectListDebugger_androidKt$RectListDebugger$1.class", "size": 1729, "crc": -299411192}, {"key": "androidx/compose/ui/spatial/RectListDebugger_androidKt.class", "name": "androidx/compose/ui/spatial/RectListDebugger_androidKt.class", "size": 7332, "crc": 62405759}, {"key": "androidx/compose/ui/spatial/RectListKt.class", "name": "androidx/compose/ui/spatial/RectListKt.class", "size": 6353, "crc": 2000754722}, {"key": "androidx/compose/ui/spatial/RectManager$currentRectInfo$1.class", "name": "androidx/compose/ui/spatial/RectManager$currentRectInfo$1.class", "size": 3794, "crc": 1867902719}, {"key": "androidx/compose/ui/spatial/RectManager$dispatchLambda$1.class", "name": "androidx/compose/ui/spatial/RectManager$dispatchLambda$1.class", "size": 2577, "crc": 675693650}, {"key": "androidx/compose/ui/spatial/RectManager.class", "name": "androidx/compose/ui/spatial/RectManager.class", "size": 23094, "crc": 1781459699}, {"key": "androidx/compose/ui/spatial/RectManagerKt.class", "name": "androidx/compose/ui/spatial/RectManagerKt.class", "size": 3317, "crc": 1207960206}, {"key": "androidx/compose/ui/spatial/RelativeLayoutBounds.class", "name": "androidx/compose/ui/spatial/RelativeLayoutBounds.class", "size": 10867, "crc": -2110752926}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacks$Entry.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacks$Entry.class", "size": 5722, "crc": 35251719}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacks.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacks.class", "size": 22889, "crc": -1061288464}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacksKt.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacksKt.class", "size": 5267, "crc": 862646084}, {"key": "androidx/compose/ui/state/ToggleableState.class", "name": "androidx/compose/ui/state/ToggleableState.class", "size": 1896, "crc": 1572949564}, {"key": "androidx/compose/ui/state/ToggleableStateKt.class", "name": "androidx/compose/ui/state/ToggleableStateKt.class", "size": 786, "crc": 641818682}, {"key": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "name": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "size": 4992, "crc": -228825928}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "size": 2101, "crc": -284183978}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "size": 2220, "crc": 846152772}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "size": 9343, "crc": 950154812}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "size": 1460, "crc": 1131059505}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "size": 1588, "crc": -47891844}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "size": 7597, "crc": 1085342142}, {"key": "androidx/compose/ui/text/input/InputEventCallback2.class", "name": "androidx/compose/ui/text/input/InputEventCallback2.class", "size": 1772, "crc": -1371591055}, {"key": "androidx/compose/ui/text/input/InputMethodManager.class", "name": "androidx/compose/ui/text/input/InputMethodManager.class", "size": 1475, "crc": 1189720300}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "size": 1881, "crc": -659395908}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "size": 4123, "crc": 1407856423}, {"key": "androidx/compose/ui/text/input/InputState_androidKt.class", "name": "androidx/compose/ui/text/input/InputState_androidKt.class", "size": 1697, "crc": -1562196606}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "size": 690, "crc": 1518561994}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "size": 7702, "crc": 317964323}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "size": 2295, "crc": 974977023}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "size": 2043, "crc": -332052114}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "size": 2594, "crc": -1225952455}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "size": 2049, "crc": -1624090019}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection.class", "size": 19658, "crc": -525123816}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "size": 698, "crc": 1770903757}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "size": 2311, "crc": -1637843173}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "size": 1089, "crc": -1008970710}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "size": 1646, "crc": 1254953417}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "size": 4017, "crc": -306830191}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "size": 1678, "crc": 21050906}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "size": 1623, "crc": -940552171}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "size": 1515, "crc": -1363325566}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "size": 1446, "crc": -897676657}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "size": 20537, "crc": -1758151666}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "size": 7207, "crc": 1871562358}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$2.class", "size": 2645, "crc": 1329811043}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "size": 2318, "crc": 310066348}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "size": 1023, "crc": -1949161838}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "size": 1842, "crc": 489559808}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "size": 1687, "crc": -62103978}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "size": 2124, "crc": -102111382}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "size": 2134, "crc": -1114606493}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "size": 1685, "crc": 1753159143}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "size": 2096, "crc": 906666115}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "size": 5180, "crc": -1755685639}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "size": 1688, "crc": 1944872729}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "size": 4226, "crc": -461289753}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "size": 3851, "crc": 664923257}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "size": 3965, "crc": -1065493692}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "size": 3629, "crc": -859242175}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "size": 1314, "crc": -902108488}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "size": 1310, "crc": -1930588696}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "size": 1573, "crc": -2076544127}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "size": 2282, "crc": -580232345}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "size": 1312, "crc": -1250106795}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "size": 43433, "crc": -1082534027}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "size": 867, "crc": 845862742}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "size": 4976, "crc": -506242163}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "size": 2261, "crc": -528627584}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "size": 2285, "crc": 1425611592}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "size": 2286, "crc": 1286262796}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "size": 2287, "crc": -1240518769}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "size": 2286, "crc": 1428538349}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "size": 2287, "crc": -1950901489}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "size": 2607, "crc": 35940421}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "size": 1311, "crc": 1545809891}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "size": 2851, "crc": -1655898544}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "size": 2228, "crc": -1844592177}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "size": 2247, "crc": -549987136}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "size": 2259, "crc": 92276666}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "size": 2318, "crc": -2123909198}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "size": 896, "crc": 78728589}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "size": 2600, "crc": -242575669}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "size": 20134, "crc": 795738512}, {"key": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "name": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "size": 4756, "crc": -1758359888}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "size": 2655, "crc": -1352635441}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onEnter$1.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onEnter$1.class", "size": 3085, "crc": -229662191}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onExit$1.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onExit$1.class", "size": 5375, "crc": -2053351302}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "size": 14220, "crc": -379951538}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "size": 2663, "crc": -496236683}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "size": 1615, "crc": -729048935}, {"key": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "size": 462, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "size": 388, "crc": -375270054}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "size": 2327, "crc": -285456756}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "size": 1804, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "size": 1738, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "size": 1742, "crc": -946948894}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "size": 9466, "crc": -**********}, {"key": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "name": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "size": 4211, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1$invoke$$inlined$onDispose$1.class", "size": 2199, "crc": -278070360}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "size": 2891, "crc": 940618337}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "size": 2232, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3.class", "size": 2417, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1.class", "size": 1634, "crc": 1796342306}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "size": 3632, "crc": -1011104248}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1.class", "size": 1377, "crc": 1950033944}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "size": 3309, "crc": 1368504457}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1.class", "size": 4661, "crc": 519517158}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "size": 2138, "crc": 1803266020}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "size": 16400, "crc": 1661126668}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "size": 1139, "crc": 141473110}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "size": 2648, "crc": 464239406}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "size": 2184, "crc": -1964835370}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "size": 3925, "crc": -710114147}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "size": 2395, "crc": -1912650135}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "size": 1928, "crc": -850802520}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "size": 3183, "crc": 1148091329}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "size": 1330, "crc": -1017849483}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "size": 4106, "crc": 463333069}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "size": 2005, "crc": 1144978918}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "size": 1649, "crc": 844220773}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "size": 2457, "crc": -462246785}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "size": 2672, "crc": -582663197}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1.class", "size": 1417, "crc": -419082758}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1.class", "size": 1641, "crc": 988606674}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "size": 1807, "crc": -1040013333}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "size": 10575, "crc": 1363929891}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "size": 2001, "crc": 1490145078}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "size": 1661, "crc": 914790612}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$2.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$2.class", "size": 1868, "crc": -1543516060}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$3.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$3.class", "size": 2274, "crc": -1341633065}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1.class", "size": 4916, "crc": 1390186069}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "size": 29750, "crc": 2099128137}, {"key": "androidx/compose/ui/window/Api33Impl.class", "name": "androidx/compose/ui/window/Api33Impl.class", "size": 2908, "crc": -477808552}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda-1$1.class", "size": 2230, "crc": 1311439754}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "size": 1551, "crc": 120903736}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda-1$1.class", "size": 2223, "crc": 1193430908}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "size": 1546, "crc": -838935950}, {"key": "androidx/compose/ui/window/DialogLayout$1.class", "name": "androidx/compose/ui/window/DialogLayout$1.class", "size": 4581, "crc": **********}, {"key": "androidx/compose/ui/window/DialogLayout.class", "name": "androidx/compose/ui/window/DialogLayout.class", "size": 13296, "crc": 700641794}, {"key": "androidx/compose/ui/window/DialogProperties.class", "name": "androidx/compose/ui/window/DialogProperties.class", "size": 3892, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWindowProvider.class", "name": "androidx/compose/ui/window/DialogWindowProvider.class", "size": 616, "crc": -423036127}, {"key": "androidx/compose/ui/window/DialogWrapper$1$2.class", "name": "androidx/compose/ui/window/DialogWrapper$1$2.class", "size": 1348, "crc": **********}, {"key": "androidx/compose/ui/window/DialogWrapper$2.class", "name": "androidx/compose/ui/window/DialogWrapper$2.class", "size": 2075, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "name": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "size": 814, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWrapper.class", "name": "androidx/compose/ui/window/DialogWrapper.class", "size": 12200, "crc": **********}, {"key": "androidx/compose/ui/window/PopupLayout$2.class", "name": "androidx/compose/ui/window/PopupLayout$2.class", "size": 1408, "crc": 38073287}, {"key": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "size": 1486, "crc": -606621804}, {"key": "androidx/compose/ui/window/PopupLayout$Companion.class", "name": "androidx/compose/ui/window/PopupLayout$Companion.class", "size": 981, "crc": 1717709659}, {"key": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "name": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "size": 809, "crc": -1806022367}, {"key": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "size": 2574, "crc": 1568468525}, {"key": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "name": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "size": 2629, "crc": 1177070143}, {"key": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "size": 2051, "crc": 403527618}, {"key": "androidx/compose/ui/window/PopupLayout.class", "name": "androidx/compose/ui/window/PopupLayout.class", "size": 28359, "crc": 1644534932}, {"key": "androidx/compose/ui/window/PopupLayoutHelper.class", "name": "androidx/compose/ui/window/PopupLayoutHelper.class", "size": 1395, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "size": 1969, "crc": **********}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "size": 1374, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupPositionProvider.class", "name": "androidx/compose/ui/window/PopupPositionProvider.class", "size": 1069, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupProperties.class", "name": "androidx/compose/ui/window/PopupProperties.class", "size": 5358, "crc": -68000931}, {"key": "androidx/compose/ui/window/SecureFlagPolicy.class", "name": "androidx/compose/ui/window/SecureFlagPolicy.class", "size": 1925, "crc": -214011108}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "size": 910, "crc": 485613228}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "size": 1170, "crc": 237959647}, {"key": "androidx/inspection/compose/ui/ProguardDetection.class", "name": "androidx/inspection/compose/ui/ProguardDetection.class", "size": 545, "crc": -656128053}, {"key": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "name": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "size": 1345, "crc": 1112067839}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "size": 857, "crc": 401769262}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "size": 2868, "crc": -342138471}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "size": 1205, "crc": 976977248}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "size": 6362, "crc": -1228692622}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "size": 807, "crc": -1944113727}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "size": 852, "crc": 665748574}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "size": 785, "crc": -1640713814}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "size": 2851, "crc": -1116252136}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "size": 2172, "crc": 668368597}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "size": 3364, "crc": 232904801}, {"key": "META-INF/androidx.compose.ui_ui.version", "name": "META-INF/androidx.compose.ui_ui.version", "size": 6, "crc": 333960004}, {"key": "META-INF/ui_release.kotlin_module", "name": "META-INF/ui_release.kotlin_module", "size": 5379, "crc": -1563267189}]