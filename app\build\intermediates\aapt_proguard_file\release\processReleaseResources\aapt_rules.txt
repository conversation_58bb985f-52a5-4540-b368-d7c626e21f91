-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class cn.ykload.flowmix.EqualizerActivity { <init>(); }
-keep class cn.ykload.flowmix.MainActivity { <init>(); }
-keep class cn.ykload.flowmix.receiver.BootReceiver { <init>(); }
-keep class cn.ykload.flowmix.service.FlowmixKeepAliveService { <init>(); }
