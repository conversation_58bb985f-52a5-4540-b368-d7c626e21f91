[{"key": "androidx/compose/foundation/layout/AddedInsets.class", "name": "androidx/compose/foundation/layout/AddedInsets.class", "size": 2926, "crc": -1554344106}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "size": 2922, "crc": -1591466599}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "size": 3182, "crc": 1456237330}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "size": 3197, "crc": 23262918}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt.class", "size": 10778, "crc": 919020008}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "size": 6834, "crc": 1760520461}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "size": 3255, "crc": **********}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "size": 5172, "crc": -435968918}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "size": 4953, "crc": -764939908}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "size": 3857, "crc": -296816548}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "size": 3268, "crc": 391262463}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "size": 1568, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "size": 4089, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "size": 3718, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "name": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "size": 6127, "crc": -595244703}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "size": 1681, "crc": -386871225}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "size": 1676, "crc": **********}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "size": 1685, "crc": -2035011776}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "size": 1701, "crc": -1429057547}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "size": 1705, "crc": 916579093}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "size": 1701, "crc": 1716317934}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "size": 2098, "crc": 498271240}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "size": 2110, "crc": -1370756193}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "size": 2051, "crc": 2069626319}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "size": 6415, "crc": -215226175}, {"key": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "size": 1440, "crc": -812491820}, {"key": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "size": 2998, "crc": 1154465295}, {"key": "androidx/compose/foundation/layout/Arrangement$End$1.class", "name": "androidx/compose/foundation/layout/Arrangement$End$1.class", "size": 1758, "crc": 1415362784}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "size": 915, "crc": -1444014325}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "size": 2404, "crc": -1418279730}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "size": 965, "crc": -2042559948}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "size": 2334, "crc": 1298081256}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "size": 3038, "crc": -1857720198}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "size": 3046, "crc": 1795679867}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "size": 3038, "crc": -583048007}, {"key": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "name": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "size": 8460, "crc": -1871439136}, {"key": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "size": 1764, "crc": 1703004397}, {"key": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "size": 1425, "crc": -1212088326}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "size": 905, "crc": 2044147930}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "size": 2254, "crc": -1030538310}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "size": 2000, "crc": -1283038409}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "size": 1941, "crc": -1493289048}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "size": 2156, "crc": -621020140}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "size": 2012, "crc": 910345398}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "size": 1953, "crc": -578099345}, {"key": "androidx/compose/foundation/layout/Arrangement.class", "name": "androidx/compose/foundation/layout/Arrangement.class", "size": 17342, "crc": 1208876300}, {"key": "androidx/compose/foundation/layout/AspectRatioElement.class", "name": "androidx/compose/foundation/layout/AspectRatioElement.class", "size": 5326, "crc": -1309097377}, {"key": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "size": 2986, "crc": 1522063784}, {"key": "androidx/compose/foundation/layout/AspectRatioKt.class", "name": "androidx/compose/foundation/layout/AspectRatioKt.class", "size": 3193, "crc": -505325753}, {"key": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "name": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "size": 1859, "crc": -1167997699}, {"key": "androidx/compose/foundation/layout/AspectRatioNode.class", "name": "androidx/compose/foundation/layout/AspectRatioNode.class", "size": 11121, "crc": 1184063700}, {"key": "androidx/compose/foundation/layout/BoxChildDataElement.class", "name": "androidx/compose/foundation/layout/BoxChildDataElement.class", "size": 4229, "crc": -2033004221}, {"key": "androidx/compose/foundation/layout/BoxChildDataNode.class", "name": "androidx/compose/foundation/layout/BoxChildDataNode.class", "size": 2294, "crc": -894508465}, {"key": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "name": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "size": 1576, "crc": -1752003150}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "size": 1647, "crc": -1824991368}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "size": 2011, "crc": 1417200495}, {"key": "androidx/compose/foundation/layout/BoxKt.class", "name": "androidx/compose/foundation/layout/BoxKt.class", "size": 16722, "crc": -133775106}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "size": 1616, "crc": -1382890709}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "size": 2763, "crc": 779491949}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "size": 4800, "crc": 371644084}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "size": 8643, "crc": -196236872}, {"key": "androidx/compose/foundation/layout/BoxScope.class", "name": "androidx/compose/foundation/layout/BoxScope.class", "size": 1071, "crc": -937101109}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "size": 2642, "crc": 283587339}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "size": 2486, "crc": 843702791}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance.class", "size": 3514, "crc": 1755918393}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "size": 3220, "crc": -1660592314}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "size": 4401, "crc": 1531634249}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "size": 2519, "crc": -487341346}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "size": 6364, "crc": 41455776}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "size": 1146, "crc": -1774788311}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "size": 6624, "crc": 2035599739}, {"key": "androidx/compose/foundation/layout/ColumnKt.class", "name": "androidx/compose/foundation/layout/ColumnKt.class", "size": 11222, "crc": -2000777240}, {"key": "androidx/compose/foundation/layout/ColumnMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/ColumnMeasurePolicy$placeHelper$1$1.class", "size": 4348, "crc": 1829608313}, {"key": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "size": 11279, "crc": 1209161004}, {"key": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "size": 605, "crc": 1886136373}, {"key": "androidx/compose/foundation/layout/ColumnScope.class", "name": "androidx/compose/foundation/layout/ColumnScope.class", "size": 2644, "crc": 1772115533}, {"key": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "size": 4629, "crc": -176117418}, {"key": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "name": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "size": 2997, "crc": 218046034}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3727, "crc": 796641576}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 2995, "crc": -37230047}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3799, "crc": 1899832012}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3133, "crc": 820536000}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3794, "crc": -183442357}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3128, "crc": 317671302}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "size": 9341, "crc": -1956129714}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "size": 3899, "crc": -496184463}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "size": 909, "crc": 656195564}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "size": 4093, "crc": 1435172778}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "size": 2133, "crc": 1320264980}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "size": 6193, "crc": -378732966}, {"key": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "name": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "size": 4977, "crc": 1609681990}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$1.class", "size": 3939, "crc": 1122805891}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "size": 4373, "crc": 1340578732}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$1.class", "size": 3897, "crc": 1277814707}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "size": 4290, "crc": -210654190}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "size": 22719, "crc": 365667067}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3697, "crc": 683615196}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 2971, "crc": -200448324}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3769, "crc": 1149425646}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3109, "crc": 814843568}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3764, "crc": -1624968509}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3104, "crc": -869746708}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "size": 9272, "crc": 1060716084}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "size": 3884, "crc": 64115172}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "size": 897, "crc": 1127096519}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "size": 4289, "crc": -1575660799}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "size": 2112, "crc": -475333307}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "size": 6307, "crc": 100655411}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "size": 2739, "crc": 651545722}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "size": 1540, "crc": -796573243}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "size": 4203, "crc": 233393087}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "size": 1629, "crc": -2140151547}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "size": 3544, "crc": 1694149531}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "size": 1635, "crc": 1178043301}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "size": 3467, "crc": -2093117278}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "size": 3879, "crc": 1326988089}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "size": 1667, "crc": 1114020985}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "size": 1882, "crc": 882823869}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "size": 7925, "crc": 918190182}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "size": 1664, "crc": -984781061}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "size": 1879, "crc": 1212773291}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "size": 8166, "crc": -1327193338}, {"key": "androidx/compose/foundation/layout/Direction.class", "name": "androidx/compose/foundation/layout/Direction.class", "size": 1925, "crc": 366452626}, {"key": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "size": 906, "crc": 377711156}, {"key": "androidx/compose/foundation/layout/ExcludeInsets.class", "name": "androidx/compose/foundation/layout/ExcludeInsets.class", "size": 3029, "crc": 392493546}, {"key": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "name": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "size": 831, "crc": 1773866412}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "size": 3373, "crc": -1391595908}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "size": 2940, "crc": 816707527}, {"key": "androidx/compose/foundation/layout/FillElement$Companion.class", "name": "androidx/compose/foundation/layout/FillElement$Companion.class", "size": 1827, "crc": -1178651953}, {"key": "androidx/compose/foundation/layout/FillElement.class", "name": "androidx/compose/foundation/layout/FillElement.class", "size": 3837, "crc": -1538173209}, {"key": "androidx/compose/foundation/layout/FillNode$measure$1.class", "name": "androidx/compose/foundation/layout/FillNode$measure$1.class", "size": 1831, "crc": 1459788486}, {"key": "androidx/compose/foundation/layout/FillNode.class", "name": "androidx/compose/foundation/layout/FillNode.class", "size": 5529, "crc": 1538463138}, {"key": "androidx/compose/foundation/layout/FixedDpInsets.class", "name": "androidx/compose/foundation/layout/FixedDpInsets.class", "size": 4418, "crc": 361498080}, {"key": "androidx/compose/foundation/layout/FixedIntInsets.class", "name": "androidx/compose/foundation/layout/FixedIntInsets.class", "size": 2898, "crc": 480696944}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3627, "crc": 1907488521}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 2915, "crc": -2053420160}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3699, "crc": -557772092}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3053, "crc": -258926685}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3694, "crc": 2103234505}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3048, "crc": -1438539411}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "size": 9092, "crc": 1345960387}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "size": 3846, "crc": 912419631}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "size": 1128, "crc": 883478053}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl$shownItemCount$2.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl$shownItemCount$2.class", "size": 1676, "crc": -678595379}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl$totalItemCount$2.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl$totalItemCount$2.class", "size": 1676, "crc": -786519949}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "size": 5308, "crc": 1445831427}, {"key": "androidx/compose/foundation/layout/FlowColumnScope.class", "name": "androidx/compose/foundation/layout/FlowColumnScope.class", "size": 1597, "crc": 1362397565}, {"key": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "size": 5069, "crc": 779180356}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "size": 2846, "crc": -919145678}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "size": 1467, "crc": 229548334}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "size": 7139, "crc": 1881840902}, {"key": "androidx/compose/foundation/layout/FlowLayoutData.class", "name": "androidx/compose/foundation/layout/FlowLayoutData.class", "size": 2430, "crc": -1294071814}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$1.class", "size": 3716, "crc": 1974693864}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$2.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$2.class", "size": 3443, "crc": -1562260918}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "size": 3595, "crc": -1579334389}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$1.class", "size": 3674, "crc": 634093213}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$2.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$2.class", "size": 3413, "crc": -1756713810}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "size": 3564, "crc": -1020612678}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1$1.class", "size": 1986, "crc": -1905611745}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1$1.class", "size": 2004, "crc": 2119659275}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "size": 2893, "crc": 1104790226}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$placeHelper$3.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$placeHelper$3.class", "size": 3402, "crc": 2145598370}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "size": 2877, "crc": -1977433545}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt.class", "size": 69398, "crc": 1083602569}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "size": 2281, "crc": -1109330386}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "size": 972, "crc": -1146773602}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "size": 6867, "crc": -1182812148}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowKt.class", "size": 1406, "crc": 2104276546}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "size": 1132, "crc": 1885470304}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$3$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$3$1.class", "size": 2989, "crc": -1892879467}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$4$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$4$1.class", "size": 2991, "crc": 1345959254}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "size": 14661, "crc": -1834929897}, {"key": "androidx/compose/foundation/layout/FlowLineInfo.class", "name": "androidx/compose/foundation/layout/FlowLineInfo.class", "size": 3882, "crc": 884253624}, {"key": "androidx/compose/foundation/layout/FlowLineMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLineMeasurePolicy$placeHelper$1$1.class", "size": 3209, "crc": -40855514}, {"key": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "size": 6778, "crc": 85623239}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$getMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$getMeasurePolicy$1.class", "size": 2150, "crc": 309951734}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$1.class", "size": 1642, "crc": -919874625}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$2.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$2.class", "size": 2922, "crc": -28089564}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "size": 3067, "crc": -999010341}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1.class", "size": 2837, "crc": 1010563153}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "size": 15811, "crc": -1253367427}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "size": 1626, "crc": 207442069}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$2.class", "size": 1626, "crc": -1645596294}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "size": 26489, "crc": 731422123}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3596, "crc": -2008113438}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 2891, "crc": -1305678654}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3668, "crc": 1750585961}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3029, "crc": 563788564}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3663, "crc": -237270950}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3024, "crc": -1398360245}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "size": 8927, "crc": 182109349}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow.class", "size": 3831, "crc": -1720266609}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "size": 1116, "crc": -1074670698}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl$shownItemCount$2.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl$shownItemCount$2.class", "size": 1658, "crc": 667453047}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl$totalItemCount$2.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl$totalItemCount$2.class", "size": 1658, "crc": 1846906624}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "size": 5492, "crc": -1597476362}, {"key": "androidx/compose/foundation/layout/FlowRowScope.class", "name": "androidx/compose/foundation/layout/FlowRowScope.class", "size": 1576, "crc": 2033158072}, {"key": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "size": 5183, "crc": 641525701}, {"key": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "name": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "size": 3412, "crc": -1637521632}, {"key": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "name": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "size": 3206, "crc": -869424635}, {"key": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "size": 5331, "crc": 1193417919}, {"key": "androidx/compose/foundation/layout/InsetsListener.class", "name": "androidx/compose/foundation/layout/InsetsListener.class", "size": 5810, "crc": -2040277399}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "size": 1970, "crc": -1476536810}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "size": 8448, "crc": 1479038586}, {"key": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "name": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "size": 4678, "crc": 1497553148}, {"key": "androidx/compose/foundation/layout/InsetsValues.class", "name": "androidx/compose/foundation/layout/InsetsValues.class", "size": 2350, "crc": -1687790463}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "size": 4263, "crc": -428815387}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "size": 3525, "crc": 1111903067}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "size": 2862, "crc": -726935282}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "size": 2904, "crc": 1683023638}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "size": 2899, "crc": 2142683273}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "size": 2857, "crc": 30321206}, {"key": "androidx/compose/foundation/layout/IntrinsicKt.class", "name": "androidx/compose/foundation/layout/IntrinsicKt.class", "size": 3906, "crc": -109351738}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "size": 16720, "crc": -728386508}, {"key": "androidx/compose/foundation/layout/IntrinsicSize.class", "name": "androidx/compose/foundation/layout/IntrinsicSize.class", "size": 1888, "crc": 136479672}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "size": 2089, "crc": 318687341}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "size": 3950, "crc": -537761188}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "size": 4253, "crc": -177097532}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "size": 3508, "crc": 989981515}, {"key": "androidx/compose/foundation/layout/LayoutOrientation.class", "name": "androidx/compose/foundation/layout/LayoutOrientation.class", "size": 1932, "crc": -1303518051}, {"key": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "name": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "size": 628, "crc": 1787128746}, {"key": "androidx/compose/foundation/layout/LayoutWeightElement.class", "name": "androidx/compose/foundation/layout/LayoutWeightElement.class", "size": 3671, "crc": 650713841}, {"key": "androidx/compose/foundation/layout/LayoutWeightNode.class", "name": "androidx/compose/foundation/layout/LayoutWeightNode.class", "size": 2758, "crc": -95030280}, {"key": "androidx/compose/foundation/layout/LazyImpl$Companion.class", "name": "androidx/compose/foundation/layout/LazyImpl$Companion.class", "size": 908, "crc": 1452731707}, {"key": "androidx/compose/foundation/layout/LazyImpl.class", "name": "androidx/compose/foundation/layout/LazyImpl.class", "size": 2955, "crc": -1123732422}, {"key": "androidx/compose/foundation/layout/LimitInsets.class", "name": "androidx/compose/foundation/layout/LimitInsets.class", "size": 4575, "crc": -1054169816}, {"key": "androidx/compose/foundation/layout/MutableWindowInsets.class", "name": "androidx/compose/foundation/layout/MutableWindowInsets.class", "size": 4214, "crc": -1946108045}, {"key": "androidx/compose/foundation/layout/OffsetElement.class", "name": "androidx/compose/foundation/layout/OffsetElement.class", "size": 5007, "crc": -495117418}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "size": 1896, "crc": -728037883}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "size": 2049, "crc": -1711975320}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "size": 1864, "crc": -895094210}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "size": 2013, "crc": -1456866173}, {"key": "androidx/compose/foundation/layout/OffsetKt.class", "name": "androidx/compose/foundation/layout/OffsetKt.class", "size": 4251, "crc": -466414498}, {"key": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "size": 2442, "crc": -1458993957}, {"key": "androidx/compose/foundation/layout/OffsetNode.class", "name": "androidx/compose/foundation/layout/OffsetNode.class", "size": 4045, "crc": 36663335}, {"key": "androidx/compose/foundation/layout/OffsetPxElement.class", "name": "androidx/compose/foundation/layout/OffsetPxElement.class", "size": 4793, "crc": -715799655}, {"key": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "size": 2567, "crc": 45170946}, {"key": "androidx/compose/foundation/layout/OffsetPxNode.class", "name": "androidx/compose/foundation/layout/OffsetPxNode.class", "size": 4259, "crc": 546232184}, {"key": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "name": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "size": 7025, "crc": -273914715}, {"key": "androidx/compose/foundation/layout/PaddingElement.class", "name": "androidx/compose/foundation/layout/PaddingElement.class", "size": 8025, "crc": 534333463}, {"key": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "size": 2067, "crc": -725848683}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "size": 2033, "crc": 1473656921}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "size": 1904, "crc": -38407937}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "size": 1645, "crc": -1117695610}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "size": 1866, "crc": 1969912992}, {"key": "androidx/compose/foundation/layout/PaddingKt.class", "name": "androidx/compose/foundation/layout/PaddingKt.class", "size": 7894, "crc": 715241955}, {"key": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "name": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "size": 2454, "crc": -1634342741}, {"key": "androidx/compose/foundation/layout/PaddingNode.class", "name": "androidx/compose/foundation/layout/PaddingNode.class", "size": 5610, "crc": -1221991644}, {"key": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "name": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "size": 5877, "crc": 2056168152}, {"key": "androidx/compose/foundation/layout/PaddingValues.class", "name": "androidx/compose/foundation/layout/PaddingValues.class", "size": 1301, "crc": -683974033}, {"key": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "size": 2347, "crc": 1236978164}, {"key": "androidx/compose/foundation/layout/PaddingValuesElement.class", "name": "androidx/compose/foundation/layout/PaddingValuesElement.class", "size": 3952, "crc": 758049749}, {"key": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "name": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "size": 6217, "crc": -919402619}, {"key": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "name": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "size": 4385, "crc": -773198244}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "size": 1986, "crc": -1959222096}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "size": 5574, "crc": -830672402}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierElement.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierElement.class", "size": 2736, "crc": 258466355}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode$measure$1.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode$measure$1.class", "size": 1919, "crc": -228195782}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode$measure$2.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode$measure$2.class", "size": 8851, "crc": 1895766066}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode.class", "size": 7253, "crc": -725252426}, {"key": "androidx/compose/foundation/layout/RowColumnImplKt.class", "name": "androidx/compose/foundation/layout/RowColumnImplKt.class", "size": 8235, "crc": 1556170545}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "size": 2448, "crc": -1831010822}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "size": 12529, "crc": 912357853}, {"key": "androidx/compose/foundation/layout/RowColumnParentData.class", "name": "androidx/compose/foundation/layout/RowColumnParentData.class", "size": 5464, "crc": -760827617}, {"key": "androidx/compose/foundation/layout/RowKt.class", "name": "androidx/compose/foundation/layout/RowKt.class", "size": 11125, "crc": -2122723897}, {"key": "androidx/compose/foundation/layout/RowMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/RowMeasurePolicy$placeHelper$1$1.class", "size": 3962, "crc": -1697250739}, {"key": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "size": 11174, "crc": 2069930573}, {"key": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "size": 593, "crc": -207909827}, {"key": "androidx/compose/foundation/layout/RowScope.class", "name": "androidx/compose/foundation/layout/RowScope.class", "size": 2763, "crc": -1953795728}, {"key": "androidx/compose/foundation/layout/RowScopeInstance.class", "name": "androidx/compose/foundation/layout/RowScopeInstance.class", "size": 4877, "crc": 571307756}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "size": 3511, "crc": -344427308}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "size": 3116, "crc": 464954635}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "size": 1965, "crc": 1867945399}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "size": 4311, "crc": -689601362}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "size": 4295, "crc": 1881187882}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "size": 4302, "crc": 77159204}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "size": 4292, "crc": -1490918821}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "size": 3498, "crc": 803766884}, {"key": "androidx/compose/foundation/layout/SideCalculator.class", "name": "androidx/compose/foundation/layout/SideCalculator.class", "size": 2041, "crc": -1473303994}, {"key": "androidx/compose/foundation/layout/SizeElement.class", "name": "androidx/compose/foundation/layout/SizeElement.class", "size": 4853, "crc": -1183529818}, {"key": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2627, "crc": 2064128714}, {"key": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2871, "crc": -533834060}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2677, "crc": -1229257972}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2920, "crc": -1508442505}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2663, "crc": 1929543519}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2906, "crc": 1395346488}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3091, "crc": 139605174}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2670, "crc": -60293839}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2914, "crc": 2141591605}, {"key": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2615, "crc": -1327329355}, {"key": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2857, "crc": -874609975}, {"key": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3042, "crc": 661751900}, {"key": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2620, "crc": -1533256265}, {"key": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2865, "crc": -445321365}, {"key": "androidx/compose/foundation/layout/SizeKt.class", "name": "androidx/compose/foundation/layout/SizeKt.class", "size": 17890, "crc": 1228374577}, {"key": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "name": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "size": 1831, "crc": -1431023575}, {"key": "androidx/compose/foundation/layout/SizeNode.class", "name": "androidx/compose/foundation/layout/SizeNode.class", "size": 11168, "crc": 1024267776}, {"key": "androidx/compose/foundation/layout/SpacerKt.class", "name": "androidx/compose/foundation/layout/SpacerKt.class", "size": 5849, "crc": -54139860}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "size": 1632, "crc": 78246289}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "size": 2543, "crc": 1179413981}, {"key": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "size": 3355, "crc": -717680472}, {"key": "androidx/compose/foundation/layout/UnionInsets.class", "name": "androidx/compose/foundation/layout/UnionInsets.class", "size": 2996, "crc": -1263469958}, {"key": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "size": 2145, "crc": -1970495872}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "size": 4085, "crc": -2024785759}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "size": 1885, "crc": -800501482}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "size": 8173, "crc": -105224839}, {"key": "androidx/compose/foundation/layout/ValueInsets.class", "name": "androidx/compose/foundation/layout/ValueInsets.class", "size": 5089, "crc": 1995398775}, {"key": "androidx/compose/foundation/layout/VerticalAlignElement.class", "name": "androidx/compose/foundation/layout/VerticalAlignElement.class", "size": 3388, "crc": -1955722537}, {"key": "androidx/compose/foundation/layout/VerticalAlignNode.class", "name": "androidx/compose/foundation/layout/VerticalAlignNode.class", "size": 3180, "crc": 1569822733}, {"key": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "size": 757, "crc": 2018886019}, {"key": "androidx/compose/foundation/layout/WindowInsets.class", "name": "androidx/compose/foundation/layout/WindowInsets.class", "size": 1370, "crc": 1265458942}, {"key": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "name": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "size": 1163, "crc": -1510280921}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "size": 2634, "crc": -298558085}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "size": 3904, "crc": 1861074801}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1$invoke$$inlined$onDispose$1.class", "size": 2485, "crc": 791778446}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1.class", "size": 3281, "crc": -1641752779}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "size": 10027, "crc": 1023895747}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1$invoke$$inlined$onDispose$1.class", "size": 2381, "crc": 256698624}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1.class", "size": 3126, "crc": 1235619223}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "size": 8996, "crc": -40249056}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "size": 12470, "crc": 474480932}, {"key": "androidx/compose/foundation/layout/WindowInsetsKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsKt.class", "size": 7166, "crc": 1883092432}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "size": 1410, "crc": 1289142200}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "size": 1389, "crc": -47323001}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "size": 2269, "crc": -278046475}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "size": 3245, "crc": -1450427703}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "size": 4930, "crc": -196896938}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "size": 5572, "crc": 328890372}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "size": 2305, "crc": 604052438}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "size": 4997, "crc": 1086117698}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "size": 4512, "crc": -1454562577}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "size": 1442, "crc": 1010014817}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "size": 20121, "crc": -240285229}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "size": 1423, "crc": 812613868}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "size": 2990, "crc": -69290084}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "size": 3009, "crc": 2024336205}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "size": 4525, "crc": -689587771}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "size": 4538, "crc": -618422623}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "size": 2989, "crc": -2135166015}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "size": 4758, "crc": -161367835}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "size": 2990, "crc": 138869962}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "size": 4514, "crc": 1419067155}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "size": 6622, "crc": 1523300681}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "size": 2623, "crc": -300635535}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "size": 5856, "crc": -205930182}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "size": 2637, "crc": -1544229668}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "size": 5873, "crc": -1722468146}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "size": 2588, "crc": 1429431099}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "size": 5814, "crc": -757553990}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2688, "crc": -664604121}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5934, "crc": -531333803}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2643, "crc": 1630679933}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5880, "crc": 56102564}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "size": 2625, "crc": -548402855}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "size": 5796, "crc": -532069054}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "size": 2625, "crc": 405846243}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "size": 5796, "crc": -458116470}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2630, "crc": 2091322029}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5802, "crc": -361037544}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2623, "crc": -178001037}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5856, "crc": 1959741684}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2620, "crc": 348445433}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5853, "crc": 908367871}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2643, "crc": 622879995}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5880, "crc": 1820765378}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "size": 2618, "crc": 2133922075}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "size": 5842, "crc": -1771537214}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "size": 5635, "crc": -2116985346}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "size": 10606, "crc": -1234730515}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "size": 3081, "crc": -220207197}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides.class", "size": 5133, "crc": -1656400673}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "size": 3014, "crc": 671557398}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "size": 1947, "crc": 852832226}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "size": 2988, "crc": 458172798}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "size": 2336, "crc": -1228765756}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "size": 3000, "crc": 1393148665}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "size": 2342, "crc": 2136022630}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "size": 2996, "crc": -674587001}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "size": 1935, "crc": 1343521968}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "size": 5162, "crc": 809835829}, {"key": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "size": 18868, "crc": 1274440841}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "size": 3826, "crc": 1260181703}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "size": 3551, "crc": 1896198476}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "size": 3578, "crc": -381820530}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "size": 2249, "crc": -233219531}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "size": 3624, "crc": 1391761950}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "size": 3113, "crc": -492838398}, {"key": "androidx/compose/foundation/layout/WrapContentElement.class", "name": "androidx/compose/foundation/layout/WrapContentElement.class", "size": 5153, "crc": -521712271}, {"key": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "name": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "size": 3946, "crc": -1095534902}, {"key": "androidx/compose/foundation/layout/WrapContentNode.class", "name": "androidx/compose/foundation/layout/WrapContentNode.class", "size": 5284, "crc": 948428824}, {"key": "androidx/compose/foundation/layout/internal/InlineClassHelperKt.class", "name": "androidx/compose/foundation/layout/internal/InlineClassHelperKt.class", "size": 3239, "crc": -1047755144}, {"key": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 580, "crc": 267959146}, {"key": "META-INF/androidx.compose.foundation_foundation-layout.version", "name": "META-INF/androidx.compose.foundation_foundation-layout.version", "size": 6, "crc": 333960004}, {"key": "META-INF/foundation-layout_release.kotlin_module", "name": "META-INF/foundation-layout_release.kotlin_module", "size": 547, "crc": -778992538}]