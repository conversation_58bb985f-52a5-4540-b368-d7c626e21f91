-- Merging decision tree log ---
manifest
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:2:1-147:12
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:2:1-147:12
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:2:1-147:12
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:2:1-147:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2991897113f086a382dee19ca8ffd1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a1cdd54d8a34b0a25654daf371db4b\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2135603019f812910142dc3283fcd47d\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6b9980105991dbc4440732d8dc1d6\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8fbc995fb42104632c0197346495b2a\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd1af14c1d4e888972c1cf6a8e90a43\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1386be00d9a7e5ee4ad21ded08c440bd\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\541ef88f5273b8c27fa00406f560cd66\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60031a52c48d0c5482dc20d4c6acd10f\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\579e5f4b4789cbcfe415a7a2e1cebfa1\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13b67d1807c70a743f19d134201343fb\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b52b31ce7c4925c60b4b6406a02271\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a41851883761cbcf6a3a059d99d78c1a\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6763d2378a08d45315bd7079c8bbc10\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\29c4e80b68c78b371e0f1a9fbbe0e894\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92408c5a979bd5b5a46fef3fa24aad18\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7326da768cb0c7959dd5c663c7bede0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af36734c947ded337103e8b2fb2a14bc\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5870c877b557da0497556c4c5b78f0c5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4de1744565ebe041cb26ca7b8010109d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6203dfae908327a71d3e7b1205255c3\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0005607a0121ef3d1fa77a40990e09f9\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\145dc04648c370fc5888bbd6c784dc2c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe8d75863dc46170a2cbdbd222802706\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a31d8f40a371fafd44c8c90abefc3f7b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db837ceb7844d83f9738025fcb93bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7294e63d3866c632da01d4b0dc17b597\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14f1a181457d952cb8e798030501f71c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\359440fcf7f5a82864f0f58a2c2787a7\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab98fde903605a6a4b477570d932c39\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7fe4ed720ed06e7ffd30aedd97f7fd7\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\623e24116e3daa1775ee9d58e3bc08fd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c707ee6291c583fe5bbea7f4ec012c\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2c98f9e4700dc0461aa836e2e49c9\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\30e53ffe2fb6d65984d12c78b7f39145\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac3aaafd6082647e9848907cd01ce3f2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e72e0bba910be40df3c3f80fb3e94468\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffad78d84c5f9d2adbb6a50cd22b6a84\transformed\napier-release\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0736e70b913d66e64cc52f2ddcda1bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\093fc546952ee69a15ebabf9421682a7\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4601e53bc387bca4da2acf752c997d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.BLUETOOTH
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:5-18:31
	android:usesPermissionFlags
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:17:9-55
	tools:targetApi
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:18:9-28
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:22-70
uses-permission#android.permission.INTERNET
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:5-67
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:5-26:38
	android:maxSdkVersion
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:26:9-35
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:22-77
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:5-75
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:22-72
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:5-76
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:5-75
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:22-72
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:5-35:40
	tools:ignore
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:35:9-37
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:22-79
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:5-39:53
	tools:ignore
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:39:9-50
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:5-77
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:5-92
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:22-89
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:5-77
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:5-81
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:5-68
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:22-65
queries
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:55:5-57:15
package#cn.ykload.seeq
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:9-50
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:18-47
application
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:59:5-145:19
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:59:5-145:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:66:9-35
	android:label
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:64:9-41
	android:fullBackupContent
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:62:9-54
	android:roundIcon
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:65:9-54
	android:icon
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:63:9-43
	android:allowBackup
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:60:9-35
	android:theme
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:67:9-45
	android:dataExtractionRules
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:61:9-65
meta-data#android.media.audio_effect_app
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:70:9-72:36
	android:value
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:72:13-33
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:71:13-58
meta-data#android.media.audio_effect.equalizer
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:73:9-75:39
	android:value
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:75:13-36
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:74:13-64
meta-data#android.media.audio_effect.description
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:76:9-78:61
	android:value
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:78:13-58
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:77:13-66
activity#cn.ykload.flowmix.MainActivity
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:79:9-98:20
	android:label
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:82:13-45
	android:launchMode
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:84:13-43
	android:exported
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:81:13-36
	android:theme
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:83:13-49
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:80:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:85:13-89:29
action#android.intent.action.MAIN
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:17-69
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:17-77
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:27-74
intent-filter#action:name:cn.ykload.flowmix.notification.NotificationHelper.ACTION_OPEN_APP+action:name:cn.ykload.flowmix.notification.NotificationHelper.ACTION_TOGGLE_FLOWMIX+category:name:android.intent.category.DEFAULT
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:92:13-96:29
action#cn.ykload.flowmix.notification.NotificationHelper.ACTION_TOGGLE_FLOWMIX
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:17-114
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:25-111
action#cn.ykload.flowmix.notification.NotificationHelper.ACTION_OPEN_APP
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:17-108
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:25-105
category#android.intent.category.DEFAULT
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:17-76
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:27-73
activity#cn.ykload.flowmix.EqualizerActivity
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:101:9-123:20
	android:label
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:104:13-58
	android:launchMode
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:106:13-43
	android:exported
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:103:13-36
	android:theme
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:105:13-49
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:102:13-46
intent-filter#action:name:android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION+action:name:android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL+action:name:android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION+category:name:android.intent.category.DEFAULT
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:109:13-114:29
action#android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:17-97
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:25-94
action#android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:17-98
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:25-95
action#android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:17-98
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:25-95
meta-data#android.media.audio_effect
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:117:13-119:40
	android:value
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:119:17-37
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:118:17-58
service#cn.ykload.flowmix.service.FlowmixKeepAliveService
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:126:9-131:44
	android:enabled
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:128:13-35
	android:exported
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:129:13-37
	android:stopWithTask
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:131:13-41
	android:foregroundServiceType
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:130:13-58
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:127:13-60
receiver#cn.ykload.flowmix.receiver.BootReceiver
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:134:9-144:20
	android:enabled
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:136:13-35
	android:exported
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:137:13-36
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:135:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:13-143:29
	android:priority
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:17-79
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:17-84
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:17-81
	android:name
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:25-78
data
ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:17-50
	android:scheme
		ADDED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:23-47
uses-sdk
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2991897113f086a382dee19ca8ffd1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2991897113f086a382dee19ca8ffd1\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a1cdd54d8a34b0a25654daf371db4b\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\41a1cdd54d8a34b0a25654daf371db4b\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2135603019f812910142dc3283fcd47d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2135603019f812910142dc3283fcd47d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6b9980105991dbc4440732d8dc1d6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8c6b9980105991dbc4440732d8dc1d6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8fbc995fb42104632c0197346495b2a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8fbc995fb42104632c0197346495b2a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd1af14c1d4e888972c1cf6a8e90a43\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dd1af14c1d4e888972c1cf6a8e90a43\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1386be00d9a7e5ee4ad21ded08c440bd\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1386be00d9a7e5ee4ad21ded08c440bd\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\541ef88f5273b8c27fa00406f560cd66\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\541ef88f5273b8c27fa00406f560cd66\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60031a52c48d0c5482dc20d4c6acd10f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60031a52c48d0c5482dc20d4c6acd10f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\579e5f4b4789cbcfe415a7a2e1cebfa1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\579e5f4b4789cbcfe415a7a2e1cebfa1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13b67d1807c70a743f19d134201343fb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\13b67d1807c70a743f19d134201343fb\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b52b31ce7c4925c60b4b6406a02271\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2b52b31ce7c4925c60b4b6406a02271\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a41851883761cbcf6a3a059d99d78c1a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a41851883761cbcf6a3a059d99d78c1a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6763d2378a08d45315bd7079c8bbc10\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6763d2378a08d45315bd7079c8bbc10\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\29c4e80b68c78b371e0f1a9fbbe0e894\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\29c4e80b68c78b371e0f1a9fbbe0e894\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92408c5a979bd5b5a46fef3fa24aad18\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92408c5a979bd5b5a46fef3fa24aad18\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7326da768cb0c7959dd5c663c7bede0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7326da768cb0c7959dd5c663c7bede0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af36734c947ded337103e8b2fb2a14bc\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af36734c947ded337103e8b2fb2a14bc\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5870c877b557da0497556c4c5b78f0c5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5870c877b557da0497556c4c5b78f0c5\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4de1744565ebe041cb26ca7b8010109d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4de1744565ebe041cb26ca7b8010109d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6203dfae908327a71d3e7b1205255c3\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6203dfae908327a71d3e7b1205255c3\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0005607a0121ef3d1fa77a40990e09f9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0005607a0121ef3d1fa77a40990e09f9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\145dc04648c370fc5888bbd6c784dc2c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\145dc04648c370fc5888bbd6c784dc2c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe8d75863dc46170a2cbdbd222802706\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe8d75863dc46170a2cbdbd222802706\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a31d8f40a371fafd44c8c90abefc3f7b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a31d8f40a371fafd44c8c90abefc3f7b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db837ceb7844d83f9738025fcb93bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db837ceb7844d83f9738025fcb93bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7294e63d3866c632da01d4b0dc17b597\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7294e63d3866c632da01d4b0dc17b597\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14f1a181457d952cb8e798030501f71c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14f1a181457d952cb8e798030501f71c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\359440fcf7f5a82864f0f58a2c2787a7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\359440fcf7f5a82864f0f58a2c2787a7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab98fde903605a6a4b477570d932c39\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab98fde903605a6a4b477570d932c39\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7fe4ed720ed06e7ffd30aedd97f7fd7\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7fe4ed720ed06e7ffd30aedd97f7fd7\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\623e24116e3daa1775ee9d58e3bc08fd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\623e24116e3daa1775ee9d58e3bc08fd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c707ee6291c583fe5bbea7f4ec012c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c707ee6291c583fe5bbea7f4ec012c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2c98f9e4700dc0461aa836e2e49c9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\90f2c98f9e4700dc0461aa836e2e49c9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\30e53ffe2fb6d65984d12c78b7f39145\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\30e53ffe2fb6d65984d12c78b7f39145\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac3aaafd6082647e9848907cd01ce3f2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac3aaafd6082647e9848907cd01ce3f2\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e72e0bba910be40df3c3f80fb3e94468\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e72e0bba910be40df3c3f80fb3e94468\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffad78d84c5f9d2adbb6a50cd22b6a84\transformed\napier-release\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffad78d84c5f9d2adbb6a50cd22b6a84\transformed\napier-release\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0736e70b913d66e64cc52f2ddcda1bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0736e70b913d66e64cc52f2ddcda1bcf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\093fc546952ee69a15ebabf9421682a7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\093fc546952ee69a15ebabf9421682a7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\767263a25e5096722de245dfbcd090de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4601e53bc387bca4da2acf752c997d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4601e53bc387bca4da2acf752c997d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Projects\flowmix\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\699d8cc78fba3b42c7f1dbad9a421ee4\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
