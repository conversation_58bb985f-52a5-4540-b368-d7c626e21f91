# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 全局属性保护 - 防止泛型类型信息丢失
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes AnnotationDefault
-keepattributes EnclosingMethod
-keepattributes InnerClasses
-keepattributes Exceptions

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# 保护 Retrofit 接口和泛型类型
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson - 增强的泛型类型保护
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 保护泛型类型信息，防止 ParameterizedType 转换错误
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# 保护反射相关类，防止 ParameterizedType 错误
-keep class java.lang.reflect.** { *; }
-keep class kotlin.reflect.** { *; }
-dontwarn kotlin.reflect.**

# 保护 Kotlin 协程相关类
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**
-keep class kotlin.coroutines.** { *; }
-dontwarn kotlin.coroutines.**

# Keep data classes for JSON serialization - 增强保护
-keep class cn.ykload.flowmix.data.** { *; }
-keep class cn.ykload.flowmix.model.** { *; }
-keepclassmembers class cn.ykload.flowmix.data.** {
    <fields>;
    <init>(...);
}
-keepclassmembers class cn.ykload.flowmix.model.** {
    <fields>;
    <init>(...);
}

# 保护 API 接口的泛型信息
-keep interface cn.ykload.flowmix.network.** { *; }
-keepclassmembers interface cn.ykload.flowmix.network.** {
    <methods>;
}

# 保护 Response 包装类的泛型信息
-keep class retrofit2.Response { *; }
-keep class retrofit2.Call { *; }

# Compose
-keep class androidx.compose.** { *; }
-dontwarn androidx.compose.**

# 保护枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 额外的泛型和反射保护规则
-keep class * extends java.lang.reflect.ParameterizedType { *; }
-keep class * implements java.lang.reflect.Type { *; }
-keep class * implements java.lang.reflect.GenericDeclaration { *; }

# 保护 Kotlin 数据类的组件函数
-keepclassmembers class cn.ykload.flowmix.data.** {
    public ** component*();
    public ** copy(...);
}

# 保护序列化相关的构造函数
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# 防止混淆导致的类型转换问题
-keep,allowshrinking,allowoptimization class * extends java.lang.reflect.Type
-keep,allowshrinking,allowoptimization class * implements java.lang.reflect.ParameterizedType

# 保护 typealias 定义的类型
-keep class cn.ykload.flowmix.data.DataSourcesResponse { *; }
-keep class cn.ykload.flowmix.data.BrandsResponse { *; }
-keep class cn.ykload.flowmix.data.HeadphonesResponse { *; }
-keep class cn.ykload.flowmix.data.FrequencyDataResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurvesResponse { *; }
-keep class cn.ykload.flowmix.data.TargetCurveDataResponse { *; }