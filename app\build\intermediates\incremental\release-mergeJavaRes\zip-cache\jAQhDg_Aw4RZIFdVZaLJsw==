[{"key": "androidx/compose/material3/AlertDialogDefaults.class", "name": "androidx/compose/material3/AlertDialogDefaults.class", "size": 5293, "crc": -2123704473}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialog$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialog$1.class", "size": 2658, "crc": -1186725920}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "size": 9807, "crc": -1230339019}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "size": 10101, "crc": 1530602981}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$3$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$3$1.class", "size": 9911, "crc": 1891597628}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1.class", "size": 16542, "crc": 1021003422}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$2.class", "size": 3478, "crc": -635731110}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "size": 5725, "crc": 1808463973}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1.class", "size": 6900, "crc": 1997839896}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$2.class", "size": 2089, "crc": 1653969182}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1$1$1.class", "size": 3108, "crc": 1169323774}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1$1.class", "size": 3399, "crc": 1245235896}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$1.class", "size": 5289, "crc": -1248594052}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogImpl$2.class", "size": 4086, "crc": -1876635880}, {"key": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$1$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$1$1$1.class", "size": 1797, "crc": -1197564315}, {"key": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$1.class", "name": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$1.class", "size": 11510, "crc": -640733680}, {"key": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$2.class", "name": "androidx/compose/material3/AlertDialogKt$BasicAlertDialog$2.class", "size": 2673, "crc": -1439671473}, {"key": "androidx/compose/material3/AlertDialogKt.class", "name": "androidx/compose/material3/AlertDialogKt.class", "size": 23642, "crc": 155470344}, {"key": "androidx/compose/material3/AnalogTimePickerState$animateToCurrent$2.class", "name": "androidx/compose/material3/AnalogTimePickerState$animateToCurrent$2.class", "size": 4250, "crc": -1919796967}, {"key": "androidx/compose/material3/AnalogTimePickerState$onGestureEnd$2.class", "name": "androidx/compose/material3/AnalogTimePickerState$onGestureEnd$2.class", "size": 4225, "crc": 2133549317}, {"key": "androidx/compose/material3/AnalogTimePickerState$rotateTo$2.class", "name": "androidx/compose/material3/AnalogTimePickerState$rotateTo$2.class", "size": 5441, "crc": -981966635}, {"key": "androidx/compose/material3/AnalogTimePickerState.class", "name": "androidx/compose/material3/AnalogTimePickerState.class", "size": 11355, "crc": 1066722643}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1.class", "size": 4187, "crc": 2120901297}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt.class", "size": 8378, "crc": -1004218605}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$1.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$1.class", "size": 5039, "crc": -984246911}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$2.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$2.class", "size": 3778, "crc": -**********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$3.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$3.class", "size": 3126, "crc": 449584473}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$4.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$4.class", "size": 2906, "crc": 80601046}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "size": 2633, "crc": **********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenuItem$1.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenuItem$1.class", "size": 3779, "crc": -294054078}, {"key": "androidx/compose/material3/AndroidMenu_androidKt.class", "name": "androidx/compose/material3/AndroidMenu_androidKt.class", "size": 23541, "crc": **********}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$1.class", "size": 3314, "crc": -**********}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$2.class", "size": 13442, "crc": -438143715}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$3.class", "size": 3593, "crc": 927217295}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$4.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$4.class", "size": 2939, "crc": 2035281708}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1$1.class", "size": 1933, "crc": -1366149385}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1.class", "size": 3771, "crc": 165239233}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$6.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$6.class", "size": 10804, "crc": -1644941757}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$7.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$7.class", "size": 3218, "crc": 197710859}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$1$1.class", "size": 1927, "crc": 1196657400}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$2$1.class", "size": 4167, "crc": 318490552}, {"key": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$1.class", "size": 3604, "crc": -673849543}, {"key": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$2.class", "size": 3675, "crc": 974202198}, {"key": "androidx/compose/material3/AppBarKt$LargeTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$LargeTopAppBar$1.class", "size": 3580, "crc": 1386975975}, {"key": "androidx/compose/material3/AppBarKt$LargeTopAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$LargeTopAppBar$2.class", "size": 3711, "crc": -602136245}, {"key": "androidx/compose/material3/AppBarKt$MediumTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$MediumTopAppBar$1.class", "size": 3583, "crc": -1670779722}, {"key": "androidx/compose/material3/AppBarKt$MediumTopAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$MediumTopAppBar$2.class", "size": 3714, "crc": 1917445305}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$2$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$2$1.class", "size": 2159, "crc": 596573310}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$3.class", "size": 8845, "crc": -803532904}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$4.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$4.class", "size": 3969, "crc": 1239208498}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$actionsRow$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$actionsRow$1.class", "size": 9757, "crc": 146877234}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$1$1.class", "size": 2016, "crc": 919815738}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$2$1.class", "size": 4263, "crc": -738483126}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$colorTransitionFraction$2$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$colorTransitionFraction$2$1.class", "size": 2074, "crc": 2043144198}, {"key": "androidx/compose/material3/AppBarKt$TopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBar$1.class", "size": 3565, "crc": 296664928}, {"key": "androidx/compose/material3/AppBarKt$TopAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$TopAppBar$2.class", "size": 3636, "crc": -805569977}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$1$2.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$1$2.class", "size": 2133, "crc": 2092405869}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1$1.class", "size": 4926, "crc": 1123894151}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1.class", "size": 8209, "crc": 1507201710}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$3.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$3.class", "size": 4303, "crc": 834235157}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$5$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$5$1.class", "size": 2507, "crc": -23494752}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$6.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$6.class", "size": 16859, "crc": -1995613147}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$7.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$7.class", "size": 4475, "crc": -1348443375}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$actionsRow$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$actionsRow$1.class", "size": 9811, "crc": -338728090}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$1$1.class", "size": 2080, "crc": 30125025}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$2$1.class", "size": 4323, "crc": -1422133754}, {"key": "androidx/compose/material3/AppBarKt$rememberBottomAppBarState$1$1.class", "name": "androidx/compose/material3/AppBarKt$rememberBottomAppBarState$1$1.class", "size": 1578, "crc": 147672822}, {"key": "androidx/compose/material3/AppBarKt$rememberTopAppBarState$1$1.class", "name": "androidx/compose/material3/AppBarKt$rememberTopAppBarState$1$1.class", "size": 1534, "crc": -1642627609}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$1.class", "size": 1743, "crc": 1839050680}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$2.class", "size": 2882, "crc": 959757480}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$3.class", "size": 2212, "crc": -1633899139}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$1.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$1.class", "size": 1776, "crc": -1700027477}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$2.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$2.class", "size": 2918, "crc": 1657558390}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$3.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$3.class", "size": 2244, "crc": -1911525822}, {"key": "androidx/compose/material3/AppBarKt.class", "name": "androidx/compose/material3/AppBarKt.class", "size": 95956, "crc": 1860859765}, {"key": "androidx/compose/material3/AssistChipDefaults.class", "name": "androidx/compose/material3/AssistChipDefaults.class", "size": 14469, "crc": -1966095270}, {"key": "androidx/compose/material3/BadgeDefaults.class", "name": "androidx/compose/material3/BadgeDefaults.class", "size": 2199, "crc": 1611765614}, {"key": "androidx/compose/material3/BadgeKt$Badge$1$1.class", "name": "androidx/compose/material3/BadgeKt$Badge$1$1.class", "size": 3091, "crc": 350449689}, {"key": "androidx/compose/material3/BadgeKt$Badge$2.class", "name": "androidx/compose/material3/BadgeKt$Badge$2.class", "size": 2339, "crc": 1231716292}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$2$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$2$1.class", "size": 3071, "crc": -607507898}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$3$1$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$3$1$1.class", "size": 3960, "crc": -1120465635}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$3$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$3$1.class", "size": 6426, "crc": 176235455}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$4.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$4.class", "size": 2490, "crc": 199586632}, {"key": "androidx/compose/material3/BadgeKt.class", "name": "androidx/compose/material3/BadgeKt.class", "size": 28136, "crc": 1879770022}, {"key": "androidx/compose/material3/BaseDatePickerStateImpl.class", "name": "androidx/compose/material3/BaseDatePickerStateImpl.class", "size": 4774, "crc": -7416557}, {"key": "androidx/compose/material3/BottomAppBarDefaults$exitAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/BottomAppBarDefaults$exitAlwaysScrollBehavior$1.class", "size": 1489, "crc": -1572084942}, {"key": "androidx/compose/material3/BottomAppBarDefaults.class", "name": "androidx/compose/material3/BottomAppBarDefaults.class", "size": 8784, "crc": -1696914366}, {"key": "androidx/compose/material3/BottomAppBarScrollBehavior.class", "name": "androidx/compose/material3/BottomAppBarScrollBehavior.class", "size": 1752, "crc": 19888659}, {"key": "androidx/compose/material3/BottomAppBarState$Companion$Saver$1.class", "name": "androidx/compose/material3/BottomAppBarState$Companion$Saver$1.class", "size": 2273, "crc": 996102381}, {"key": "androidx/compose/material3/BottomAppBarState$Companion$Saver$2.class", "name": "androidx/compose/material3/BottomAppBarState$Companion$Saver$2.class", "size": 2002, "crc": -1752188772}, {"key": "androidx/compose/material3/BottomAppBarState$Companion.class", "name": "androidx/compose/material3/BottomAppBarState$Companion.class", "size": 1938, "crc": -491094483}, {"key": "androidx/compose/material3/BottomAppBarState.class", "name": "androidx/compose/material3/BottomAppBarState.class", "size": 1256, "crc": 1034424064}, {"key": "androidx/compose/material3/BottomAppBarStateImpl.class", "name": "androidx/compose/material3/BottomAppBarStateImpl.class", "size": 3837, "crc": -984483659}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$1$1.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$1$1.class", "size": 1878, "crc": -175193145}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$2.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$2.class", "size": 2887, "crc": -1167571614}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$3.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$3.class", "size": 2241, "crc": 992370044}, {"key": "androidx/compose/material3/BottomSheetDefaults.class", "name": "androidx/compose/material3/BottomSheetDefaults.class", "size": 13279, "crc": 1180791053}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "size": 3493, "crc": 260968974}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "size": 4645, "crc": 2051597136}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$3.class", "size": 3609, "crc": -94927359}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$4$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$4$1.class", "size": 1927, "crc": -1985551505}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$5.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$5.class", "size": 4865, "crc": 1812574511}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1.class", "size": 3469, "crc": 367330459}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1$WhenMappings.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1$WhenMappings.class", "size": 1048, "crc": 2093289069}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1.class", "size": 8158, "crc": -1622875762}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1.class", "size": 8668, "crc": -1393003438}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$3.class", "size": 3511, "crc": -1344741854}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$WhenMappings.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$WhenMappings.class", "size": 940, "crc": -865771518}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$newAnchors$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$newAnchors$1.class", "size": 2591, "crc": -1995174137}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1.class", "size": 4194, "crc": 1532350548}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$1$1.class", "size": 3807, "crc": 2145449313}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$1.class", "size": 2148, "crc": -685922456}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$2$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$2$1.class", "size": 3814, "crc": -1413804889}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$2.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$2.class", "size": 2148, "crc": -367268516}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$3$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$3$1.class", "size": 3805, "crc": -1212267252}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1$1$3.class", "size": 2148, "crc": 2120313416}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$1$1.class", "size": 4062, "crc": -1982414603}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2.class", "size": 16968, "crc": -1407833972}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3.class", "size": 3383, "crc": -634067393}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$nestedScroll$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$nestedScroll$1$1$1.class", "size": 3697, "crc": -37795137}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$nestedScroll$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$nestedScroll$1$1.class", "size": 2226, "crc": 1684320275}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$rememberStandardBottomSheetState$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$rememberStandardBottomSheetState$1.class", "size": 1696, "crc": -315034297}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt.class", "size": 37013, "crc": -822447905}, {"key": "androidx/compose/material3/BottomSheetScaffoldState.class", "name": "androidx/compose/material3/BottomSheetScaffoldState.class", "size": 1559, "crc": 1812546159}, {"key": "androidx/compose/material3/ButtonColors.class", "name": "androidx/compose/material3/ButtonColors.class", "size": 5517, "crc": -1366976476}, {"key": "androidx/compose/material3/ButtonDefaults.class", "name": "androidx/compose/material3/ButtonDefaults.class", "size": 23266, "crc": 1390009873}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$1$1$1.class", "size": 3691, "crc": -898653886}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$1$1.class", "size": 4445, "crc": -1585749775}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$2$1.class", "size": 6163, "crc": -1305618373}, {"key": "androidx/compose/material3/ButtonElevation.class", "name": "androidx/compose/material3/ButtonElevation.class", "size": 10139, "crc": 263878930}, {"key": "androidx/compose/material3/ButtonKt$Button$1.class", "name": "androidx/compose/material3/ButtonKt$Button$1.class", "size": 2278, "crc": 1845020220}, {"key": "androidx/compose/material3/ButtonKt$Button$2$1.class", "name": "androidx/compose/material3/ButtonKt$Button$2$1.class", "size": 10115, "crc": 181175783}, {"key": "androidx/compose/material3/ButtonKt$Button$2.class", "name": "androidx/compose/material3/ButtonKt$Button$2.class", "size": 4263, "crc": -2134635691}, {"key": "androidx/compose/material3/ButtonKt$Button$3.class", "name": "androidx/compose/material3/ButtonKt$Button$3.class", "size": 3969, "crc": -348558505}, {"key": "androidx/compose/material3/ButtonKt$ElevatedButton$1.class", "name": "androidx/compose/material3/ButtonKt$ElevatedButton$1.class", "size": 3993, "crc": -237841516}, {"key": "androidx/compose/material3/ButtonKt$FilledTonalButton$1.class", "name": "androidx/compose/material3/ButtonKt$FilledTonalButton$1.class", "size": 4002, "crc": 558750610}, {"key": "androidx/compose/material3/ButtonKt$OutlinedButton$1.class", "name": "androidx/compose/material3/ButtonKt$OutlinedButton$1.class", "size": 3993, "crc": -1391001018}, {"key": "androidx/compose/material3/ButtonKt$TextButton$1.class", "name": "androidx/compose/material3/ButtonKt$TextButton$1.class", "size": 3981, "crc": 1931298704}, {"key": "androidx/compose/material3/ButtonKt.class", "name": "androidx/compose/material3/ButtonKt.class", "size": 20548, "crc": 1695437657}, {"key": "androidx/compose/material3/CalendarLocale_androidKt.class", "name": "androidx/compose/material3/CalendarLocale_androidKt.class", "size": 3816, "crc": -1492598728}, {"key": "androidx/compose/material3/CalendarLocale_jvmKt.class", "name": "androidx/compose/material3/CalendarLocale_jvmKt.class", "size": 3788, "crc": 1873171545}, {"key": "androidx/compose/material3/CardColors.class", "name": "androidx/compose/material3/CardColors.class", "size": 5427, "crc": 1865811832}, {"key": "androidx/compose/material3/CardDefaults.class", "name": "androidx/compose/material3/CardDefaults.class", "size": 16017, "crc": -1955489511}, {"key": "androidx/compose/material3/CardElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$1$1$1.class", "size": 4174, "crc": 2133214146}, {"key": "androidx/compose/material3/CardElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$1$1.class", "size": 4431, "crc": -1542516513}, {"key": "androidx/compose/material3/CardElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$2$1.class", "size": 6365, "crc": -586929625}, {"key": "androidx/compose/material3/CardElevation.class", "name": "androidx/compose/material3/CardElevation.class", "size": 11197, "crc": -1432690686}, {"key": "androidx/compose/material3/CardKt$Card$1.class", "name": "androidx/compose/material3/CardKt$Card$1.class", "size": 9360, "crc": -1732167560}, {"key": "androidx/compose/material3/CardKt$Card$2.class", "name": "androidx/compose/material3/CardKt$Card$2.class", "size": 3051, "crc": 257619147}, {"key": "androidx/compose/material3/CardKt$Card$3.class", "name": "androidx/compose/material3/CardKt$Card$3.class", "size": 9485, "crc": -1315141113}, {"key": "androidx/compose/material3/CardKt$Card$4.class", "name": "androidx/compose/material3/CardKt$Card$4.class", "size": 3686, "crc": -823291888}, {"key": "androidx/compose/material3/CardKt$ElevatedCard$1.class", "name": "androidx/compose/material3/CardKt$ElevatedCard$1.class", "size": 2856, "crc": -1797534677}, {"key": "androidx/compose/material3/CardKt$ElevatedCard$2.class", "name": "androidx/compose/material3/CardKt$ElevatedCard$2.class", "size": 3491, "crc": -259785014}, {"key": "androidx/compose/material3/CardKt$OutlinedCard$1.class", "name": "androidx/compose/material3/CardKt$OutlinedCard$1.class", "size": 3075, "crc": 603259995}, {"key": "androidx/compose/material3/CardKt$OutlinedCard$2.class", "name": "androidx/compose/material3/CardKt$OutlinedCard$2.class", "size": 3710, "crc": -526112046}, {"key": "androidx/compose/material3/CardKt.class", "name": "androidx/compose/material3/CardKt.class", "size": 21203, "crc": 317618102}, {"key": "androidx/compose/material3/CaretType.class", "name": "androidx/compose/material3/CaretType.class", "size": 1478, "crc": -464127981}, {"key": "androidx/compose/material3/CenteredContentMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/CenteredContentMeasurePolicy$measure$1.class", "size": 1738, "crc": 903928172}, {"key": "androidx/compose/material3/CenteredContentMeasurePolicy$measure$5.class", "name": "androidx/compose/material3/CenteredContentMeasurePolicy$measure$5.class", "size": 3709, "crc": -1774379069}, {"key": "androidx/compose/material3/CenteredContentMeasurePolicy.class", "name": "androidx/compose/material3/CenteredContentMeasurePolicy.class", "size": 6685, "crc": 563858972}, {"key": "androidx/compose/material3/CheckDrawingCache.class", "name": "androidx/compose/material3/CheckDrawingCache.class", "size": 2134, "crc": -1429477041}, {"key": "androidx/compose/material3/CheckboxColors$WhenMappings.class", "name": "androidx/compose/material3/CheckboxColors$WhenMappings.class", "size": 858, "crc": -1359544710}, {"key": "androidx/compose/material3/CheckboxColors.class", "name": "androidx/compose/material3/CheckboxColors.class", "size": 15674, "crc": 1653053575}, {"key": "androidx/compose/material3/CheckboxDefaults.class", "name": "androidx/compose/material3/CheckboxDefaults.class", "size": 5680, "crc": 744560516}, {"key": "androidx/compose/material3/CheckboxKt$Checkbox$1$1.class", "name": "androidx/compose/material3/CheckboxKt$Checkbox$1$1.class", "size": 1782, "crc": -1544789409}, {"key": "androidx/compose/material3/CheckboxKt$Checkbox$2.class", "name": "androidx/compose/material3/CheckboxKt$Checkbox$2.class", "size": 2734, "crc": 1457705643}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$1$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$1$1.class", "size": 3513, "crc": 1460877131}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$2.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$2.class", "size": 2132, "crc": 1668283404}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$1.class", "size": 3832, "crc": 1138809687}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkDrawFraction$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkDrawFraction$1.class", "size": 3822, "crc": -389758544}, {"key": "androidx/compose/material3/CheckboxKt$TriStateCheckbox$1.class", "name": "androidx/compose/material3/CheckboxKt$TriStateCheckbox$1.class", "size": 2881, "crc": 2097045747}, {"key": "androidx/compose/material3/CheckboxKt$WhenMappings.class", "name": "androidx/compose/material3/CheckboxKt$WhenMappings.class", "size": 850, "crc": -1502526014}, {"key": "androidx/compose/material3/CheckboxKt.class", "name": "androidx/compose/material3/CheckboxKt.class", "size": 25983, "crc": 600518559}, {"key": "androidx/compose/material3/ChipBorder.class", "name": "androidx/compose/material3/ChipBorder.class", "size": 3854, "crc": -1786759953}, {"key": "androidx/compose/material3/ChipColors.class", "name": "androidx/compose/material3/ChipColors.class", "size": 8764, "crc": -1159644272}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$1$1$1.class", "size": 4174, "crc": 490165544}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$1$1.class", "size": 4431, "crc": 1589266137}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$2$1.class", "size": 5450, "crc": -793354639}, {"key": "androidx/compose/material3/ChipElevation.class", "name": "androidx/compose/material3/ChipElevation.class", "size": 12591, "crc": -1398053780}, {"key": "androidx/compose/material3/ChipKt$AssistChip$1.class", "name": "androidx/compose/material3/ChipKt$AssistChip$1.class", "size": 4119, "crc": 488953344}, {"key": "androidx/compose/material3/ChipKt$AssistChip$3.class", "name": "androidx/compose/material3/ChipKt$AssistChip$3.class", "size": 4107, "crc": 898472268}, {"key": "androidx/compose/material3/ChipKt$Chip$1.class", "name": "androidx/compose/material3/ChipKt$Chip$1.class", "size": 2370, "crc": -412723570}, {"key": "androidx/compose/material3/ChipKt$Chip$2.class", "name": "androidx/compose/material3/ChipKt$Chip$2.class", "size": 4638, "crc": -27396136}, {"key": "androidx/compose/material3/ChipKt$Chip$3.class", "name": "androidx/compose/material3/ChipKt$Chip$3.class", "size": 4661, "crc": 697547913}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1$1$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1$1$1.class", "size": 2895, "crc": -1060825567}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1$1.class", "size": 6587, "crc": 959273204}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1.class", "size": 18849, "crc": -378858567}, {"key": "androidx/compose/material3/ChipKt$ChipContent$2.class", "name": "androidx/compose/material3/ChipKt$ChipContent$2.class", "size": 3328, "crc": 2065876200}, {"key": "androidx/compose/material3/ChipKt$ElevatedAssistChip$1.class", "name": "androidx/compose/material3/ChipKt$ElevatedAssistChip$1.class", "size": 4143, "crc": -586255957}, {"key": "androidx/compose/material3/ChipKt$ElevatedAssistChip$3.class", "name": "androidx/compose/material3/ChipKt$ElevatedAssistChip$3.class", "size": 4131, "crc": 1130894764}, {"key": "androidx/compose/material3/ChipKt$ElevatedFilterChip$1.class", "name": "androidx/compose/material3/ChipKt$ElevatedFilterChip$1.class", "size": 4276, "crc": 169817149}, {"key": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$1.class", "name": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$1.class", "size": 3862, "crc": -196414571}, {"key": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$3.class", "name": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$3.class", "size": 3850, "crc": 1118208504}, {"key": "androidx/compose/material3/ChipKt$FilterChip$1.class", "name": "androidx/compose/material3/ChipKt$FilterChip$1.class", "size": 4252, "crc": -328345902}, {"key": "androidx/compose/material3/ChipKt$InputChip$1$1$1.class", "name": "androidx/compose/material3/ChipKt$InputChip$1$1$1.class", "size": 1787, "crc": 727769763}, {"key": "androidx/compose/material3/ChipKt$InputChip$1.class", "name": "androidx/compose/material3/ChipKt$InputChip$1.class", "size": 10643, "crc": 1412053453}, {"key": "androidx/compose/material3/ChipKt$InputChip$2.class", "name": "androidx/compose/material3/ChipKt$InputChip$2.class", "size": 4472, "crc": 1757451182}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$1.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$1.class", "size": 2454, "crc": 2115269581}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$2.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$2.class", "size": 5043, "crc": -2005119188}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$3.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$3.class", "size": 4998, "crc": -211274663}, {"key": "androidx/compose/material3/ChipKt$SuggestionChip$1.class", "name": "androidx/compose/material3/ChipKt$SuggestionChip$1.class", "size": 3838, "crc": -1286725393}, {"key": "androidx/compose/material3/ChipKt$SuggestionChip$3.class", "name": "androidx/compose/material3/ChipKt$SuggestionChip$3.class", "size": 3826, "crc": 1538380414}, {"key": "androidx/compose/material3/ChipKt.class", "name": "androidx/compose/material3/ChipKt.class", "size": 71275, "crc": 859680542}, {"key": "androidx/compose/material3/ClockDialModifier.class", "name": "androidx/compose/material3/ClockDialModifier.class", "size": 5179, "crc": -2028422851}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1$1.class", "size": 4225, "crc": 1218139532}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1.class", "size": 1875, "crc": -1071001474}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2$1.class", "size": 4648, "crc": -336584135}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2.class", "size": 3100, "crc": 1168235000}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1.class", "size": 4249, "crc": 1244097955}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$1.class", "size": 3620, "crc": -1737721676}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2$1.class", "size": 4299, "crc": -1372343766}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2.class", "size": 2104, "crc": 201752666}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1.class", "size": 4280, "crc": -1440271970}, {"key": "androidx/compose/material3/ClockDialNode$updateNode$1.class", "name": "androidx/compose/material3/ClockDialNode$updateNode$1.class", "size": 3553, "crc": 438725413}, {"key": "androidx/compose/material3/ClockDialNode.class", "name": "androidx/compose/material3/ClockDialNode.class", "size": 7385, "crc": -375334561}, {"key": "androidx/compose/material3/ColorResourceHelper.class", "name": "androidx/compose/material3/ColorResourceHelper.class", "size": 1665, "crc": 1049142708}, {"key": "androidx/compose/material3/ColorScheme.class", "name": "androidx/compose/material3/ColorScheme.class", "size": 37595, "crc": 727791908}, {"key": "androidx/compose/material3/ColorSchemeKt$LocalColorScheme$1.class", "name": "androidx/compose/material3/ColorSchemeKt$LocalColorScheme$1.class", "size": 1430, "crc": 2037039799}, {"key": "androidx/compose/material3/ColorSchemeKt$LocalTonalElevationEnabled$1.class", "name": "androidx/compose/material3/ColorSchemeKt$LocalTonalElevationEnabled$1.class", "size": 1257, "crc": 1735356058}, {"key": "androidx/compose/material3/ColorSchemeKt$WhenMappings.class", "name": "androidx/compose/material3/ColorSchemeKt$WhenMappings.class", "size": 2707, "crc": -1519621025}, {"key": "androidx/compose/material3/ColorSchemeKt.class", "name": "androidx/compose/material3/ColorSchemeKt.class", "size": 21487, "crc": -1318507019}, {"key": "androidx/compose/material3/CompatRippleTheme.class", "name": "androidx/compose/material3/CompatRippleTheme.class", "size": 4121, "crc": -1157600949}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-1$1.class", "size": 2179, "crc": -477232057}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-10$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-10$1.class", "size": 2486, "crc": -1829377051}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-11$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-11$1.class", "size": 2182, "crc": -1069614277}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-12$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-12$1.class", "size": 2486, "crc": -691396178}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-13$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-13$1.class", "size": 2182, "crc": -50936908}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-14$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-14$1.class", "size": 2486, "crc": 1442969181}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-15$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-15$1.class", "size": 2182, "crc": -1654980632}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-16$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-16$1.class", "size": 2486, "crc": 285360309}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-17$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-17$1.class", "size": 2283, "crc": -988194310}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-18$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-18$1.class", "size": 2283, "crc": -766759700}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-2$1.class", "size": 2483, "crc": 878589215}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-3$1.class", "size": 2179, "crc": -52360126}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-4$1.class", "size": 2483, "crc": 1544679624}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-5$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-5$1.class", "size": 2179, "crc": 407227618}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-6$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-6$1.class", "size": 2483, "crc": 349057376}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-7$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-7$1.class", "size": 2179, "crc": -401049174}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-8$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-8$1.class", "size": 2483, "crc": -428807471}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-9$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-9$1.class", "size": 2179, "crc": 1289316194}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt.class", "size": 7639, "crc": 1212931123}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "size": 2658, "crc": -1951082314}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "size": 3063, "crc": 912058144}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "size": 2270, "crc": 269253590}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt.class", "size": 2682, "crc": -58742958}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-1$1.class", "size": 4309, "crc": -1527911641}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-2$1.class", "size": 4325, "crc": 963970091}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-3$1.class", "size": 4474, "crc": -1950338790}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-4$1.class", "size": 4466, "crc": -482454270}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt.class", "size": 2535, "crc": 690057189}, {"key": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt$lambda-1$1.class", "size": 2839, "crc": 926847644}, {"key": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt.class", "size": 1528, "crc": 1885858662}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-1$1.class", "size": 2193, "crc": 2027286709}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-2$1.class", "size": 2193, "crc": -2083053085}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-3$1.class", "size": 2193, "crc": 2138777719}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-4$1.class", "size": 2193, "crc": -1510842345}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt.class", "size": 2513, "crc": 902649806}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt$lambda-1$1.class", "size": 2637, "crc": -1280694787}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt$lambda-2$1.class", "size": 2637, "crc": 744066446}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheetKt.class", "size": 1889, "crc": 850344928}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-1$1.class", "size": 2694, "crc": 140671400}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-2$1.class", "size": 2305, "crc": 1636358285}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt.class", "size": 1945, "crc": -1252986181}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "size": 2192, "crc": -255794629}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "size": 2192, "crc": -2062067625}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "size": 2192, "crc": 437945148}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "size": 2192, "crc": 429327694}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt.class", "size": 2513, "crc": -578717641}, {"key": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-1$1.class", "size": 2356, "crc": -2091156094}, {"key": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-2$1.class", "size": 2356, "crc": -183130190}, {"key": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt.class", "name": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt.class", "size": 1896, "crc": -1315034462}, {"key": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt$lambda-1$1.class", "size": 2507, "crc": -1546538027}, {"key": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt.class", "name": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt.class", "size": 1528, "crc": -905444759}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "size": 3006, "crc": 51409124}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt.class", "size": 1595, "crc": -647073715}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarKt$lambda-1$1.class", "size": 4236, "crc": 829067012}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarKt.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarKt.class", "size": 1493, "crc": 1107638816}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-1$1.class", "size": 2455, "crc": 766086284}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-2$1.class", "size": 2456, "crc": 233639622}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-3$1.class", "size": 2456, "crc": 1855389692}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-4$1.class", "size": 2456, "crc": 912428726}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-5$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-5$1.class", "size": 2456, "crc": -1151956452}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-6$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-6$1.class", "size": 2456, "crc": -362664309}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-7$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-7$1.class", "size": 2458, "crc": 1543882816}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt.class", "size": 3499, "crc": -1078575931}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-1$1.class", "size": 4485, "crc": 1362441632}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-2$1.class", "size": 4485, "crc": 980455826}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt.class", "size": 1937, "crc": -1107538860}, {"key": "androidx/compose/material3/ContentColorKt$LocalContentColor$1.class", "name": "androidx/compose/material3/ContentColorKt$LocalContentColor$1.class", "size": 1413, "crc": 969833585}, {"key": "androidx/compose/material3/ContentColorKt.class", "name": "androidx/compose/material3/ContentColorKt.class", "size": 1498, "crc": 797278195}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$2$1$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$2$1$1.class", "size": 2037, "crc": -1805307506}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$2.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$2.class", "size": 5424, "crc": -1562264647}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$3$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$3$1.class", "size": 1604, "crc": -1733786510}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$3.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$3.class", "size": 3588, "crc": 140806311}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$4.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$4.class", "size": 3135, "crc": 1559385986}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$1$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$1$1.class", "size": 6266, "crc": -2133161399}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$2$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$2$1.class", "size": 2595, "crc": 1218807717}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$3.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$3.class", "size": 3614, "crc": 2014304911}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$4.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$4.class", "size": 4037, "crc": -1480304411}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$errorText$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$errorText$1.class", "size": 2018, "crc": 1424292704}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$text$2$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$text$2$1.class", "size": 3378, "crc": 77620759}, {"key": "androidx/compose/material3/DateInputKt.class", "name": "androidx/compose/material3/DateInputKt.class", "size": 25388, "crc": 891409199}, {"key": "androidx/compose/material3/DateInputValidator.class", "name": "androidx/compose/material3/DateInputValidator.class", "size": 6456, "crc": -153954202}, {"key": "androidx/compose/material3/DatePickerColors$copy$25.class", "name": "androidx/compose/material3/DatePickerColors$copy$25.class", "size": 1479, "crc": 149618651}, {"key": "androidx/compose/material3/DatePickerColors.class", "name": "androidx/compose/material3/DatePickerColors.class", "size": 29104, "crc": 1089144999}, {"key": "androidx/compose/material3/DatePickerDefaults$AllDates$1.class", "name": "androidx/compose/material3/DatePickerDefaults$AllDates$1.class", "size": 755, "crc": 1337267358}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$1$1.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$1$1.class", "size": 2267, "crc": **********}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$2.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$2.class", "size": 2311, "crc": **********}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerTitle$1.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerTitle$1.class", "size": 1989, "crc": -51973137}, {"key": "androidx/compose/material3/DatePickerDefaults$rememberSnapFlingBehavior$1$snapLayoutInfoProvider$1.class", "name": "androidx/compose/material3/DatePickerDefaults$rememberSnapFlingBehavior$1$snapLayoutInfoProvider$1.class", "size": 1589, "crc": 905831506}, {"key": "androidx/compose/material3/DatePickerDefaults.class", "name": "androidx/compose/material3/DatePickerDefaults.class", "size": 26304, "crc": 505302657}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$2$1$1.class", "size": 3344, "crc": 663906168}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$2$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$2$1.class", "size": 3707, "crc": 4485461}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1.class", "size": 16708, "crc": -292380143}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1.class", "size": 5513, "crc": 138402906}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$2.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$2.class", "size": 3917, "crc": -1666808552}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt.class", "size": 9641, "crc": -932659985}, {"key": "androidx/compose/material3/DatePickerFormatter.class", "name": "androidx/compose/material3/DatePickerFormatter.class", "size": 1604, "crc": -1050827677}, {"key": "androidx/compose/material3/DatePickerFormatterImpl.class", "name": "androidx/compose/material3/DatePickerFormatterImpl.class", "size": 3530, "crc": 1348170558}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$1.class", "size": 1994, "crc": 446850458}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1$1$1$1.class", "size": 9423, "crc": 1268307105}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1.class", "size": 15416, "crc": -487592819}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$3.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$3.class", "size": 3314, "crc": 1798631413}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$2.class", "size": 3292, "crc": -1719170215}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$3.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$3.class", "size": 3587, "crc": 303297056}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$4$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$4$1$1.class", "size": 1594, "crc": -176106952}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$4.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$4.class", "size": 5144, "crc": -1107474492}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5$1$1.class", "size": 1584, "crc": 1300872273}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5$2$1.class", "size": 1495, "crc": -267996480}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5.class", "size": 6321, "crc": -1022470134}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$6.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$6.class", "size": 3147, "crc": -969681793}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1$1.class", "size": 3784, "crc": 688533146}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1.class", "size": 2156, "crc": 483156419}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1$1.class", "size": 3784, "crc": 1050645402}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1.class", "size": 2156, "crc": 1090619972}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$3$1.class", "size": 1970, "crc": -1036122448}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$1$1.class", "size": 1853, "crc": 1325776478}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1$1.class", "size": 4294, "crc": -658930010}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1.class", "size": 3237, "crc": -2116636663}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2.class", "size": 16785, "crc": -1209873499}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$2.class", "size": 3444, "crc": 436231894}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$yearPickerVisible$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$yearPickerVisible$2.class", "size": 2031, "crc": -1691151354}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerHeader$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerHeader$1$1.class", "size": 9012, "crc": 1642637442}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerHeader$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerHeader$2.class", "size": 2538, "crc": -61452976}, {"key": "androidx/compose/material3/DatePickerKt$Day$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Day$1$1.class", "size": 2399, "crc": 1664450482}, {"key": "androidx/compose/material3/DatePickerKt$Day$2.class", "name": "androidx/compose/material3/DatePickerKt$Day$2.class", "size": 9387, "crc": -1060834247}, {"key": "androidx/compose/material3/DatePickerKt$Day$3.class", "name": "androidx/compose/material3/DatePickerKt$Day$3.class", "size": 2980, "crc": -318193817}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$1$1.class", "size": 1895, "crc": -27019202}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$2$1.class", "size": 1896, "crc": 691319247}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$3.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$3.class", "size": 2187, "crc": 2399924}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$1.class", "size": 1418, "crc": 102816797}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$2.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$2.class", "size": 1418, "crc": 1503492646}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1.class", "size": 2384, "crc": -1433717484}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1$1.class", "size": 12345, "crc": 1480659263}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1.class", "size": 4199, "crc": -360648794}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1.class", "size": 9151, "crc": 1710061423}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$2$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$2$1.class", "size": 4662, "crc": 1163773365}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$3.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$3.class", "size": 3636, "crc": 1006852823}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$1$1.class", "size": 1863, "crc": -1521429243}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$3$1.class", "size": 1587, "crc": -1566606737}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$3.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$3.class", "size": 4024, "crc": 356114568}, {"key": "androidx/compose/material3/DatePickerKt$Month$2.class", "name": "androidx/compose/material3/DatePickerKt$Month$2.class", "size": 3355, "crc": 1275612150}, {"key": "androidx/compose/material3/DatePickerKt$Month$rangeSelectionDrawModifier$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$rangeSelectionDrawModifier$1$1.class", "size": 2496, "crc": 1575812805}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1$1$1.class", "size": 2232, "crc": -1101431647}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1.class", "size": 5136, "crc": 662992057}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1.class", "size": 11336, "crc": 1519018957}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$2.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$2.class", "size": 2958, "crc": -1941721654}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$1.class", "size": 2072, "crc": 2144496589}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$1.class", "size": 1553, "crc": 803180115}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$2.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$2.class", "size": 1521, "crc": -432186985}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$3.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$3.class", "size": 1521, "crc": -1666891418}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$4.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$4.class", "size": 1557, "crc": -201764909}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$5.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$5.class", "size": 2526, "crc": -129036839}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1.class", "size": 5671, "crc": 1727587531}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$3.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$3.class", "size": 5968, "crc": -2079373343}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$4.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$4.class", "size": 3552, "crc": 739475591}, {"key": "androidx/compose/material3/DatePickerKt$WeekDays$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$WeekDays$1$1$1$1.class", "size": 2068, "crc": 1420991134}, {"key": "androidx/compose/material3/DatePickerKt$WeekDays$2.class", "name": "androidx/compose/material3/DatePickerKt$WeekDays$2.class", "size": 1941, "crc": 116138373}, {"key": "androidx/compose/material3/DatePickerKt$Year$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Year$1$1.class", "size": 2400, "crc": -660253534}, {"key": "androidx/compose/material3/DatePickerKt$Year$2.class", "name": "androidx/compose/material3/DatePickerKt$Year$2.class", "size": 9149, "crc": -910054056}, {"key": "androidx/compose/material3/DatePickerKt$Year$3.class", "name": "androidx/compose/material3/DatePickerKt$Year$3.class", "size": 2879, "crc": -1927081020}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$1.class", "size": 1378, "crc": -849027607}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$2.class", "size": 1378, "crc": 1247635086}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1.class", "size": 2312, "crc": 840153455}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$1$1.class", "size": 3428, "crc": 284824640}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$2$1.class", "size": 1800, "crc": -985042254}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3$1.class", "size": 1822, "crc": -108696963}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3.class", "size": 3906, "crc": -2092859962}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1.class", "size": 10667, "crc": -872119163}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1.class", "size": 3676, "crc": 55404811}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1.class", "size": 12517, "crc": -24129387}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$2.class", "size": 3011, "crc": 1198656279}, {"key": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$1.class", "size": 6465, "crc": -1511555427}, {"key": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$2.class", "size": 2529, "crc": 1333256860}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1$1.class", "size": 3797, "crc": 754244818}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1.class", "size": 2291, "crc": 107361757}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1$1.class", "size": 3787, "crc": 1476310143}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1.class", "size": 2286, "crc": 1721366017}, {"key": "androidx/compose/material3/DatePickerKt$rememberDatePickerState$1$1.class", "name": "androidx/compose/material3/DatePickerKt$rememberDatePickerState$1$1.class", "size": 2273, "crc": 1575804690}, {"key": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$2.class", "name": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$2.class", "size": 1642, "crc": 1679718625}, {"key": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$3.class", "name": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$3.class", "size": 3309, "crc": 1147419869}, {"key": "androidx/compose/material3/DatePickerKt.class", "name": "androidx/compose/material3/DatePickerKt.class", "size": 117961, "crc": 676262208}, {"key": "androidx/compose/material3/DatePickerState.class", "name": "androidx/compose/material3/DatePickerState.class", "size": 1565, "crc": -1034370959}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$1.class", "size": 2715, "crc": 1793683740}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$2.class", "size": 2700, "crc": 370549043}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion.class", "size": 2307, "crc": -1122883480}, {"key": "androidx/compose/material3/DatePickerStateImpl.class", "name": "androidx/compose/material3/DatePickerStateImpl.class", "size": 6120, "crc": 2098976617}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$1$1.class", "size": 2204, "crc": -985888536}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2$1$1.class", "size": 2083, "crc": -476632537}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2.class", "size": 5618, "crc": -806303788}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3$1.class", "size": 1645, "crc": 858739345}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3.class", "size": 3694, "crc": 83382328}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$4$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$4$1.class", "size": 2204, "crc": -774518101}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5$1$1.class", "size": 2081, "crc": 698730387}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5.class", "size": 5621, "crc": 1511820379}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6$1.class", "size": 1645, "crc": -995070338}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6.class", "size": 3696, "crc": -1087566188}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$3.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$3.class", "size": 3320, "crc": 547589654}, {"key": "androidx/compose/material3/DateRangeInputKt.class", "name": "androidx/compose/material3/DateRangeInputKt.class", "size": 22576, "crc": -1663759801}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$1.class", "size": 3030, "crc": 218138993}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$2.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$2.class", "size": 3028, "crc": 1720789767}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$3.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$3.class", "size": 2456, "crc": 2038726479}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$4$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$4$1.class", "size": 2732, "crc": -495999207}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$6.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$6.class", "size": 4102, "crc": -1214183575}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerTitle$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerTitle$1.class", "size": 2034, "crc": 2141481671}, {"key": "androidx/compose/material3/DateRangePickerDefaults.class", "name": "androidx/compose/material3/DateRangePickerDefaults.class", "size": 23501, "crc": -2094933826}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$2.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$2.class", "size": 3380, "crc": 1507130984}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$3.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$3.class", "size": 3754, "crc": 848442375}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4$1$1.class", "size": 1644, "crc": 1858740035}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4.class", "size": 5354, "crc": 1095310716}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$1$1.class", "size": 1849, "crc": -1871259447}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$2$1.class", "size": 1545, "crc": 1151870449}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5.class", "size": 6611, "crc": 924358317}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$6.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$6.class", "size": 3202, "crc": 671502042}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePickerContent$2.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePickerContent$2.class", "size": 3734, "crc": -98720475}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$1.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$1.class", "size": 2109, "crc": -406490172}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$2.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$2.class", "size": 6170, "crc": -1150294063}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$3.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$3.class", "size": 3879, "crc": 569713032}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$1.class", "size": 1435, "crc": -1913257829}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$2.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$2.class", "size": 1435, "crc": -245798758}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1.class", "size": 2408, "crc": -328052319}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$1$1.class", "size": 2273, "crc": 1902939609}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1.class", "size": 7348, "crc": 1155328041}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1.class", "size": 16361, "crc": 898136288}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1.class", "size": 4614, "crc": 217225340}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$onDateSelectionChange$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$onDateSelectionChange$1$1.class", "size": 2059, "crc": 261279598}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1.class", "size": 13327, "crc": 228394709}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$2$1.class", "size": 4743, "crc": 1878418494}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$3.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$3.class", "size": 3899, "crc": 348283823}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1$1.class", "size": 3802, "crc": 1369524197}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1.class", "size": 2291, "crc": 1106140343}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1$1.class", "size": 3792, "crc": 1002782330}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1.class", "size": 2286, "crc": 1110139882}, {"key": "androidx/compose/material3/DateRangePickerKt$rememberDateRangePickerState$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$rememberDateRangePickerState$1$1.class", "size": 2460, "crc": 6721535}, {"key": "androidx/compose/material3/DateRangePickerKt.class", "name": "androidx/compose/material3/DateRangePickerKt.class", "size": 45538, "crc": 1095704292}, {"key": "androidx/compose/material3/DateRangePickerState.class", "name": "androidx/compose/material3/DateRangePickerState.class", "size": 1787, "crc": -521535889}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$1.class", "size": 2818, "crc": 271102671}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$2.class", "size": 2775, "crc": -1945609728}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion.class", "size": 2352, "crc": -1262724126}, {"key": "androidx/compose/material3/DateRangePickerStateImpl.class", "name": "androidx/compose/material3/DateRangePickerStateImpl.class", "size": 7734, "crc": 1298924268}, {"key": "androidx/compose/material3/DateVisualTransformation$dateOffsetTranslator$1.class", "name": "androidx/compose/material3/DateVisualTransformation$dateOffsetTranslator$1.class", "size": 1689, "crc": 672885858}, {"key": "androidx/compose/material3/DateVisualTransformation.class", "name": "androidx/compose/material3/DateVisualTransformation.class", "size": 4972, "crc": 442518360}, {"key": "androidx/compose/material3/DefaultDrawerItemsColor.class", "name": "androidx/compose/material3/DefaultDrawerItemsColor.class", "size": 6289, "crc": 382034849}, {"key": "androidx/compose/material3/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "name": "androidx/compose/material3/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "size": 3312, "crc": -1819448905}, {"key": "androidx/compose/material3/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "name": "androidx/compose/material3/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "size": 2380, "crc": -1973784911}, {"key": "androidx/compose/material3/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "name": "androidx/compose/material3/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "size": 2192, "crc": 751050477}, {"key": "androidx/compose/material3/DelegatingThemeAwareRippleNode.class", "name": "androidx/compose/material3/DelegatingThemeAwareRippleNode.class", "size": 5193, "crc": -1793254298}, {"key": "androidx/compose/material3/DisplayMode$Companion.class", "name": "androidx/compose/material3/DisplayMode$Companion.class", "size": 1222, "crc": 896220538}, {"key": "androidx/compose/material3/DisplayMode.class", "name": "androidx/compose/material3/DisplayMode.class", "size": 2676, "crc": -2117060067}, {"key": "androidx/compose/material3/DividerDefaults.class", "name": "androidx/compose/material3/DividerDefaults.class", "size": 2417, "crc": -306969837}, {"key": "androidx/compose/material3/DividerKt$Divider$1.class", "name": "androidx/compose/material3/DividerKt$Divider$1.class", "size": 1842, "crc": 1820876008}, {"key": "androidx/compose/material3/DividerKt$HorizontalDivider$1$1.class", "name": "androidx/compose/material3/DividerKt$HorizontalDivider$1$1.class", "size": 2135, "crc": -1610986488}, {"key": "androidx/compose/material3/DividerKt$HorizontalDivider$2.class", "name": "androidx/compose/material3/DividerKt$HorizontalDivider$2.class", "size": 1872, "crc": -1832410263}, {"key": "androidx/compose/material3/DividerKt$VerticalDivider$1$1.class", "name": "androidx/compose/material3/DividerKt$VerticalDivider$1$1.class", "size": 2130, "crc": 1198303774}, {"key": "androidx/compose/material3/DividerKt$VerticalDivider$2.class", "name": "androidx/compose/material3/DividerKt$VerticalDivider$2.class", "size": 1866, "crc": 2084069164}, {"key": "androidx/compose/material3/DividerKt.class", "name": "androidx/compose/material3/DividerKt.class", "size": 10150, "crc": -1882820620}, {"key": "androidx/compose/material3/DrawerDefaults.class", "name": "androidx/compose/material3/DrawerDefaults.class", "size": 7457, "crc": 1226159266}, {"key": "androidx/compose/material3/DrawerPredictiveBackState.class", "name": "androidx/compose/material3/DrawerPredictiveBackState.class", "size": 5139, "crc": -1797696508}, {"key": "androidx/compose/material3/DrawerState$1.class", "name": "androidx/compose/material3/DrawerState$1.class", "size": 1645, "crc": -1117467919}, {"key": "androidx/compose/material3/DrawerState$Companion$Saver$1.class", "name": "androidx/compose/material3/DrawerState$Companion$Saver$1.class", "size": 2059, "crc": -1401787169}, {"key": "androidx/compose/material3/DrawerState$Companion$Saver$2.class", "name": "androidx/compose/material3/DrawerState$Companion$Saver$2.class", "size": 2104, "crc": 1044020950}, {"key": "androidx/compose/material3/DrawerState$Companion.class", "name": "androidx/compose/material3/DrawerState$Companion.class", "size": 2183, "crc": 284634973}, {"key": "androidx/compose/material3/DrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/DrawerState$anchoredDraggableState$1.class", "size": 1571, "crc": -467876447}, {"key": "androidx/compose/material3/DrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material3/DrawerState$anchoredDraggableState$2.class", "size": 2340, "crc": 261320591}, {"key": "androidx/compose/material3/DrawerState$animateTo$3$1.class", "name": "androidx/compose/material3/DrawerState$animateTo$3$1.class", "size": 1851, "crc": -1584006002}, {"key": "androidx/compose/material3/DrawerState$animateTo$3.class", "name": "androidx/compose/material3/DrawerState$animateTo$3.class", "size": 5511, "crc": 1363206418}, {"key": "androidx/compose/material3/DrawerState$offset$1.class", "name": "androidx/compose/material3/DrawerState$offset$1.class", "size": 1642, "crc": -1242561139}, {"key": "androidx/compose/material3/DrawerState.class", "name": "androidx/compose/material3/DrawerState.class", "size": 11793, "crc": -741129782}, {"key": "androidx/compose/material3/DrawerValue.class", "name": "androidx/compose/material3/DrawerValue.class", "size": 1390, "crc": 1301864685}, {"key": "androidx/compose/material3/DynamicTonalPaletteKt.class", "name": "androidx/compose/material3/DynamicTonalPaletteKt.class", "size": 12109, "crc": -1105249928}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$1.class", "size": 1466, "crc": -819432244}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2055, "crc": 1502609632}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1.class", "size": 5306, "crc": -308783252}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior.class", "size": 4487, "crc": -1614775839}, {"key": "androidx/compose/material3/EqualWeightContentMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/EqualWeightContentMeasurePolicy$measure$1.class", "size": 1747, "crc": -208062604}, {"key": "androidx/compose/material3/EqualWeightContentMeasurePolicy$measure$5.class", "name": "androidx/compose/material3/EqualWeightContentMeasurePolicy$measure$5.class", "size": 3549, "crc": -1741139495}, {"key": "androidx/compose/material3/EqualWeightContentMeasurePolicy.class", "name": "androidx/compose/material3/EqualWeightContentMeasurePolicy.class", "size": 6125, "crc": -1863792830}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$1.class", "size": 1466, "crc": 1051402706}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2047, "crc": -748822899}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1.class", "size": 4922, "crc": 1045195759}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior.class", "size": 4502, "crc": 1953345891}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$1.class", "size": 1487, "crc": -1759729977}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2111, "crc": 115629390}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1.class", "size": 5517, "crc": -85868442}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior.class", "size": 4529, "crc": 1741936752}, {"key": "androidx/compose/material3/ExperimentalMaterial3Api.class", "name": "androidx/compose/material3/ExperimentalMaterial3Api.class", "size": 828, "crc": -1244049908}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1$1.class", "size": 1760, "crc": 1985033088}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "size": 5494, "crc": -1110882551}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$3.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$3.class", "size": 3829, "crc": 1583407476}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$4.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$4.class", "size": 3883, "crc": 709419904}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$5.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$5.class", "size": 3101, "crc": -85622881}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "size": 2644, "crc": -794005427}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope.class", "size": 24916, "crc": -705081654}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScopeImpl.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScopeImpl.class", "size": 728, "crc": 398237547}, {"key": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "size": 2035, "crc": -576719385}, {"key": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "name": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "size": 1840, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenuDefaults.class", "name": "androidx/compose/material3/ExposedDropdownMenuDefaults.class", "size": 39773, "crc": 926217595}, {"key": "androidx/compose/material3/ExposedDropdownMenuPositionProvider$2.class", "name": "androidx/compose/material3/ExposedDropdownMenuPositionProvider$2.class", "size": 1794, "crc": 567688886}, {"key": "androidx/compose/material3/ExposedDropdownMenuPositionProvider.class", "name": "androidx/compose/material3/ExposedDropdownMenuPositionProvider.class", "size": 9035, "crc": 798600510}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "size": 3510, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$3$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$3$1.class", "size": 2663, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "size": 1571, "crc": 418757790}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "size": 1720, "crc": 525136960}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "size": 2729, "crc": -1801816052}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1$1.class", "size": 2234, "crc": 1438872064}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1.class", "size": 3928, "crc": 1901523766}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$1.class", "size": 2502, "crc": 1659296044}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "size": 5536, "crc": 487627810}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$invoke$$inlined$onDispose$1.class", "size": 2432, "crc": 589473127}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$listener$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$listener$1.class", "size": 3152, "crc": -1617401772}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1.class", "size": 3669, "crc": -1775798727}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$2.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$2.class", "size": 2283, "crc": 1572969283}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1$1.class", "size": 5238, "crc": 2039931773}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1.class", "size": 4314, "crc": 1276750971}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2$1.class", "size": 2364, "crc": -546933431}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2.class", "size": 4097, "crc": -408804573}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt.class", "size": 33572, "crc": -1797188241}, {"key": "androidx/compose/material3/ExpressiveNavigationBarDefaults.class", "name": "androidx/compose/material3/ExpressiveNavigationBarDefaults.class", "size": 4805, "crc": 2055371624}, {"key": "androidx/compose/material3/ExpressiveNavigationBarItemDefaults.class", "name": "androidx/compose/material3/ExpressiveNavigationBarItemDefaults.class", "size": 4487, "crc": 893920440}, {"key": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBar$1.class", "name": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBar$1.class", "size": 9135, "crc": 2062437263}, {"key": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBar$2.class", "name": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBar$2.class", "size": 2683, "crc": -994884687}, {"key": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBarItem$1.class", "name": "androidx/compose/material3/ExpressiveNavigationBarKt$ExpressiveNavigationBarItem$1.class", "size": 3698, "crc": -75883142}, {"key": "androidx/compose/material3/ExpressiveNavigationBarKt.class", "name": "androidx/compose/material3/ExpressiveNavigationBarKt.class", "size": 19027, "crc": 2015124870}, {"key": "androidx/compose/material3/FabPlacement.class", "name": "androidx/compose/material3/FabPlacement.class", "size": 1138, "crc": 2076667878}, {"key": "androidx/compose/material3/FabPosition$Companion.class", "name": "androidx/compose/material3/FabPosition$Companion.class", "size": 1539, "crc": 1034435693}, {"key": "androidx/compose/material3/FabPosition.class", "name": "androidx/compose/material3/FabPosition.class", "size": 2866, "crc": 1628412907}, {"key": "androidx/compose/material3/FadeInFadeOutAnimationItem.class", "name": "androidx/compose/material3/FadeInFadeOutAnimationItem.class", "size": 4399, "crc": 1933823945}, {"key": "androidx/compose/material3/FadeInFadeOutState.class", "name": "androidx/compose/material3/FadeInFadeOutState.class", "size": 2309, "crc": 65848276}, {"key": "androidx/compose/material3/FilterChipDefaults.class", "name": "androidx/compose/material3/FilterChipDefaults.class", "size": 15006, "crc": 1244898709}, {"key": "androidx/compose/material3/FloatingActionButtonDefaults.class", "name": "androidx/compose/material3/FloatingActionButtonDefaults.class", "size": 8884, "crc": -945472938}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$1$1.class", "size": 4346, "crc": 1839644593}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1$1.class", "size": 4095, "crc": -278458073}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1.class", "size": 4715, "crc": -702032213}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1.class", "size": 4686, "crc": 1158122527}, {"key": "androidx/compose/material3/FloatingActionButtonElevation.class", "name": "androidx/compose/material3/FloatingActionButtonElevation.class", "size": 8462, "crc": -1000300289}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "size": 2016, "crc": 1501232927}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "size": 2073, "crc": -589342467}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable.class", "size": 7123, "crc": -154636230}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "size": 10468, "crc": 897505446}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "size": 3526, "crc": -1376445331}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3$1$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3$1$1$1.class", "size": 1842, "crc": -1516580789}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3$1$1.class", "size": 11030, "crc": -2049575038}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3.class", "size": 12391, "crc": -592652228}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$4.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$4.class", "size": 3710, "crc": -2062165288}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$1.class", "size": 2266, "crc": -1930911983}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2$1.class", "size": 9448, "crc": 685531666}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2.class", "size": 4077, "crc": -1581115045}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3.class", "size": 3411, "crc": 287812016}, {"key": "androidx/compose/material3/FloatingActionButtonKt$LargeFloatingActionButton$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$LargeFloatingActionButton$1.class", "size": 3426, "crc": -1251117934}, {"key": "androidx/compose/material3/FloatingActionButtonKt$SmallFloatingActionButton$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$SmallFloatingActionButton$1.class", "size": 3426, "crc": -1944134623}, {"key": "androidx/compose/material3/FloatingActionButtonKt.class", "name": "androidx/compose/material3/FloatingActionButtonKt.class", "size": 25825, "crc": 735787008}, {"key": "androidx/compose/material3/IconButtonColors.class", "name": "androidx/compose/material3/IconButtonColors.class", "size": 5597, "crc": 299352334}, {"key": "androidx/compose/material3/IconButtonDefaults.class", "name": "androidx/compose/material3/IconButtonDefaults.class", "size": 29005, "crc": -787936021}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$1.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$1.class", "size": 2192, "crc": -1469476269}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$2.class", "size": 9542, "crc": 1350125272}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$3.class", "size": 3232, "crc": -1310555598}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$1.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$1.class", "size": 2219, "crc": -1996476863}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$2.class", "size": 9603, "crc": -1817531293}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$3.class", "size": 3373, "crc": 106310173}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$1.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$1.class", "size": 2207, "crc": 820733868}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$2.class", "size": 9597, "crc": -882799396}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$3.class", "size": 3247, "crc": -972800298}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$1.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$1.class", "size": 2234, "crc": 253236289}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$2.class", "size": 9658, "crc": 1994591370}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$3.class", "size": 3388, "crc": -1711167619}, {"key": "androidx/compose/material3/IconButtonKt$IconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$IconButton$2.class", "size": 3020, "crc": -1952746491}, {"key": "androidx/compose/material3/IconButtonKt$IconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$IconToggleButton$2.class", "size": 3161, "crc": 1476307660}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$1.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$1.class", "size": 2240, "crc": -152905763}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$2.class", "size": 9547, "crc": -2039437573}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$3.class", "size": 3457, "crc": -422894836}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$1.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$1.class", "size": 2267, "crc": -997808756}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$2.class", "size": 9608, "crc": 948658384}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$3.class", "size": 3598, "crc": 548541323}, {"key": "androidx/compose/material3/IconButtonKt.class", "name": "androidx/compose/material3/IconButtonKt.class", "size": 38321, "crc": -211308657}, {"key": "androidx/compose/material3/IconKt$Icon$1.class", "name": "androidx/compose/material3/IconKt$Icon$1.class", "size": 2083, "crc": 1143906148}, {"key": "androidx/compose/material3/IconKt$Icon$2.class", "name": "androidx/compose/material3/IconKt$Icon$2.class", "size": 2057, "crc": -1339369659}, {"key": "androidx/compose/material3/IconKt$Icon$3.class", "name": "androidx/compose/material3/IconKt$Icon$3.class", "size": 2070, "crc": -1192334411}, {"key": "androidx/compose/material3/IconKt$Icon$4$1.class", "name": "androidx/compose/material3/IconKt$Icon$4$1.class", "size": 3324, "crc": 1176635156}, {"key": "androidx/compose/material3/IconKt$Icon$5.class", "name": "androidx/compose/material3/IconKt$Icon$5.class", "size": 2191, "crc": -794428618}, {"key": "androidx/compose/material3/IconKt$Icon$semantics$1$1.class", "name": "androidx/compose/material3/IconKt$Icon$semantics$1$1.class", "size": 2165, "crc": -1810537284}, {"key": "androidx/compose/material3/IconKt$Icon$semantics$2$1.class", "name": "androidx/compose/material3/IconKt$Icon$semantics$2$1.class", "size": 2200, "crc": -729641216}, {"key": "androidx/compose/material3/IconKt$defaultSizeForColorProducer$1$1.class", "name": "androidx/compose/material3/IconKt$defaultSizeForColorProducer$1$1.class", "size": 1964, "crc": 896618951}, {"key": "androidx/compose/material3/IconKt$defaultSizeForColorProducer$1.class", "name": "androidx/compose/material3/IconKt$defaultSizeForColorProducer$1.class", "size": 3033, "crc": -166638287}, {"key": "androidx/compose/material3/IconKt.class", "name": "androidx/compose/material3/IconKt.class", "size": 17315, "crc": -164281736}, {"key": "androidx/compose/material3/IconToggleButtonColors.class", "name": "androidx/compose/material3/IconToggleButtonColors.class", "size": 8688, "crc": -928549071}, {"key": "androidx/compose/material3/InputChipDefaults.class", "name": "androidx/compose/material3/InputChipDefaults.class", "size": 11190, "crc": 1345860812}, {"key": "androidx/compose/material3/InputIdentifier$Companion.class", "name": "androidx/compose/material3/InputIdentifier$Companion.class", "size": 1467, "crc": -313363428}, {"key": "androidx/compose/material3/InputIdentifier.class", "name": "androidx/compose/material3/InputIdentifier.class", "size": 2808, "crc": 919202899}, {"key": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "name": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "size": 1343, "crc": -185968809}, {"key": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentSize$1.class", "name": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentSize$1.class", "size": 2347, "crc": -360118151}, {"key": "androidx/compose/material3/InteractiveComponentSizeKt.class", "name": "androidx/compose/material3/InteractiveComponentSizeKt.class", "size": 3224, "crc": -1965025271}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$1$1$1.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$1$1$1.class", "size": 4444, "crc": 704136109}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$1$1.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$1$1.class", "size": 4238, "crc": -315460637}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$2.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$2.class", "size": 2075, "crc": -1351278014}, {"key": "androidx/compose/material3/LabelKt$Label$1.class", "name": "androidx/compose/material3/LabelKt$Label$1.class", "size": 3138, "crc": -1986406189}, {"key": "androidx/compose/material3/LabelKt$Label$2.class", "name": "androidx/compose/material3/LabelKt$Label$2.class", "size": 2950, "crc": 1261227612}, {"key": "androidx/compose/material3/LabelKt$Label$scope$1$1.class", "name": "androidx/compose/material3/LabelKt$Label$scope$1$1.class", "size": 2018, "crc": 1368039912}, {"key": "androidx/compose/material3/LabelKt$Label$wrappedContent$1$1.class", "name": "androidx/compose/material3/LabelKt$Label$wrappedContent$1$1.class", "size": 2073, "crc": 1843574460}, {"key": "androidx/compose/material3/LabelKt$Label$wrappedContent$1.class", "name": "androidx/compose/material3/LabelKt$Label$wrappedContent$1.class", "size": 9734, "crc": -784876519}, {"key": "androidx/compose/material3/LabelKt.class", "name": "androidx/compose/material3/LabelKt.class", "size": 12382, "crc": -920404342}, {"key": "androidx/compose/material3/LabelStateImpl.class", "name": "androidx/compose/material3/LabelStateImpl.class", "size": 2731, "crc": 1013186462}, {"key": "androidx/compose/material3/LayoutId.class", "name": "androidx/compose/material3/LayoutId.class", "size": 1375, "crc": -1303348528}, {"key": "androidx/compose/material3/ListItemColors.class", "name": "androidx/compose/material3/ListItemColors.class", "size": 3948, "crc": -1334315542}, {"key": "androidx/compose/material3/ListItemDefaults.class", "name": "androidx/compose/material3/ListItemDefaults.class", "size": 6023, "crc": -1001311973}, {"key": "androidx/compose/material3/ListItemKt$ListItem$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$1.class", "size": 1797, "crc": -64522712}, {"key": "androidx/compose/material3/ListItemKt$ListItem$2.class", "name": "androidx/compose/material3/ListItemKt$ListItem$2.class", "size": 3913, "crc": 529218010}, {"key": "androidx/compose/material3/ListItemKt$ListItem$3.class", "name": "androidx/compose/material3/ListItemKt$ListItem$3.class", "size": 3431, "crc": -398486159}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedHeadlineContent$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedHeadlineContent$1.class", "size": 3458, "crc": -1420966607}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedLeadingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedLeadingContent$1$1.class", "size": 10365, "crc": 1342648988}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedOverlineContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedOverlineContent$1$1.class", "size": 3558, "crc": 135743638}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedSupportingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedSupportingContent$1$1.class", "size": 3570, "crc": -1652080938}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedTrailingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedTrailingContent$1$1.class", "size": 10132, "crc": -845498227}, {"key": "androidx/compose/material3/ListItemKt$ListItemLayout$1.class", "name": "androidx/compose/material3/ListItemKt$ListItemLayout$1.class", "size": 2863, "crc": 1368064105}, {"key": "androidx/compose/material3/ListItemKt$ProvideTextStyleFromToken$1.class", "name": "androidx/compose/material3/ListItemKt$ProvideTextStyleFromToken$1.class", "size": 2350, "crc": 660334174}, {"key": "androidx/compose/material3/ListItemKt$place$1.class", "name": "androidx/compose/material3/ListItemKt$place$1.class", "size": 4147, "crc": 908014226}, {"key": "androidx/compose/material3/ListItemKt.class", "name": "androidx/compose/material3/ListItemKt.class", "size": 29218, "crc": 1128754334}, {"key": "androidx/compose/material3/ListItemMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material3/ListItemMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1869, "crc": -650750716}, {"key": "androidx/compose/material3/ListItemMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material3/ListItemMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1865, "crc": -781854742}, {"key": "androidx/compose/material3/ListItemMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material3/ListItemMeasurePolicy$minIntrinsicHeight$1.class", "size": 1869, "crc": -1454091026}, {"key": "androidx/compose/material3/ListItemMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material3/ListItemMeasurePolicy$minIntrinsicWidth$1.class", "size": 1865, "crc": -1800558945}, {"key": "androidx/compose/material3/ListItemMeasurePolicy.class", "name": "androidx/compose/material3/ListItemMeasurePolicy.class", "size": 14060, "crc": 1992440100}, {"key": "androidx/compose/material3/ListItemType$Companion.class", "name": "androidx/compose/material3/ListItemType$Companion.class", "size": 1796, "crc": -910001090}, {"key": "androidx/compose/material3/ListItemType.class", "name": "androidx/compose/material3/ListItemType.class", "size": 3323, "crc": -478626425}, {"key": "androidx/compose/material3/Locale24$Companion.class", "name": "androidx/compose/material3/Locale24$Companion.class", "size": 3501, "crc": -1830338944}, {"key": "androidx/compose/material3/Locale24.class", "name": "androidx/compose/material3/Locale24.class", "size": 962, "crc": 614525201}, {"key": "androidx/compose/material3/MaterialTheme.class", "name": "androidx/compose/material3/MaterialTheme.class", "size": 4637, "crc": -1959979499}, {"key": "androidx/compose/material3/MaterialThemeKt$LocalUsingExpressiveTheme$1.class", "name": "androidx/compose/material3/MaterialThemeKt$LocalUsingExpressiveTheme$1.class", "size": 1263, "crc": -765659096}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialExpressiveTheme$1.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialExpressiveTheme$1.class", "size": 4529, "crc": 1472526281}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialExpressiveTheme$2.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialExpressiveTheme$2.class", "size": 2650, "crc": 1203240025}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$1.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$1.class", "size": 3106, "crc": 482800824}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$2.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$2.class", "size": 2620, "crc": -1731831470}, {"key": "androidx/compose/material3/MaterialThemeKt.class", "name": "androidx/compose/material3/MaterialThemeKt.class", "size": 12375, "crc": -1625026682}, {"key": "androidx/compose/material3/MenuAnchorType$Companion.class", "name": "androidx/compose/material3/MenuAnchorType$Companion.class", "size": 1646, "crc": 1505286700}, {"key": "androidx/compose/material3/MenuAnchorType.class", "name": "androidx/compose/material3/MenuAnchorType.class", "size": 2919, "crc": -370010363}, {"key": "androidx/compose/material3/MenuDefaults.class", "name": "androidx/compose/material3/MenuDefaults.class", "size": 8688, "crc": -147163158}, {"key": "androidx/compose/material3/MenuItemColors.class", "name": "androidx/compose/material3/MenuItemColors.class", "size": 7042, "crc": 443225051}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$1$1.class", "size": 3740, "crc": 2090392812}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$2.class", "size": 10631, "crc": -2127997090}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$3.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$3.class", "size": 3786, "crc": 1951114361}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$alpha$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$alpha$2.class", "size": 3679, "crc": -1294278404}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$scale$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$scale$2.class", "size": 3807, "crc": 1024024511}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$1.class", "size": 9178, "crc": -1383831336}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$2.class", "size": 10333, "crc": 1998801098}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$3.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$3.class", "size": 9180, "crc": -813243309}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1.class", "size": 5624, "crc": 423654728}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$2.class", "size": 3687, "crc": 1867383368}, {"key": "androidx/compose/material3/MenuKt.class", "name": "androidx/compose/material3/MenuKt.class", "size": 30222, "crc": 1201758696}, {"key": "androidx/compose/material3/MinimumInteractiveModifier.class", "name": "androidx/compose/material3/MinimumInteractiveModifier.class", "size": 3157, "crc": -876469556}, {"key": "androidx/compose/material3/MinimumInteractiveModifierNode$measure$1.class", "name": "androidx/compose/material3/MinimumInteractiveModifierNode$measure$1.class", "size": 2259, "crc": 740278934}, {"key": "androidx/compose/material3/MinimumInteractiveModifierNode.class", "name": "androidx/compose/material3/MinimumInteractiveModifierNode.class", "size": 5051, "crc": -2070598808}, {"key": "androidx/compose/material3/ModalBottomSheetDefaults.class", "name": "androidx/compose/material3/ModalBottomSheetDefaults.class", "size": 2497, "crc": -154850327}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api33Impl.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api33Impl.class", "size": 3102, "crc": 502089866}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackCancelled$1.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackCancelled$1.class", "size": 4386, "crc": 172066150}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackProgressed$1.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackProgressed$1.class", "size": 4611, "crc": -1801831927}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackStarted$1.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1$onBackStarted$1.class", "size": 4596, "crc": -181134088}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1.class", "size": 3528, "crc": 824907933}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Api34Impl.class", "size": 2300, "crc": -55656028}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout$Content$4.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout$Content$4.class", "size": 1730, "crc": 2136416052}, {"key": "androidx/compose/material3/ModalBottomSheetDialogLayout.class", "name": "androidx/compose/material3/ModalBottomSheetDialogLayout.class", "size": 9183, "crc": -115129210}, {"key": "androidx/compose/material3/ModalBottomSheetDialogWrapper$1$2.class", "name": "androidx/compose/material3/ModalBottomSheetDialogWrapper$1$2.class", "size": 1592, "crc": -1554231041}, {"key": "androidx/compose/material3/ModalBottomSheetDialogWrapper$3.class", "name": "androidx/compose/material3/ModalBottomSheetDialogWrapper$3.class", "size": 2396, "crc": -179361288}, {"key": "androidx/compose/material3/ModalBottomSheetDialogWrapper$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheetDialogWrapper$WhenMappings.class", "size": 849, "crc": 1921258070}, {"key": "androidx/compose/material3/ModalBottomSheetDialogWrapper.class", "name": "androidx/compose/material3/ModalBottomSheetDialogWrapper.class", "size": 12697, "crc": 223456524}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$1.class", "size": 2748, "crc": 1106169495}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$1.class", "size": 4044, "crc": -1883530303}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$2.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$2.class", "size": 3538, "crc": -839384004}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$3.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$3.class", "size": 3529, "crc": -754496987}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$4.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1$4.class", "size": 1689, "crc": 1099281221}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$2$1.class", "size": 3815, "crc": 1752171756}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$3$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$3$1.class", "size": 1777, "crc": 1648833081}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$3.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$3.class", "size": 13998, "crc": 1042745003}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$4$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$4$1.class", "size": 3850, "crc": 1566557704}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$5.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$5.class", "size": 4375, "crc": -1228838619}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1$1.class", "size": 3614, "crc": -648730664}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1$2.class", "size": 2021, "crc": 816820859}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$animateToDismiss$1$1.class", "size": 3479, "crc": 1760885060}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1$1.class", "size": 3684, "crc": 1142274740}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1$2.class", "size": 2011, "crc": -1096205858}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheet$settleToDismiss$1$1.class", "size": 3027, "crc": -1995618209}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$1.class", "size": 2876, "crc": -1372557761}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1$WhenMappings.class", "size": 939, "crc": 991278080}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1$newAnchors$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1$newAnchors$1.class", "size": 2565, "crc": 1796797856}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$3$1.class", "size": 4241, "crc": 2035676553}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$4$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$4$1.class", "size": 3894, "crc": -1154222151}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$5$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$5$1.class", "size": 2335, "crc": 740887529}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$6$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$6$1.class", "size": 3646, "crc": 1547036405}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$1$1.class", "size": 2708, "crc": 872747035}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$1.class", "size": 1660, "crc": -24784689}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$2$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$2$1.class", "size": 3800, "crc": 421077351}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$2.class", "size": 2833, "crc": -361258525}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$3$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$3$1.class", "size": 3818, "crc": 282457463}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$3.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1$1$3.class", "size": 2760, "crc": 43469469}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7$2$1$1.class", "size": 3927, "crc": 610606074}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$7.class", "size": 19246, "crc": -833287989}, {"key": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$8.class", "name": "androidx/compose/material3/ModalBottomSheetKt$ModalBottomSheetContent$8.class", "size": 5255, "crc": -1229797964}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$1$1.class", "size": 2188, "crc": 2145802716}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$2.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$2.class", "size": 1983, "crc": 888509503}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$1$1$1.class", "size": 1699, "crc": 1446989474}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$1$1.class", "size": 4231, "crc": 969820763}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$2$1$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$2$1$1.class", "size": 1544, "crc": 1258243352}, {"key": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$2$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$Scrim$dismissSheet$2$1.class", "size": 2555, "crc": -1281827264}, {"key": "androidx/compose/material3/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "name": "androidx/compose/material3/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "size": 1636, "crc": 1394789034}, {"key": "androidx/compose/material3/ModalBottomSheetKt.class", "name": "androidx/compose/material3/ModalBottomSheetKt.class", "size": 41971, "crc": 463214158}, {"key": "androidx/compose/material3/ModalBottomSheetProperties.class", "name": "androidx/compose/material3/ModalBottomSheetProperties.class", "size": 3071, "crc": 1697828564}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$1.class", "size": 2771, "crc": -1561013046}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2.class", "size": 4247, "crc": 1953360124}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$1$1$invoke$$inlined$onDispose$1.class", "size": 2371, "crc": -1283494556}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$1$1.class", "size": 3267, "crc": 155398477}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$2$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$2$1.class", "size": 2449, "crc": 1522251547}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$3.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$3.class", "size": 2995, "crc": 291634144}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialog$1$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialog$1$1$1$1.class", "size": 1784, "crc": 137381646}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialog$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialog$1$1$1.class", "size": 10171, "crc": -1081479883}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialogId$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetDialog$dialogId$1.class", "size": 1490, "crc": -557940929}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$WhenMappings.class", "size": 910, "crc": -1952419136}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt.class", "size": 23456, "crc": -1596765391}, {"key": "androidx/compose/material3/MultiChoiceSegmentedButtonRowScope.class", "name": "androidx/compose/material3/MultiChoiceSegmentedButtonRowScope.class", "size": 539, "crc": -1529522263}, {"key": "androidx/compose/material3/MultiChoiceSegmentedButtonScopeWrapper.class", "name": "androidx/compose/material3/MultiChoiceSegmentedButtonScopeWrapper.class", "size": 3177, "crc": 1862968908}, {"key": "androidx/compose/material3/NavigationBarArrangement$Companion.class", "name": "androidx/compose/material3/NavigationBarArrangement$Companion.class", "size": 1311, "crc": -2103321137}, {"key": "androidx/compose/material3/NavigationBarArrangement.class", "name": "androidx/compose/material3/NavigationBarArrangement.class", "size": 2658, "crc": 357937139}, {"key": "androidx/compose/material3/NavigationBarDefaults.class", "name": "androidx/compose/material3/NavigationBarDefaults.class", "size": 4194, "crc": 1165178937}, {"key": "androidx/compose/material3/NavigationBarItemColors.class", "name": "androidx/compose/material3/NavigationBarItemColors.class", "size": 8066, "crc": 753751822}, {"key": "androidx/compose/material3/NavigationBarItemDefaults.class", "name": "androidx/compose/material3/NavigationBarItemDefaults.class", "size": 7001, "crc": -2049840930}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBar$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBar$1.class", "size": 10628, "crc": -1130305504}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBar$2.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBar$2.class", "size": 2711, "crc": -1231696625}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$1$1.class", "size": 1943, "crc": -36943240}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$2$1.class", "size": 1790, "crc": -338707495}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicator$1$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicator$1$1$1.class", "size": 1976, "crc": -636683191}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicator$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicator$1.class", "size": 6490, "crc": 940772436}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicatorRipple$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$indicatorRipple$1.class", "size": 4379, "crc": -2013548648}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3.class", "size": 3672, "crc": 1581379611}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1$1.class", "size": 1656, "crc": -2133391578}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1.class", "size": 12405, "crc": -1532877700}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledLabel$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledLabel$1$1.class", "size": 6140, "crc": 675668105}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$1$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$1$2$1.class", "size": 2214, "crc": -862127058}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$2$1.class", "size": 9382, "crc": -190996036}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$3.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$3.class", "size": 3029, "crc": -621055021}, {"key": "androidx/compose/material3/NavigationBarKt$placeIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$placeIcon$1.class", "size": 2799, "crc": -1576557002}, {"key": "androidx/compose/material3/NavigationBarKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$placeLabelAndIcon$1.class", "size": 3598, "crc": -1958825257}, {"key": "androidx/compose/material3/NavigationBarKt.class", "name": "androidx/compose/material3/NavigationBarKt.class", "size": 42293, "crc": 78097475}, {"key": "androidx/compose/material3/NavigationDrawerItemColors.class", "name": "androidx/compose/material3/NavigationDrawerItemColors.class", "size": 1362, "crc": 2117084272}, {"key": "androidx/compose/material3/NavigationDrawerItemDefaults.class", "name": "androidx/compose/material3/NavigationDrawerItemDefaults.class", "size": 5131, "crc": 2065562092}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$1.class", "size": 2973, "crc": 2005941898}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$2.class", "size": 4484, "crc": 724182213}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$3.class", "size": 3189, "crc": 438451162}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1.class", "size": 1666, "crc": -687917965}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1$1.class", "size": 3733, "crc": -1806706459}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1.class", "size": 2678, "crc": -767400614}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1.class", "size": 2746, "crc": 2052221285}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1$1.class", "size": 2274, "crc": -1113131777}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1.class", "size": 4337, "crc": 735464984}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1.class", "size": 3229, "crc": 1310043873}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$3.class", "size": 2739, "crc": 520350083}, {"key": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$1.class", "size": 11199, "crc": 1018679865}, {"key": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$2.class", "size": 3226, "crc": -1779456959}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$1.class", "size": 2955, "crc": 1319035616}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$2.class", "size": 4460, "crc": 1533108187}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$3.class", "size": 3171, "crc": -1162089981}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1.class", "size": 1657, "crc": 2065801705}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1$1.class", "size": 3570, "crc": 1905179958}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1.class", "size": 2710, "crc": 146122253}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$3$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$3$1.class", "size": 1990, "crc": -1298546567}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$4$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$4$1.class", "size": 2471, "crc": 1612531404}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1$1.class", "size": 3685, "crc": 1778647346}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1.class", "size": 2646, "crc": 668534356}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1.class", "size": 2725, "crc": 1023841613}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1$1$1.class", "size": 2546, "crc": 1713940080}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1$1.class", "size": 5785, "crc": -1863622665}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$6$1.class", "size": 6804, "crc": 1432383657}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$3.class", "size": 2788, "crc": 1747809276}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$1.class", "size": 2299, "crc": 461381529}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$2.class", "size": 15873, "crc": 2131726248}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$3.class", "size": 3750, "crc": -421984617}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$1$1.class", "size": 1969, "crc": -473316893}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$2.class", "size": 2967, "crc": 839065645}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentNavigationDrawer$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentNavigationDrawer$2.class", "size": 2453, "crc": -1973571338}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$1$1.class", "size": 2194, "crc": -380075800}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$2.class", "size": 2197, "crc": -1655982427}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1$1.class", "size": 1693, "crc": 701400347}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1.class", "size": 4259, "crc": -486483989}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1$1.class", "size": 1538, "crc": -1624326112}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1.class", "size": 2479, "crc": 276784098}, {"key": "androidx/compose/material3/NavigationDrawerKt$predictiveBackDrawerChild$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$predictiveBackDrawerChild$1.class", "size": 2450, "crc": -477449832}, {"key": "androidx/compose/material3/NavigationDrawerKt$predictiveBackDrawerContainer$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$predictiveBackDrawerContainer$1.class", "size": 2347, "crc": -544875918}, {"key": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$1.class", "size": 1650, "crc": 1552491838}, {"key": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$2$1.class", "size": 1940, "crc": 1340023833}, {"key": "androidx/compose/material3/NavigationDrawerKt.class", "name": "androidx/compose/material3/NavigationDrawerKt.class", "size": 87819, "crc": -1836684198}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$1.class", "size": 3020, "crc": 40948800}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$2$1.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$2$1.class", "size": 1863, "crc": 1186705083}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$2.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1$2.class", "size": 4139, "crc": 244439818}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$2$1.class", "size": 6529, "crc": 1902337701}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$3$1.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$3$1.class", "size": 3940, "crc": -1739317261}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$4.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt$DrawerPredictiveBackHandler$4.class", "size": 2360, "crc": 1547348351}, {"key": "androidx/compose/material3/NavigationDrawer_androidKt.class", "name": "androidx/compose/material3/NavigationDrawer_androidKt.class", "size": 12104, "crc": 1251590415}, {"key": "androidx/compose/material3/NavigationItemColors.class", "name": "androidx/compose/material3/NavigationItemColors.class", "size": 7826, "crc": -2020864306}, {"key": "androidx/compose/material3/NavigationItemIconPosition$Companion.class", "name": "androidx/compose/material3/NavigationItemIconPosition$Companion.class", "size": 1277, "crc": 926225755}, {"key": "androidx/compose/material3/NavigationItemIconPosition.class", "name": "androidx/compose/material3/NavigationItemIconPosition.class", "size": 2705, "crc": 2025937539}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$1$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$1$1.class", "size": 2013, "crc": -240221342}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$2$3$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$2$3$1.class", "size": 1855, "crc": -2043769692}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$3.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$3.class", "size": 4461, "crc": -705868332}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1$1.class", "size": 3158, "crc": 53829481}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1$2.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1$2.class", "size": 3165, "crc": 1319501773}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$iconWithBadge$1.class", "size": 3946, "crc": -953793713}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledIcon$1$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledIcon$1$1.class", "size": 1651, "crc": 1885719591}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledIcon$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledIcon$1.class", "size": 11080, "crc": 1801375598}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledLabel$1$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItem$styledLabel$1$1.class", "size": 3857, "crc": 1488507503}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItemLayout$1$1$1.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItemLayout$1$1$1.class", "size": 2141, "crc": 2144964897}, {"key": "androidx/compose/material3/NavigationItemKt$NavigationItemLayout$2.class", "name": "androidx/compose/material3/NavigationItemKt$NavigationItemLayout$2.class", "size": 3563, "crc": -842707418}, {"key": "androidx/compose/material3/NavigationItemKt$placeIcon$1.class", "name": "androidx/compose/material3/NavigationItemKt$placeIcon$1.class", "size": 2546, "crc": 1857085303}, {"key": "androidx/compose/material3/NavigationItemKt$placeLabelAndStartIcon$1.class", "name": "androidx/compose/material3/NavigationItemKt$placeLabelAndStartIcon$1.class", "size": 2827, "crc": -1154697588}, {"key": "androidx/compose/material3/NavigationItemKt$placeLabelAndTopIcon$1.class", "name": "androidx/compose/material3/NavigationItemKt$placeLabelAndTopIcon$1.class", "size": 2823, "crc": 467444827}, {"key": "androidx/compose/material3/NavigationItemKt.class", "name": "androidx/compose/material3/NavigationItemKt.class", "size": 40465, "crc": -1726654150}, {"key": "androidx/compose/material3/NavigationRailDefaults.class", "name": "androidx/compose/material3/NavigationRailDefaults.class", "size": 3823, "crc": 1138411601}, {"key": "androidx/compose/material3/NavigationRailItemColors.class", "name": "androidx/compose/material3/NavigationRailItemColors.class", "size": 8089, "crc": -1237531333}, {"key": "androidx/compose/material3/NavigationRailItemDefaults.class", "name": "androidx/compose/material3/NavigationRailItemDefaults.class", "size": 6953, "crc": 847244398}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRail$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRail$1.class", "size": 11818, "crc": -554815988}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRail$2.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRail$2.class", "size": 2933, "crc": -1412684388}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$2$1.class", "size": 1753, "crc": 1796305199}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicator$1$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicator$1$1$1.class", "size": 1983, "crc": -1275624923}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicator$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicator$1.class", "size": 6206, "crc": 589277309}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicatorRipple$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$1$indicatorRipple$1.class", "size": 4044, "crc": -1390729654}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2.class", "size": 3424, "crc": -2097240232}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1$1.class", "size": 1663, "crc": 2071157291}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1.class", "size": 12397, "crc": 813821507}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "size": 6127, "crc": -908302341}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$1$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$1$2$1.class", "size": 2225, "crc": 343020310}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$2$1.class", "size": 9603, "crc": 1912260458}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$3.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$3.class", "size": 3037, "crc": 1609902616}, {"key": "androidx/compose/material3/NavigationRailKt$placeIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$placeIcon$1.class", "size": 2804, "crc": 382794603}, {"key": "androidx/compose/material3/NavigationRailKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$placeLabelAndIcon$1.class", "size": 3613, "crc": 2011983589}, {"key": "androidx/compose/material3/NavigationRailKt.class", "name": "androidx/compose/material3/NavigationRailKt.class", "size": 41015, "crc": -1401963653}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$Container$1.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$Container$1.class", "size": 1300, "crc": -293112567}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$Container$2.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$Container$2.class", "size": 2782, "crc": 848688748}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$ContainerBox$1.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$ContainerBox$1.class", "size": 2648, "crc": -1190012126}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$1.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$1.class", "size": 3930, "crc": -247073827}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$2.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$2.class", "size": 5472, "crc": 1746052618}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults.class", "size": 33002, "crc": 401144660}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$1.class", "size": 1628, "crc": -919491784}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$3$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$3$1.class", "size": 3488, "crc": -1293142582}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$3.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1$3.class", "size": 6989, "crc": 2142702690}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$1.class", "size": 11820, "crc": 1417654340}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2.class", "size": 6159, "crc": 2056508167}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$1.class", "size": 1628, "crc": 714000286}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$3$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$3$1.class", "size": 3488, "crc": 301044949}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$3.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3$3.class", "size": 7189, "crc": 446188942}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3.class", "size": 12052, "crc": 1211077073}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$4.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$4.class", "size": 6333, "crc": 1937529081}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "size": 4940, "crc": -861311974}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "size": 819, "crc": 1656301562}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1.class", "size": 6245, "crc": 2129611281}, {"key": "androidx/compose/material3/OutlinedTextFieldKt.class", "name": "androidx/compose/material3/OutlinedTextFieldKt.class", "size": 67954, "crc": -779068452}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1922, "crc": 1782498116}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1919, "crc": -551341459}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$measure$1.class", "size": 4190, "crc": 628542931}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1922, "crc": 334206294}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1919, "crc": -1058810723}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy.class", "size": 33752, "crc": 1642562137}, {"key": "androidx/compose/material3/PinnedScrollBehavior$1.class", "name": "androidx/compose/material3/PinnedScrollBehavior$1.class", "size": 1352, "crc": 1386167995}, {"key": "androidx/compose/material3/PinnedScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/PinnedScrollBehavior$nestedScrollConnection$1.class", "size": 2205, "crc": 1839075076}, {"key": "androidx/compose/material3/PinnedScrollBehavior.class", "name": "androidx/compose/material3/PinnedScrollBehavior.class", "size": 4044, "crc": -1351154019}, {"key": "androidx/compose/material3/ProgressIndicatorDefaults.class", "name": "androidx/compose/material3/ProgressIndicatorDefaults.class", "size": 9674, "crc": 550560641}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$1.class", "size": 2357, "crc": -812892652}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$10.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$10.class", "size": 1941, "crc": -1871450032}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$2$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$2$1.class", "size": 2512, "crc": 1844770574}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "size": 4445, "crc": -591080123}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$4.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$4.class", "size": 2409, "crc": 1236409472}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$5$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$5$1.class", "size": 3408, "crc": -877019762}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$6.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$6.class", "size": 2046, "crc": 1822446480}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$7$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$7$1.class", "size": 1360, "crc": -245110479}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$8.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$8.class", "size": 2097, "crc": 1647363804}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$9.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$9.class", "size": 1990, "crc": 590895958}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$coercedProgress$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$coercedProgress$1$1.class", "size": 1748, "crc": -54920942}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$endAngle$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$endAngle$1.class", "size": 2662, "crc": -1837261483}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$startAngle$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$startAngle$1.class", "size": 2705, "crc": 1672266694}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1$1.class", "size": 2059, "crc": -427603899}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1.class", "size": 3060, "crc": 919646014}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$2.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$2.class", "size": 1561, "crc": 259229728}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$1.class", "size": 2291, "crc": -806233642}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$10.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$10.class", "size": 2038, "crc": 393957664}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$11.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$11.class", "size": 1985, "crc": -417100956}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$12.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$12.class", "size": 1930, "crc": -910963327}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$2$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$2$1.class", "size": 1968, "crc": 1064439046}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "size": 2537, "crc": 1039611841}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$4$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$4$1.class", "size": 4597, "crc": -2129434948}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$5.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$5.class", "size": 2711, "crc": -768275842}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$6.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$6.class", "size": 1981, "crc": -1553966951}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$7$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$7$1.class", "size": 5039, "crc": -1627839495}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$8.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$8.class", "size": 2036, "crc": 1732617319}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$9$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$9$1.class", "size": 1353, "crc": -1467966511}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$coercedProgress$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$coercedProgress$1$1.class", "size": 1773, "crc": 1579072712}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$1.class", "size": 2665, "crc": 1639303827}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$1.class", "size": 2667, "crc": 1322263794}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$1.class", "size": 2670, "crc": -1150666889}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$1.class", "size": 2670, "crc": 2053444502}, {"key": "androidx/compose/material3/ProgressIndicatorKt.class", "name": "androidx/compose/material3/ProgressIndicatorKt.class", "size": 52870, "crc": -2060244040}, {"key": "androidx/compose/material3/RadioButtonColors.class", "name": "androidx/compose/material3/RadioButtonColors.class", "size": 7565, "crc": -1311122844}, {"key": "androidx/compose/material3/RadioButtonDefaults.class", "name": "androidx/compose/material3/RadioButtonDefaults.class", "size": 5292, "crc": -2004394269}, {"key": "androidx/compose/material3/RadioButtonKt$RadioButton$1$1.class", "name": "androidx/compose/material3/RadioButtonKt$RadioButton$1$1.class", "size": 4410, "crc": 1169293951}, {"key": "androidx/compose/material3/RadioButtonKt$RadioButton$2.class", "name": "androidx/compose/material3/RadioButtonKt$RadioButton$2.class", "size": 2721, "crc": -914226456}, {"key": "androidx/compose/material3/RadioButtonKt.class", "name": "androidx/compose/material3/RadioButtonKt.class", "size": 10797, "crc": 1521138205}, {"key": "androidx/compose/material3/RangeSliderComponents.class", "name": "androidx/compose/material3/RangeSliderComponents.class", "size": 1507, "crc": 1822458699}, {"key": "androidx/compose/material3/RangeSliderLogic$captureThumb$1.class", "name": "androidx/compose/material3/RangeSliderLogic$captureThumb$1.class", "size": 4048, "crc": -1754136941}, {"key": "androidx/compose/material3/RangeSliderLogic.class", "name": "androidx/compose/material3/RangeSliderLogic.class", "size": 3753, "crc": 1233879203}, {"key": "androidx/compose/material3/RangeSliderState$gestureEndAction$1.class", "name": "androidx/compose/material3/RangeSliderState$gestureEndAction$1.class", "size": 1630, "crc": -1820135967}, {"key": "androidx/compose/material3/RangeSliderState.class", "name": "androidx/compose/material3/RangeSliderState.class", "size": 17295, "crc": -1792538517}, {"key": "androidx/compose/material3/RichTooltipColors.class", "name": "androidx/compose/material3/RichTooltipColors.class", "size": 5187, "crc": -2045913484}, {"key": "androidx/compose/material3/RippleConfiguration.class", "name": "androidx/compose/material3/RippleConfiguration.class", "size": 3344, "crc": -459339473}, {"key": "androidx/compose/material3/RippleDefaults.class", "name": "androidx/compose/material3/RippleDefaults.class", "size": 1210, "crc": 1083595423}, {"key": "androidx/compose/material3/RippleKt$LocalRippleConfiguration$1.class", "name": "androidx/compose/material3/RippleKt$LocalRippleConfiguration$1.class", "size": 1437, "crc": 1074782883}, {"key": "androidx/compose/material3/RippleKt$LocalUseFallbackRippleImplementation$1.class", "name": "androidx/compose/material3/RippleKt$LocalUseFallbackRippleImplementation$1.class", "size": 1257, "crc": -1387422304}, {"key": "androidx/compose/material3/RippleKt.class", "name": "androidx/compose/material3/RippleKt.class", "size": 7728, "crc": 343594513}, {"key": "androidx/compose/material3/RippleNodeFactory$create$colorProducer$1.class", "name": "androidx/compose/material3/RippleNodeFactory$create$colorProducer$1.class", "size": 1199, "crc": 1642674186}, {"key": "androidx/compose/material3/RippleNodeFactory.class", "name": "androidx/compose/material3/RippleNodeFactory.class", "size": 4323, "crc": -78522812}, {"key": "androidx/compose/material3/ScaffoldDefaults.class", "name": "androidx/compose/material3/ScaffoldDefaults.class", "size": 2415, "crc": 581683214}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$1$1.class", "size": 2313, "crc": -486053344}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$2.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$2.class", "size": 4511, "crc": -990308970}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$3.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$3.class", "size": 3756, "crc": 562735776}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$1.class", "size": 6883, "crc": 537034140}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "size": 5121, "crc": 908481162}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "size": 2713, "crc": -1293739436}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1$1.class", "size": 17367, "crc": -33207533}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$2.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$2.class", "size": 3427, "crc": -146535298}, {"key": "androidx/compose/material3/ScaffoldKt.class", "name": "androidx/compose/material3/ScaffoldKt.class", "size": 15696, "crc": -1552458800}, {"key": "androidx/compose/material3/ScaffoldLayoutContent.class", "name": "androidx/compose/material3/ScaffoldLayoutContent.class", "size": 1631, "crc": -1744410358}, {"key": "androidx/compose/material3/ScrollableTabData$onLaidOut$1$1.class", "name": "androidx/compose/material3/ScrollableTabData$onLaidOut$1$1.class", "size": 3955, "crc": 210930085}, {"key": "androidx/compose/material3/ScrollableTabData.class", "name": "androidx/compose/material3/ScrollableTabData.class", "size": 4245, "crc": 568359306}, {"key": "androidx/compose/material3/ScrolledOffset.class", "name": "androidx/compose/material3/ScrolledOffset.class", "size": 445, "crc": -1024420551}, {"key": "androidx/compose/material3/SearchBarColors.class", "name": "androidx/compose/material3/SearchBarColors.class", "size": 3650, "crc": -496433369}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$1$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$1$1.class", "size": 2224, "crc": 1888964228}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$2$1$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$2$1$1.class", "size": 1481, "crc": -970339376}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$2$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$2$1.class", "size": 2853, "crc": 810718273}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$3$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$3$1.class", "size": 2250, "crc": 1542315233}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$4$1$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$4$1$1.class", "size": 9341, "crc": 137207075}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$4$2$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$4$2$1.class", "size": 9587, "crc": -1264812152}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$4.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$4.class", "size": 7609, "crc": 1158174402}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$5$1.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$5$1.class", "size": 4101, "crc": -392624145}, {"key": "androidx/compose/material3/SearchBarDefaults$InputField$6.class", "name": "androidx/compose/material3/SearchBarDefaults$InputField$6.class", "size": 4420, "crc": -1313444769}, {"key": "androidx/compose/material3/SearchBarDefaults.class", "name": "androidx/compose/material3/SearchBarDefaults.class", "size": 33886, "crc": 910013538}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$1$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$1$1$1.class", "size": 13675, "crc": 286509719}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$1.class", "size": 11586, "crc": -972320156}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2$1.class", "size": 1781, "crc": 598303946}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$3.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$3.class", "size": 3570, "crc": -1705803173}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$4.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$4.class", "size": 5846, "crc": -682161629}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$5.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$5.class", "size": 5030, "crc": 1758358143}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$1$1.class", "size": 6015, "crc": -1894034111}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1$1$1.class", "size": 3510, "crc": -1554250955}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1$1.class", "size": 6042, "crc": 1737858475}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1.class", "size": 5923, "crc": 677035841}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3.class", "size": 3805, "crc": -1571015655}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4.class", "size": 5871, "crc": 549247877}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$5.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$5.class", "size": 5265, "crc": 1036404970}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$1.class", "size": 4746, "crc": -2060134930}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$animatedShape$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$animatedShape$1$1.class", "size": 5060, "crc": -622282718}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$showContent$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$showContent$2$1.class", "size": 2227, "crc": 1650351617}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$surface$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$surface$1.class", "size": 3864, "crc": 1391595372}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$useFullScreenShape$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$useFullScreenShape$2$1.class", "size": 2241, "crc": -1070765637}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$wrappedContent$1$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$wrappedContent$1$1$1.class", "size": 2149, "crc": 912363580}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$wrappedContent$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarImpl$wrappedContent$1.class", "size": 12515, "crc": 1759316643}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$1$1.class", "size": 2453, "crc": -1027509827}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$2$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$2$1$1.class", "size": 4566, "crc": 790462061}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$2$1.class", "size": 10255, "crc": -1820299609}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$4.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarLayout$4.class", "size": 4108, "crc": -819494386}, {"key": "androidx/compose/material3/SearchBar_androidKt.class", "name": "androidx/compose/material3/SearchBar_androidKt.class", "size": 70724, "crc": 1559756004}, {"key": "androidx/compose/material3/SegmentedButtonColors.class", "name": "androidx/compose/material3/SegmentedButtonColors.class", "size": 12248, "crc": 857802483}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$1.class", "size": 4455, "crc": -447428169}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$2.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$2.class", "size": 5043, "crc": 747909916}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy.class", "size": 10829, "crc": -2109573966}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$ActiveIcon$1.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$ActiveIcon$1.class", "size": 1705, "crc": -1935366340}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$1.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$1.class", "size": 3036, "crc": -314661808}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$2.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$2.class", "size": 3383, "crc": -893861515}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$3.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$3.class", "size": 2504, "crc": -1447580936}, {"key": "androidx/compose/material3/SegmentedButtonDefaults.class", "name": "androidx/compose/material3/SegmentedButtonDefaults.class", "size": 16565, "crc": 1964753312}, {"key": "androidx/compose/material3/SegmentedButtonKt$MultiChoiceSegmentedButtonRow$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$MultiChoiceSegmentedButtonRow$2.class", "size": 2420, "crc": -1358776968}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$1.class", "size": 2781, "crc": -953527083}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$2.class", "size": 3341, "crc": 530259745}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$3.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$3.class", "size": 4192, "crc": -354476438}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$4.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$4.class", "size": 2784, "crc": -1439292449}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$5.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$5.class", "size": 2359, "crc": -733072305}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$6.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$6.class", "size": 3343, "crc": -51217697}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$7.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$7.class", "size": 4150, "crc": -602481264}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$1$1.class", "size": 12451, "crc": -1477986099}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$2.class", "size": 2242, "crc": -2004549271}, {"key": "androidx/compose/material3/SegmentedButtonKt$SingleChoiceSegmentedButtonRow$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SingleChoiceSegmentedButtonRow$2.class", "size": 2425, "crc": -658894564}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1$1.class", "size": 2678, "crc": -1678335672}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1.class", "size": 4311, "crc": 1493721447}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1$1.class", "size": 2473, "crc": 1495884906}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1.class", "size": 3096, "crc": -928044}, {"key": "androidx/compose/material3/SegmentedButtonKt.class", "name": "androidx/compose/material3/SegmentedButtonKt.class", "size": 37572, "crc": 1834175327}, {"key": "androidx/compose/material3/SelectableChipColors.class", "name": "androidx/compose/material3/SelectableChipColors.class", "size": 11852, "crc": 585647455}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1$1.class", "size": 4204, "crc": 925577966}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1.class", "size": 4491, "crc": -357003998}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$2$1.class", "size": 5500, "crc": 1450297980}, {"key": "androidx/compose/material3/SelectableChipElevation.class", "name": "androidx/compose/material3/SelectableChipElevation.class", "size": 12741, "crc": 148823964}, {"key": "androidx/compose/material3/SelectableDates.class", "name": "androidx/compose/material3/SelectableDates.class", "size": 890, "crc": 1856400765}, {"key": "androidx/compose/material3/SelectedRangeInfo$Companion.class", "name": "androidx/compose/material3/SelectedRangeInfo$Companion.class", "size": 2584, "crc": -2039906054}, {"key": "androidx/compose/material3/SelectedRangeInfo.class", "name": "androidx/compose/material3/SelectedRangeInfo.class", "size": 2151, "crc": -1063550879}, {"key": "androidx/compose/material3/ShapeDefaults.class", "name": "androidx/compose/material3/ShapeDefaults.class", "size": 2147, "crc": -543149424}, {"key": "androidx/compose/material3/Shapes.class", "name": "androidx/compose/material3/Shapes.class", "size": 5243, "crc": 1298916350}, {"key": "androidx/compose/material3/ShapesKt$LocalShapes$1.class", "name": "androidx/compose/material3/ShapesKt$LocalShapes$1.class", "size": 1573, "crc": -1703115420}, {"key": "androidx/compose/material3/ShapesKt$WhenMappings.class", "name": "androidx/compose/material3/ShapesKt$WhenMappings.class", "size": 1325, "crc": 789984016}, {"key": "androidx/compose/material3/ShapesKt.class", "name": "androidx/compose/material3/ShapesKt.class", "size": 6492, "crc": 812329936}, {"key": "androidx/compose/material3/SheetDefaultsKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 5916, "crc": 236898835}, {"key": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$1.class", "size": 1631, "crc": 192841012}, {"key": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$2$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$2$1.class", "size": 2221, "crc": 739996596}, {"key": "androidx/compose/material3/SheetDefaultsKt.class", "name": "androidx/compose/material3/SheetDefaultsKt.class", "size": 9369, "crc": 1999938505}, {"key": "androidx/compose/material3/SheetState$1.class", "name": "androidx/compose/material3/SheetState$1.class", "size": 1670, "crc": 524643016}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$1.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$1.class", "size": 2081, "crc": 1411882356}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$2.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$2.class", "size": 2471, "crc": -88183515}, {"key": "androidx/compose/material3/SheetState$Companion.class", "name": "androidx/compose/material3/SheetState$Companion.class", "size": 2461, "crc": 1001622000}, {"key": "androidx/compose/material3/SheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/SheetState$anchoredDraggableState$1.class", "size": 2664, "crc": 751511103}, {"key": "androidx/compose/material3/SheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material3/SheetState$anchoredDraggableState$2.class", "size": 2525, "crc": -173086387}, {"key": "androidx/compose/material3/SheetState.class", "name": "androidx/compose/material3/SheetState.class", "size": 10014, "crc": 2121759324}, {"key": "androidx/compose/material3/SheetValue.class", "name": "androidx/compose/material3/SheetValue.class", "size": 1558, "crc": 1612873498}, {"key": "androidx/compose/material3/SingleChoiceSegmentedButtonRowScope.class", "name": "androidx/compose/material3/SingleChoiceSegmentedButtonRowScope.class", "size": 541, "crc": -1721882017}, {"key": "androidx/compose/material3/SingleChoiceSegmentedButtonScopeWrapper.class", "name": "androidx/compose/material3/SingleChoiceSegmentedButtonScopeWrapper.class", "size": 3181, "crc": 732797126}, {"key": "androidx/compose/material3/SliderColors.class", "name": "androidx/compose/material3/SliderColors.class", "size": 10241, "crc": 45831058}, {"key": "androidx/compose/material3/SliderComponents.class", "name": "androidx/compose/material3/SliderComponents.class", "size": 1410, "crc": 117712079}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$1$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$1$1$1.class", "size": 3356, "crc": -558894027}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$1$1.class", "size": 4458, "crc": 1029789028}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$2.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$2.class", "size": 2426, "crc": -1934277584}, {"key": "androidx/compose/material3/SliderDefaults$Track$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$1$1.class", "size": 8948, "crc": 129919332}, {"key": "androidx/compose/material3/SliderDefaults$Track$10.class", "name": "androidx/compose/material3/SliderDefaults$Track$10.class", "size": 2225, "crc": 1121792203}, {"key": "androidx/compose/material3/SliderDefaults$Track$11$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$11$1.class", "size": 3923, "crc": 1091708679}, {"key": "androidx/compose/material3/SliderDefaults$Track$12.class", "name": "androidx/compose/material3/SliderDefaults$Track$12.class", "size": 3557, "crc": -348523962}, {"key": "androidx/compose/material3/SliderDefaults$Track$2.class", "name": "androidx/compose/material3/SliderDefaults$Track$2.class", "size": 2293, "crc": 2066090539}, {"key": "androidx/compose/material3/SliderDefaults$Track$3.class", "name": "androidx/compose/material3/SliderDefaults$Track$3.class", "size": 2277, "crc": -1194427506}, {"key": "androidx/compose/material3/SliderDefaults$Track$4$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$4$1.class", "size": 2339, "crc": 1011106962}, {"key": "androidx/compose/material3/SliderDefaults$Track$5.class", "name": "androidx/compose/material3/SliderDefaults$Track$5.class", "size": 2218, "crc": 1278259542}, {"key": "androidx/compose/material3/SliderDefaults$Track$6$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$6$1.class", "size": 3768, "crc": 883116445}, {"key": "androidx/compose/material3/SliderDefaults$Track$7.class", "name": "androidx/compose/material3/SliderDefaults$Track$7.class", "size": 3530, "crc": 1891267679}, {"key": "androidx/compose/material3/SliderDefaults$Track$8.class", "name": "androidx/compose/material3/SliderDefaults$Track$8.class", "size": 2297, "crc": 89618403}, {"key": "androidx/compose/material3/SliderDefaults$Track$9$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$9$1.class", "size": 2344, "crc": -1179323823}, {"key": "androidx/compose/material3/SliderDefaults.class", "name": "androidx/compose/material3/SliderDefaults.class", "size": 40949, "crc": 2020770554}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$1.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$1.class", "size": 3375, "crc": 1581343840}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$10$1.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$10$1.class", "size": 2337, "crc": -2001923414}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$11.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$11.class", "size": 4888, "crc": 468526585}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$14.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$14.class", "size": 3398, "crc": -697764397}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$15.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$15.class", "size": 3396, "crc": 306893715}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$16.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$16.class", "size": 3268, "crc": -2099954474}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$18.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$18.class", "size": 3868, "crc": -927058130}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$2.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$2.class", "size": 3373, "crc": 136327870}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$3.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$3.class", "size": 3245, "crc": -1737445939}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$4.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$4.class", "size": 3242, "crc": 1696322781}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$7.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$7.class", "size": 3497, "crc": 2077524199}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$8.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$8.class", "size": 3495, "crc": -732439769}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$9.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$9.class", "size": 3367, "crc": 135637795}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$1$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$1$1.class", "size": 1928, "crc": -706839676}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$2$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$2$1.class", "size": 2085, "crc": 601609303}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$4$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$4$1.class", "size": 1926, "crc": -416612627}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$5$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$5$1.class", "size": 2083, "crc": -157723391}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1$1.class", "size": 2501, "crc": -1960518705}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1.class", "size": 7379, "crc": 2141387241}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$3.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$3.class", "size": 3640, "crc": 196966659}, {"key": "androidx/compose/material3/SliderKt$Slider$10.class", "name": "androidx/compose/material3/SliderKt$Slider$10.class", "size": 3250, "crc": 223621185}, {"key": "androidx/compose/material3/SliderKt$Slider$11.class", "name": "androidx/compose/material3/SliderKt$Slider$11.class", "size": 3098, "crc": 1823681092}, {"key": "androidx/compose/material3/SliderKt$Slider$13.class", "name": "androidx/compose/material3/SliderKt$Slider$13.class", "size": 3277, "crc": -1462763574}, {"key": "androidx/compose/material3/SliderKt$Slider$2.class", "name": "androidx/compose/material3/SliderKt$Slider$2.class", "size": 3356, "crc": -1593837764}, {"key": "androidx/compose/material3/SliderKt$Slider$3.class", "name": "androidx/compose/material3/SliderKt$Slider$3.class", "size": 3204, "crc": 204185046}, {"key": "androidx/compose/material3/SliderKt$Slider$4.class", "name": "androidx/compose/material3/SliderKt$Slider$4.class", "size": 3329, "crc": 872016009}, {"key": "androidx/compose/material3/SliderKt$Slider$6.class", "name": "androidx/compose/material3/SliderKt$Slider$6.class", "size": 3315, "crc": 60936474}, {"key": "androidx/compose/material3/SliderKt$Slider$7.class", "name": "androidx/compose/material3/SliderKt$Slider$7.class", "size": 3163, "crc": 1255940063}, {"key": "androidx/compose/material3/SliderKt$Slider$8.class", "name": "androidx/compose/material3/SliderKt$Slider$8.class", "size": 4091, "crc": 1378804339}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$1$1$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$1$1$1.class", "size": 1794, "crc": 1228300620}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$2$1$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$2$1$1.class", "size": 2247, "crc": -1551408934}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$2$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$2$1.class", "size": 6004, "crc": 1970295102}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$3.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$3.class", "size": 3044, "crc": 1743412354}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$drag$1$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$drag$1$1.class", "size": 3487, "crc": -1259220152}, {"key": "androidx/compose/material3/SliderKt$awaitSlop$1.class", "name": "androidx/compose/material3/SliderKt$awaitSlop$1.class", "size": 1564, "crc": -528619634}, {"key": "androidx/compose/material3/SliderKt$awaitSlop$postPointerSlop$1.class", "name": "androidx/compose/material3/SliderKt$awaitSlop$postPointerSlop$1.class", "size": 2007, "crc": 1075805078}, {"key": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1$1.class", "size": 4275, "crc": 1525762885}, {"key": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1.class", "size": 2595, "crc": -1551700200}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "size": 4533, "crc": -1094026996}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "size": 2589, "crc": 1454601783}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "size": 10298, "crc": -555758475}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1.class", "size": 4401, "crc": -593258226}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1.class", "size": 5050, "crc": -1815270487}, {"key": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1$1.class", "size": 4289, "crc": 441792026}, {"key": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1.class", "size": 2603, "crc": 1523039926}, {"key": "androidx/compose/material3/SliderKt$sliderSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$sliderSemantics$1$1.class", "size": 3491, "crc": 1137345372}, {"key": "androidx/compose/material3/SliderKt$sliderSemantics$1.class", "name": "androidx/compose/material3/SliderKt$sliderSemantics$1.class", "size": 2160, "crc": 1351253202}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1$1.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1$1.class", "size": 3428, "crc": 1024170681}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1$2.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1$2.class", "size": 1722, "crc": 143242177}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1.class", "size": 4321, "crc": -1183854893}, {"key": "androidx/compose/material3/SliderKt.class", "name": "androidx/compose/material3/SliderKt.class", "size": 77752, "crc": -282711358}, {"key": "androidx/compose/material3/SliderPositions.class", "name": "androidx/compose/material3/SliderPositions.class", "size": 4867, "crc": 1837894635}, {"key": "androidx/compose/material3/SliderRange$Companion.class", "name": "androidx/compose/material3/SliderRange$Companion.class", "size": 1254, "crc": 1159755797}, {"key": "androidx/compose/material3/SliderRange.class", "name": "androidx/compose/material3/SliderRange.class", "size": 5045, "crc": 843003144}, {"key": "androidx/compose/material3/SliderState$drag$2.class", "name": "androidx/compose/material3/SliderState$drag$2.class", "size": 4618, "crc": -2084328638}, {"key": "androidx/compose/material3/SliderState$dragScope$1.class", "name": "androidx/compose/material3/SliderState$dragScope$1.class", "size": 1148, "crc": 554350749}, {"key": "androidx/compose/material3/SliderState$gestureEndAction$1.class", "name": "androidx/compose/material3/SliderState$gestureEndAction$1.class", "size": 1435, "crc": 1449063968}, {"key": "androidx/compose/material3/SliderState.class", "name": "androidx/compose/material3/SliderState.class", "size": 16223, "crc": -1284033937}, {"key": "androidx/compose/material3/SnackbarData.class", "name": "androidx/compose/material3/SnackbarData.class", "size": 779, "crc": -1081789074}, {"key": "androidx/compose/material3/SnackbarDefaults.class", "name": "androidx/compose/material3/SnackbarDefaults.class", "size": 4625, "crc": 507467256}, {"key": "androidx/compose/material3/SnackbarDuration.class", "name": "androidx/compose/material3/SnackbarDuration.class", "size": 1479, "crc": -1627191896}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "size": 1580, "crc": -826297303}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "size": 2437, "crc": 1685251822}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "size": 2175, "crc": -685301495}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "size": 2449, "crc": -1570917447}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "size": 14271, "crc": -2065623174}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "size": 3315, "crc": -818521551}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "size": 2534, "crc": -196539452}, {"key": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$1$1.class", "size": 4464, "crc": -926127880}, {"key": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$2.class", "name": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$2.class", "size": 2490, "crc": -477887326}, {"key": "androidx/compose/material3/SnackbarHostKt$WhenMappings.class", "name": "androidx/compose/material3/SnackbarHostKt$WhenMappings.class", "size": 869, "crc": -2096692653}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$1.class", "size": 1194, "crc": 972319760}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$2$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$2$1.class", "size": 4802, "crc": 479185853}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedScale$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedScale$1$1.class", "size": 4474, "crc": 335363853}, {"key": "androidx/compose/material3/SnackbarHostKt.class", "name": "androidx/compose/material3/SnackbarHostKt.class", "size": 24904, "crc": -450430308}, {"key": "androidx/compose/material3/SnackbarHostState$SnackbarDataImpl.class", "name": "androidx/compose/material3/SnackbarHostState$SnackbarDataImpl.class", "size": 3080, "crc": -2056063201}, {"key": "androidx/compose/material3/SnackbarHostState$SnackbarVisualsImpl.class", "name": "androidx/compose/material3/SnackbarHostState$SnackbarVisualsImpl.class", "size": 2868, "crc": 466381703}, {"key": "androidx/compose/material3/SnackbarHostState$showSnackbar$2.class", "name": "androidx/compose/material3/SnackbarHostState$showSnackbar$2.class", "size": 1941, "crc": -297901070}, {"key": "androidx/compose/material3/SnackbarHostState.class", "name": "androidx/compose/material3/SnackbarHostState.class", "size": 8873, "crc": 47396344}, {"key": "androidx/compose/material3/SnackbarKt$NewLineButtonSnackbar$2.class", "name": "androidx/compose/material3/SnackbarKt$NewLineButtonSnackbar$2.class", "size": 2796, "crc": -1939660508}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1$2.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1$2.class", "size": 2518, "crc": -707837408}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1.class", "size": 8759, "crc": -632812212}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$3.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$3.class", "size": 2758, "crc": 699172589}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$1$1.class", "size": 3841, "crc": -1288906461}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$1.class", "size": 4914, "crc": -735744400}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$2.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$2.class", "size": 3112, "crc": -322601755}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$3.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$3.class", "size": 3154, "crc": -1221222565}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$4.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$4.class", "size": 2444, "crc": 66755046}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "size": 1336, "crc": -1933195592}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$2.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$2.class", "size": 3149, "crc": 2112006499}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1.class", "size": 5801, "crc": 2088556359}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1$1$1.class", "size": 1351, "crc": 1228921478}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1.class", "size": 5000, "crc": 166969730}, {"key": "androidx/compose/material3/SnackbarKt.class", "name": "androidx/compose/material3/SnackbarKt.class", "size": 40464, "crc": 2004420527}, {"key": "androidx/compose/material3/SnackbarResult.class", "name": "androidx/compose/material3/SnackbarResult.class", "size": 1418, "crc": -693983255}, {"key": "androidx/compose/material3/SnackbarVisuals.class", "name": "androidx/compose/material3/SnackbarVisuals.class", "size": 1031, "crc": -1403874004}, {"key": "androidx/compose/material3/StartIconMeasurePolicy.class", "name": "androidx/compose/material3/StartIconMeasurePolicy.class", "size": 13753, "crc": 1296665515}, {"key": "androidx/compose/material3/SuggestionChipDefaults.class", "name": "androidx/compose/material3/SuggestionChipDefaults.class", "size": 13622, "crc": 144698492}, {"key": "androidx/compose/material3/SurfaceKt$LocalAbsoluteTonalElevation$1.class", "name": "androidx/compose/material3/SurfaceKt$LocalAbsoluteTonalElevation$1.class", "size": 2054, "crc": -2121095437}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1$2.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1$2.class", "size": 1713, "crc": 859141007}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1$3.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1$3.class", "size": 3179, "crc": 1485087409}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1.class", "size": 11509, "crc": 1028080761}, {"key": "androidx/compose/material3/SurfaceKt$Surface$2.class", "name": "androidx/compose/material3/SurfaceKt$Surface$2.class", "size": 12186, "crc": 692761984}, {"key": "androidx/compose/material3/SurfaceKt$Surface$3.class", "name": "androidx/compose/material3/SurfaceKt$Surface$3.class", "size": 12237, "crc": 211433794}, {"key": "androidx/compose/material3/SurfaceKt$Surface$4.class", "name": "androidx/compose/material3/SurfaceKt$Surface$4.class", "size": 12318, "crc": -393538366}, {"key": "androidx/compose/material3/SurfaceKt.class", "name": "androidx/compose/material3/SurfaceKt.class", "size": 17827, "crc": -1030651124}, {"key": "androidx/compose/material3/SwipeToDismissBoxDefaults$positionalThreshold$1$1$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxDefaults$positionalThreshold$1$1$1.class", "size": 2583, "crc": 1421812665}, {"key": "androidx/compose/material3/SwipeToDismissBoxDefaults.class", "name": "androidx/compose/material3/SwipeToDismissBoxDefaults.class", "size": 5091, "crc": -788140504}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$1$1$1$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$1$1$1$1.class", "size": 2409, "crc": -510077658}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$1$1$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$1$1$1.class", "size": 3282, "crc": -1849562277}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$2.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$2.class", "size": 3036, "crc": 374165177}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$1.class", "size": 1785, "crc": 1703523606}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$2$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$2$1.class", "size": 2524, "crc": 1970914015}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt.class", "size": 24057, "crc": -2143132828}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$1.class", "size": 1800, "crc": 2013889228}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$1.class", "size": 2247, "crc": -2087486784}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$2.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$2.class", "size": 2784, "crc": -1490088952}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion.class", "size": 2775, "crc": 473782640}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$anchoredDraggableState$1.class", "size": 2475, "crc": 329449084}, {"key": "androidx/compose/material3/SwipeToDismissBoxState.class", "name": "androidx/compose/material3/SwipeToDismissBoxState.class", "size": 6893, "crc": 639640489}, {"key": "androidx/compose/material3/SwipeToDismissBoxValue.class", "name": "androidx/compose/material3/SwipeToDismissBoxValue.class", "size": 1528, "crc": -907879544}, {"key": "androidx/compose/material3/SwitchColors.class", "name": "androidx/compose/material3/SwitchColors.class", "size": 15412, "crc": 120186328}, {"key": "androidx/compose/material3/SwitchDefaults.class", "name": "androidx/compose/material3/SwitchDefaults.class", "size": 8812, "crc": -1631990910}, {"key": "androidx/compose/material3/SwitchKt$Switch$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$1.class", "size": 3080, "crc": 847446684}, {"key": "androidx/compose/material3/SwitchKt$SwitchImpl$2.class", "name": "androidx/compose/material3/SwitchKt$SwitchImpl$2.class", "size": 2935, "crc": -1257585471}, {"key": "androidx/compose/material3/SwitchKt.class", "name": "androidx/compose/material3/SwitchKt.class", "size": 22750, "crc": 122870518}, {"key": "androidx/compose/material3/TabIndicatorModifier.class", "name": "androidx/compose/material3/TabIndicatorModifier.class", "size": 5615, "crc": 410301079}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$1.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$1.class", "size": 1726, "crc": 619485589}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$2.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$2.class", "size": 4330, "crc": 35408872}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$3.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$3.class", "size": 4331, "crc": 1133228411}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$4.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$4.class", "size": 2221, "crc": 800693704}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode.class", "size": 8129, "crc": 956020100}, {"key": "androidx/compose/material3/TabIndicatorScope.class", "name": "androidx/compose/material3/TabIndicatorScope.class", "size": 2240, "crc": -470729123}, {"key": "androidx/compose/material3/TabKt$LeadingIconTab$1.class", "name": "androidx/compose/material3/TabKt$LeadingIconTab$1.class", "size": 13823, "crc": -1393779671}, {"key": "androidx/compose/material3/TabKt$LeadingIconTab$2.class", "name": "androidx/compose/material3/TabKt$LeadingIconTab$2.class", "size": 3188, "crc": -113838158}, {"key": "androidx/compose/material3/TabKt$Tab$1.class", "name": "androidx/compose/material3/TabKt$Tab$1.class", "size": 3354, "crc": -1737631118}, {"key": "androidx/compose/material3/TabKt$Tab$2.class", "name": "androidx/compose/material3/TabKt$Tab$2.class", "size": 3155, "crc": 1134105396}, {"key": "androidx/compose/material3/TabKt$Tab$3.class", "name": "androidx/compose/material3/TabKt$Tab$3.class", "size": 10803, "crc": 2119844694}, {"key": "androidx/compose/material3/TabKt$Tab$4.class", "name": "androidx/compose/material3/TabKt$Tab$4.class", "size": 3034, "crc": 901686217}, {"key": "androidx/compose/material3/TabKt$Tab$styledText$1$1.class", "name": "androidx/compose/material3/TabKt$Tab$styledText$1$1.class", "size": 4528, "crc": 1526623134}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1$1.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1$1.class", "size": 2983, "crc": -2084446961}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1.class", "size": 7304, "crc": -1536868091}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$3.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$3.class", "size": 2171, "crc": 1552610163}, {"key": "androidx/compose/material3/TabKt$TabTransition$1.class", "name": "androidx/compose/material3/TabKt$TabTransition$1.class", "size": 2129, "crc": 640664002}, {"key": "androidx/compose/material3/TabKt$TabTransition$color$2.class", "name": "androidx/compose/material3/TabKt$TabTransition$color$2.class", "size": 3624, "crc": -1021772281}, {"key": "androidx/compose/material3/TabKt.class", "name": "androidx/compose/material3/TabKt.class", "size": 36870, "crc": 739136537}, {"key": "androidx/compose/material3/TabPosition.class", "name": "androidx/compose/material3/TabPosition.class", "size": 3542, "crc": 1284182961}, {"key": "androidx/compose/material3/TabPositionsHolder.class", "name": "androidx/compose/material3/TabPositionsHolder.class", "size": 737, "crc": -854100607}, {"key": "androidx/compose/material3/TabRowDefaults$Indicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$Indicator$1.class", "size": 1999, "crc": 1602101991}, {"key": "androidx/compose/material3/TabRowDefaults$PrimaryIndicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$PrimaryIndicator$1.class", "size": 2227, "crc": -208495768}, {"key": "androidx/compose/material3/TabRowDefaults$SecondaryIndicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$SecondaryIndicator$1.class", "size": 2026, "crc": 1749437472}, {"key": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "size": 2789, "crc": 2065450612}, {"key": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "name": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "size": 2106, "crc": -61278856}, {"key": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2.class", "name": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2.class", "size": 7079, "crc": -82151604}, {"key": "androidx/compose/material3/TabRowDefaults.class", "name": "androidx/compose/material3/TabRowDefaults.class", "size": 13856, "crc": 1887839206}, {"key": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$1.class", "size": 3627, "crc": 1448981195}, {"key": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$2.class", "size": 3325, "crc": -897076188}, {"key": "androidx/compose/material3/TabRowKt$PrimaryTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$PrimaryTabRow$1.class", "size": 3544, "crc": 1685851969}, {"key": "androidx/compose/material3/TabRowKt$PrimaryTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$PrimaryTabRow$2.class", "size": 3015, "crc": -1277723101}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRow$1.class", "size": 3208, "crc": -1311458669}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRow$2.class", "size": 3108, "crc": -1784857129}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$1.class", "size": 3094, "crc": 1943125341}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$2$1$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$2$1$1.class", "size": 6313, "crc": -1009815622}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$2$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$2$1.class", "size": 9883, "crc": 130129559}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "size": 3647, "crc": -844402018}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$scope$1$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1$scope$1$1.class", "size": 4412, "crc": -1394988446}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$1.class", "size": 14864, "crc": 989796611}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImpl$2.class", "size": 3306, "crc": 1943173582}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1$2$3.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1$2$3.class", "size": 3467, "crc": -760742702}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1$2.class", "size": 7857, "crc": 415849016}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1$1$1.class", "size": 8717, "crc": 1122929554}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$1.class", "size": 10205, "crc": 922977178}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowWithSubcomposeImpl$2.class", "size": 3439, "crc": -675424088}, {"key": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$1.class", "size": 3408, "crc": 420081767}, {"key": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$2.class", "size": 3331, "crc": 67482158}, {"key": "androidx/compose/material3/TabRowKt$SecondaryTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$SecondaryTabRow$1.class", "size": 3326, "crc": 948723565}, {"key": "androidx/compose/material3/TabRowKt$SecondaryTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$SecondaryTabRow$2.class", "size": 3021, "crc": -2059488096}, {"key": "androidx/compose/material3/TabRowKt$TabRow$1.class", "name": "androidx/compose/material3/TabRowKt$TabRow$1.class", "size": 3202, "crc": 1655418691}, {"key": "androidx/compose/material3/TabRowKt$TabRow$2.class", "name": "androidx/compose/material3/TabRowKt$TabRow$2.class", "size": 3018, "crc": -529068662}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$1.class", "size": 3014, "crc": -10715368}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1$2.class", "size": 4516, "crc": 1641958926}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1.class", "size": 8916, "crc": 1369739697}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "size": 3577, "crc": 1319726807}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1.class", "size": 4362, "crc": 896798604}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1.class", "size": 11463, "crc": -242643029}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$2.class", "size": 2926, "crc": -1773830348}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1$3.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1$3.class", "size": 3405, "crc": 1537066949}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1.class", "size": 6869, "crc": 401031860}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1.class", "size": 8607, "crc": -1531926711}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1.class", "size": 5777, "crc": -712537414}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$2.class", "size": 3006, "crc": -806790731}, {"key": "androidx/compose/material3/TabRowKt.class", "name": "androidx/compose/material3/TabRowKt.class", "size": 32469, "crc": 1128943741}, {"key": "androidx/compose/material3/TabSlots.class", "name": "androidx/compose/material3/TabSlots.class", "size": 1426, "crc": -58056406}, {"key": "androidx/compose/material3/TextFieldColors$copy$11.class", "name": "androidx/compose/material3/TextFieldColors$copy$11.class", "size": 1578, "crc": -1343743622}, {"key": "androidx/compose/material3/TextFieldColors.class", "name": "androidx/compose/material3/TextFieldColors.class", "size": 49127, "crc": -837538743}, {"key": "androidx/compose/material3/TextFieldDefaults$Container$1.class", "name": "androidx/compose/material3/TextFieldDefaults$Container$1.class", "size": 1276, "crc": -6585744}, {"key": "androidx/compose/material3/TextFieldDefaults$Container$2.class", "name": "androidx/compose/material3/TextFieldDefaults$Container$2.class", "size": 2756, "crc": 841180988}, {"key": "androidx/compose/material3/TextFieldDefaults$ContainerBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$ContainerBox$1.class", "size": 2462, "crc": 1590717660}, {"key": "androidx/compose/material3/TextFieldDefaults$DecorationBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$DecorationBox$1.class", "size": 3933, "crc": -62225388}, {"key": "androidx/compose/material3/TextFieldDefaults$DecorationBox$2.class", "name": "androidx/compose/material3/TextFieldDefaults$DecorationBox$2.class", "size": 5618, "crc": -261769357}, {"key": "androidx/compose/material3/TextFieldDefaults$indicatorLine$2.class", "name": "androidx/compose/material3/TextFieldDefaults$indicatorLine$2.class", "size": 4162, "crc": 1894128664}, {"key": "androidx/compose/material3/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "size": 3857, "crc": 1578376415}, {"key": "androidx/compose/material3/TextFieldDefaults$sam$androidx_compose_ui_graphics_ColorProducer$0.class", "name": "androidx/compose/material3/TextFieldDefaults$sam$androidx_compose_ui_graphics_ColorProducer$0.class", "size": 1771, "crc": 956835995}, {"key": "androidx/compose/material3/TextFieldDefaults.class", "name": "androidx/compose/material3/TextFieldDefaults.class", "size": 40372, "crc": 1637627160}, {"key": "androidx/compose/material3/TextFieldKt$TextField$1$1.class", "name": "androidx/compose/material3/TextFieldKt$TextField$1$1.class", "size": 6419, "crc": 1586048901}, {"key": "androidx/compose/material3/TextFieldKt$TextField$1.class", "name": "androidx/compose/material3/TextFieldKt$TextField$1.class", "size": 10206, "crc": 68939827}, {"key": "androidx/compose/material3/TextFieldKt$TextField$2.class", "name": "androidx/compose/material3/TextFieldKt$TextField$2.class", "size": 6103, "crc": 1398973022}, {"key": "androidx/compose/material3/TextFieldKt$TextField$3$1.class", "name": "androidx/compose/material3/TextFieldKt$TextField$3$1.class", "size": 6619, "crc": -704593918}, {"key": "androidx/compose/material3/TextFieldKt$TextField$3.class", "name": "androidx/compose/material3/TextFieldKt$TextField$3.class", "size": 10438, "crc": -1319424727}, {"key": "androidx/compose/material3/TextFieldKt$TextField$4.class", "name": "androidx/compose/material3/TextFieldKt$TextField$4.class", "size": 6277, "crc": 35271051}, {"key": "androidx/compose/material3/TextFieldKt$TextFieldLayout$2.class", "name": "androidx/compose/material3/TextFieldKt$TextFieldLayout$2.class", "size": 4552, "crc": -1677695838}, {"key": "androidx/compose/material3/TextFieldKt$drawIndicatorLine$1.class", "name": "androidx/compose/material3/TextFieldKt$drawIndicatorLine$1.class", "size": 2795, "crc": -1486666919}, {"key": "androidx/compose/material3/TextFieldKt.class", "name": "androidx/compose/material3/TextFieldKt.class", "size": 66522, "crc": 403073964}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1890, "crc": 957125814}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1887, "crc": 1227610187}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$measure$1.class", "size": 4658, "crc": -572482253}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1890, "crc": 1365888449}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1887, "crc": -713884617}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy.class", "size": 31710, "crc": 271435214}, {"key": "androidx/compose/material3/TextKt$LocalTextStyle$1.class", "name": "androidx/compose/material3/TextKt$LocalTextStyle$1.class", "size": 1290, "crc": -365865967}, {"key": "androidx/compose/material3/TextKt$ProvideTextStyle$1.class", "name": "androidx/compose/material3/TextKt$ProvideTextStyle$1.class", "size": 2116, "crc": 2115156873}, {"key": "androidx/compose/material3/TextKt$Text$1.class", "name": "androidx/compose/material3/TextKt$Text$1.class", "size": 4043, "crc": 748765154}, {"key": "androidx/compose/material3/TextKt$Text$2.class", "name": "androidx/compose/material3/TextKt$Text$2.class", "size": 1712, "crc": 1478437832}, {"key": "androidx/compose/material3/TextKt$Text$3.class", "name": "androidx/compose/material3/TextKt$Text$3.class", "size": 3990, "crc": 1115649894}, {"key": "androidx/compose/material3/TextKt$Text$4.class", "name": "androidx/compose/material3/TextKt$Text$4.class", "size": 1752, "crc": 1427071083}, {"key": "androidx/compose/material3/TextKt$Text$5.class", "name": "androidx/compose/material3/TextKt$Text$5.class", "size": 4427, "crc": -575660362}, {"key": "androidx/compose/material3/TextKt$Text$6.class", "name": "androidx/compose/material3/TextKt$Text$6.class", "size": 1751, "crc": -1922696156}, {"key": "androidx/compose/material3/TextKt$Text$7.class", "name": "androidx/compose/material3/TextKt$Text$7.class", "size": 4374, "crc": -498261071}, {"key": "androidx/compose/material3/TextKt.class", "name": "androidx/compose/material3/TextKt.class", "size": 31430, "crc": -479250853}, {"key": "androidx/compose/material3/ThumbElement.class", "name": "androidx/compose/material3/ThumbElement.class", "size": 5030, "crc": -1458565749}, {"key": "androidx/compose/material3/ThumbNode$measure$1.class", "name": "androidx/compose/material3/ThumbNode$measure$1.class", "size": 4572, "crc": -1007809203}, {"key": "androidx/compose/material3/ThumbNode$measure$2.class", "name": "androidx/compose/material3/ThumbNode$measure$2.class", "size": 4576, "crc": 432585697}, {"key": "androidx/compose/material3/ThumbNode$measure$3.class", "name": "androidx/compose/material3/ThumbNode$measure$3.class", "size": 2447, "crc": 1687911422}, {"key": "androidx/compose/material3/ThumbNode$onAttach$1$1.class", "name": "androidx/compose/material3/ThumbNode$onAttach$1$1.class", "size": 3031, "crc": -1126970008}, {"key": "androidx/compose/material3/ThumbNode$onAttach$1.class", "name": "androidx/compose/material3/ThumbNode$onAttach$1.class", "size": 4048, "crc": 1103809573}, {"key": "androidx/compose/material3/ThumbNode.class", "name": "androidx/compose/material3/ThumbNode.class", "size": 8480, "crc": 667589317}, {"key": "androidx/compose/material3/TimeFormat_androidKt.class", "name": "androidx/compose/material3/TimeFormat_androidKt.class", "size": 2805, "crc": 860785747}, {"key": "androidx/compose/material3/TimePickerColors.class", "name": "androidx/compose/material3/TimePickerColors.class", "size": 14381, "crc": 343009641}, {"key": "androidx/compose/material3/TimePickerDefaults.class", "name": "androidx/compose/material3/TimePickerDefaults.class", "size": 7731, "crc": 1497781489}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1$1.class", "size": 4365, "crc": 796921421}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1.class", "size": 7183, "crc": 574146881}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$2.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$2.class", "size": 2266, "crc": 2111236457}, {"key": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$1.class", "size": 11003, "crc": 541365283}, {"key": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$2.class", "size": 1979, "crc": -351613933}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$1.class", "size": 1694, "crc": -983704572}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$1$1$1.class", "size": 1852, "crc": 1807985046}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$2$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$2$1$1$1.class", "size": 1931, "crc": 1939059894}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1$2.class", "size": 5436, "crc": 620757828}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$2$1.class", "size": 7760, "crc": -2003990336}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1$2.class", "size": 3953, "crc": 1783160128}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1.class", "size": 4415, "crc": 191549905}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2.class", "size": 1999, "crc": 1254679330}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$1$1.class", "size": 3013, "crc": 621060648}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1$1.class", "size": 4620, "crc": -1370922}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1.class", "size": 2845, "crc": -1872963451}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1.class", "size": 3305, "crc": -340436371}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$3$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$3$1$1.class", "size": 1863, "crc": 1534316776}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$4.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$4.class", "size": 2029, "crc": 1002034288}, {"key": "androidx/compose/material3/TimePickerKt$DisplaySeparator$1.class", "name": "androidx/compose/material3/TimePickerKt$DisplaySeparator$1.class", "size": 1626, "crc": 1667403907}, {"key": "androidx/compose/material3/TimePickerKt$DisplaySeparator$3.class", "name": "androidx/compose/material3/TimePickerKt$DisplaySeparator$3.class", "size": 1743, "crc": 230635761}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalClockDisplay$2.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalClockDisplay$2.class", "size": 1991, "crc": 1585198710}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$1.class", "size": 2134, "crc": -1123392602}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1$1.class", "size": 2441, "crc": -1395604762}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1.class", "size": 6819, "crc": 1805682071}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalTimePicker$2.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalTimePicker$2.class", "size": 2227, "crc": -1816420786}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$1$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$1$1.class", "size": 2127, "crc": -1374060576}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$1$1.class", "size": 1551, "crc": 53263896}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$2$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$2$1.class", "size": 1551, "crc": 50506788}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$3.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$3.class", "size": 2579, "crc": 604936227}, {"key": "androidx/compose/material3/TimePickerKt$TimeInput$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInput$1.class", "size": 2110, "crc": 259370647}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$1$1.class", "size": 3236, "crc": 574480289}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$2$1$1.class", "size": 2045, "crc": 77632552}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$2$1.class", "size": 3013, "crc": 1969587929}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$3$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$3$1.class", "size": 1996, "crc": -1081999443}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$4$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$4$1.class", "size": 3058, "crc": 427125968}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$5$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$5$1$1.class", "size": 2048, "crc": -472445797}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$5$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$5$1.class", "size": 2854, "crc": 1063543293}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$6$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$6$1.class", "size": 1996, "crc": -2015891832}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1.class", "size": 18473, "crc": -1700818771}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$2.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$2.class", "size": 2098, "crc": -615972297}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$hourValue$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$hourValue$2$1.class", "size": 2320, "crc": 1303287219}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$minuteValue$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$minuteValue$2$1.class", "size": 2322, "crc": -39672463}, {"key": "androidx/compose/material3/TimePickerKt$TimePicker$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePicker$1.class", "size": 2175, "crc": -786765647}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$1$1.class", "size": 2112, "crc": 2045304133}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2$1.class", "size": 3621, "crc": 1793292714}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2.class", "size": 6549, "crc": 331007094}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$2.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$2.class", "size": 1917, "crc": -564220262}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$2$1.class", "size": 4173, "crc": 1564155675}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$3.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$3.class", "size": 3491, "crc": -2044837762}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$1$1.class", "size": 2232, "crc": 1643879910}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1.class", "size": 1666, "crc": -179281732}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$3$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$3$1$1$1.class", "size": 1796, "crc": 954175475}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$3.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$3.class", "size": 11349, "crc": -1472426921}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$4.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$4.class", "size": 2212, "crc": 262156284}, {"key": "androidx/compose/material3/TimePickerKt$ToggleItem$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ToggleItem$1$1.class", "size": 1843, "crc": 522794056}, {"key": "androidx/compose/material3/TimePickerKt$ToggleItem$2.class", "name": "androidx/compose/material3/TimePickerKt$ToggleItem$2.class", "size": 2778, "crc": -2056589584}, {"key": "androidx/compose/material3/TimePickerKt$VerticalClockDisplay$2.class", "name": "androidx/compose/material3/TimePickerKt$VerticalClockDisplay$2.class", "size": 1983, "crc": -213610352}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$1.class", "size": 2126, "crc": 659340384}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1$1.class", "size": 2436, "crc": 1390079454}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1.class", "size": 6795, "crc": 795804432}, {"key": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$1.class", "size": 1874, "crc": 778742205}, {"key": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$3.class", "name": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$3.class", "size": 2221, "crc": 1967047343}, {"key": "androidx/compose/material3/TimePickerKt$drawSelector$1.class", "name": "androidx/compose/material3/TimePickerKt$drawSelector$1.class", "size": 4635, "crc": 1695361625}, {"key": "androidx/compose/material3/TimePickerKt$onTap$1.class", "name": "androidx/compose/material3/TimePickerKt$onTap$1.class", "size": 1650, "crc": -1023776066}, {"key": "androidx/compose/material3/TimePickerKt$rememberTimePickerState$state$1$1.class", "name": "androidx/compose/material3/TimePickerKt$rememberTimePickerState$state$1$1.class", "size": 1575, "crc": -2080851733}, {"key": "androidx/compose/material3/TimePickerKt$visible$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TimePickerKt$visible$$inlined$debugInspectorInfo$1.class", "size": 2829, "crc": 104291997}, {"key": "androidx/compose/material3/TimePickerKt.class", "name": "androidx/compose/material3/TimePickerKt.class", "size": 124704, "crc": -778383785}, {"key": "androidx/compose/material3/TimePickerLayoutType$Companion.class", "name": "androidx/compose/material3/TimePickerLayoutType$Companion.class", "size": 1279, "crc": -845545504}, {"key": "androidx/compose/material3/TimePickerLayoutType.class", "name": "androidx/compose/material3/TimePickerLayoutType.class", "size": 2735, "crc": -2120864083}, {"key": "androidx/compose/material3/TimePickerSelectionMode$Companion.class", "name": "androidx/compose/material3/TimePickerSelectionMode$Companion.class", "size": 1267, "crc": 1010583251}, {"key": "androidx/compose/material3/TimePickerSelectionMode.class", "name": "androidx/compose/material3/TimePickerSelectionMode.class", "size": 2754, "crc": 337786893}, {"key": "androidx/compose/material3/TimePickerState.class", "name": "androidx/compose/material3/TimePickerState.class", "size": 1316, "crc": -309020745}, {"key": "androidx/compose/material3/TimePickerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/TimePickerStateImpl$Companion$Saver$1.class", "size": 2449, "crc": -438814775}, {"key": "androidx/compose/material3/TimePickerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/TimePickerStateImpl$Companion$Saver$2.class", "size": 2252, "crc": 895067400}, {"key": "androidx/compose/material3/TimePickerStateImpl$Companion.class", "name": "androidx/compose/material3/TimePickerStateImpl$Companion.class", "size": 1871, "crc": -1147057102}, {"key": "androidx/compose/material3/TimePickerStateImpl.class", "name": "androidx/compose/material3/TimePickerStateImpl.class", "size": 6136, "crc": -94940740}, {"key": "androidx/compose/material3/TimePicker_androidKt.class", "name": "androidx/compose/material3/TimePicker_androidKt.class", "size": 3575, "crc": **********}, {"key": "androidx/compose/material3/TonalPalette.class", "name": "androidx/compose/material3/TonalPalette.class", "size": 18292, "crc": -529710909}, {"key": "androidx/compose/material3/TonalPaletteKt.class", "name": "androidx/compose/material3/TonalPaletteKt.class", "size": 5511, "crc": 128997963}, {"key": "androidx/compose/material3/TooltipDefaults$rememberPlainTooltipPositionProvider$1$1.class", "name": "androidx/compose/material3/TooltipDefaults$rememberPlainTooltipPositionProvider$1$1.class", "size": 2137, "crc": **********}, {"key": "androidx/compose/material3/TooltipDefaults$rememberRichTooltipPositionProvider$1$1.class", "name": "androidx/compose/material3/TooltipDefaults$rememberRichTooltipPositionProvider$1$1.class", "size": 2224, "crc": -293798926}, {"key": "androidx/compose/material3/TooltipDefaults.class", "name": "androidx/compose/material3/TooltipDefaults.class", "size": 12800, "crc": 995404653}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$1.class", "size": 9817, "crc": -**********}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$2.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$2.class", "size": 3181, "crc": -250857976}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1.class", "size": 1866, "crc": -**********}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1$1$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1$1$1.class", "size": 1920, "crc": **********}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1.class", "size": 10541, "crc": 122839721}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$$inlined$debugInspectorInfo$1.class", "size": 2914, "crc": -1551847526}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2$alpha$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2$alpha$2.class", "size": 3492, "crc": 1160712012}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2$scale$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2$scale$2.class", "size": 3525, "crc": -1117291874}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2.class", "size": 9106, "crc": -341373300}, {"key": "androidx/compose/material3/TooltipKt.class", "name": "androidx/compose/material3/TooltipKt.class", "size": 16909, "crc": -534302338}, {"key": "androidx/compose/material3/TooltipScope.class", "name": "androidx/compose/material3/TooltipScope.class", "size": 1349, "crc": 1622632125}, {"key": "androidx/compose/material3/TooltipScopeImpl$drawCaret$1.class", "name": "androidx/compose/material3/TooltipScopeImpl$drawCaret$1.class", "size": 2525, "crc": 2137130221}, {"key": "androidx/compose/material3/TooltipScopeImpl.class", "name": "androidx/compose/material3/TooltipScopeImpl.class", "size": 2770, "crc": -1686543038}, {"key": "androidx/compose/material3/TooltipState.class", "name": "androidx/compose/material3/TooltipState.class", "size": 2047, "crc": 1606123690}, {"key": "androidx/compose/material3/TooltipStateImpl$show$2$1.class", "name": "androidx/compose/material3/TooltipStateImpl$show$2$1.class", "size": 3524, "crc": 1240714408}, {"key": "androidx/compose/material3/TooltipStateImpl$show$2.class", "name": "androidx/compose/material3/TooltipStateImpl$show$2.class", "size": 4343, "crc": 1305794486}, {"key": "androidx/compose/material3/TooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/material3/TooltipStateImpl$show$cancellableShow$1.class", "size": 5375, "crc": 2068452219}, {"key": "androidx/compose/material3/TooltipStateImpl.class", "name": "androidx/compose/material3/TooltipStateImpl.class", "size": 4616, "crc": -131346447}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$1.class", "size": 11040, "crc": 1711225328}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$2.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$2.class", "size": 2917, "crc": -1678298924}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$drawCaretModifier$1$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$drawCaretModifier$1$1.class", "size": 2838, "crc": -877506607}, {"key": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$1.class", "size": 20430, "crc": 657473279}, {"key": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$2.class", "name": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$2.class", "size": 3474, "crc": -1412452277}, {"key": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$drawCaretModifier$1$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$RichTooltip$drawCaretModifier$1$1.class", "size": 2941, "crc": 829528351}, {"key": "androidx/compose/material3/Tooltip_androidKt$drawCaretWithPath$4.class", "name": "androidx/compose/material3/Tooltip_androidKt$drawCaretWithPath$4.class", "size": 2467, "crc": -473339672}, {"key": "androidx/compose/material3/Tooltip_androidKt.class", "name": "androidx/compose/material3/Tooltip_androidKt.class", "size": 23050, "crc": -1053662196}, {"key": "androidx/compose/material3/TopAppBarColors.class", "name": "androidx/compose/material3/TopAppBarColors.class", "size": 6421, "crc": 1587570304}, {"key": "androidx/compose/material3/TopAppBarDefaults$enterAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$enterAlwaysScrollBehavior$1.class", "size": 1477, "crc": 899742651}, {"key": "androidx/compose/material3/TopAppBarDefaults$exitUntilCollapsedScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$exitUntilCollapsedScrollBehavior$1.class", "size": 1498, "crc": 62186954}, {"key": "androidx/compose/material3/TopAppBarDefaults$pinnedScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$pinnedScrollBehavior$1.class", "size": 1363, "crc": -449510895}, {"key": "androidx/compose/material3/TopAppBarDefaults.class", "name": "androidx/compose/material3/TopAppBarDefaults.class", "size": 18671, "crc": 1913935637}, {"key": "androidx/compose/material3/TopAppBarScrollBehavior.class", "name": "androidx/compose/material3/TopAppBarScrollBehavior.class", "size": 1740, "crc": 695649808}, {"key": "androidx/compose/material3/TopAppBarState$Companion$Saver$1.class", "name": "androidx/compose/material3/TopAppBarState$Companion$Saver$1.class", "size": 2166, "crc": -343179721}, {"key": "androidx/compose/material3/TopAppBarState$Companion$Saver$2.class", "name": "androidx/compose/material3/TopAppBarState$Companion$Saver$2.class", "size": 1803, "crc": -646898910}, {"key": "androidx/compose/material3/TopAppBarState$Companion.class", "name": "androidx/compose/material3/TopAppBarState$Companion.class", "size": 1324, "crc": -1036442902}, {"key": "androidx/compose/material3/TopAppBarState.class", "name": "androidx/compose/material3/TopAppBarState.class", "size": 5203, "crc": 1671574558}, {"key": "androidx/compose/material3/TopIconOrIconOnlyMeasurePolicy.class", "name": "androidx/compose/material3/TopIconOrIconOnlyMeasurePolicy.class", "size": 13141, "crc": -638291158}, {"key": "androidx/compose/material3/Typography.class", "name": "androidx/compose/material3/Typography.class", "size": 10230, "crc": 711469525}, {"key": "androidx/compose/material3/TypographyKt$LocalTypography$1.class", "name": "androidx/compose/material3/TypographyKt$LocalTypography$1.class", "size": 1904, "crc": 514483092}, {"key": "androidx/compose/material3/TypographyKt$WhenMappings.class", "name": "androidx/compose/material3/TypographyKt$WhenMappings.class", "size": 1531, "crc": 20337912}, {"key": "androidx/compose/material3/TypographyKt.class", "name": "androidx/compose/material3/TypographyKt.class", "size": 4238, "crc": 1688249832}, {"key": "androidx/compose/material3/VisibleModifier$measure$1.class", "name": "androidx/compose/material3/VisibleModifier$measure$1.class", "size": 1709, "crc": 1801577612}, {"key": "androidx/compose/material3/VisibleModifier$measure$2.class", "name": "androidx/compose/material3/VisibleModifier$measure$2.class", "size": 1916, "crc": 325685429}, {"key": "androidx/compose/material3/VisibleModifier.class", "name": "androidx/compose/material3/VisibleModifier.class", "size": 3424, "crc": -220991491}, {"key": "androidx/compose/material3/carousel/Arrangement$Companion.class", "name": "androidx/compose/material3/carousel/Arrangement$Companion.class", "size": 4014, "crc": 362681539}, {"key": "androidx/compose/material3/carousel/Arrangement.class", "name": "androidx/compose/material3/carousel/Arrangement.class", "size": 2916, "crc": -485476345}, {"key": "androidx/compose/material3/carousel/CarouselAlignment$Companion.class", "name": "androidx/compose/material3/carousel/CarouselAlignment$Companion.class", "size": 1429, "crc": -784036906}, {"key": "androidx/compose/material3/carousel/CarouselAlignment.class", "name": "androidx/compose/material3/carousel/CarouselAlignment.class", "size": 2812, "crc": 183971018}, {"key": "androidx/compose/material3/carousel/CarouselDefaults$multiBrowseFlingBehavior$pagerSnapDistance$1.class", "name": "androidx/compose/material3/carousel/CarouselDefaults$multiBrowseFlingBehavior$pagerSnapDistance$1.class", "size": 1456, "crc": **********}, {"key": "androidx/compose/material3/carousel/CarouselDefaults$noSnapFlingBehavior$decayLayoutInfoProvider$1$1.class", "name": "androidx/compose/material3/carousel/CarouselDefaults$noSnapFlingBehavior$decayLayoutInfoProvider$1$1.class", "size": 1178, "crc": -**********}, {"key": "androidx/compose/material3/carousel/CarouselDefaults.class", "name": "androidx/compose/material3/carousel/CarouselDefaults.class", "size": 9317, "crc": **********}, {"key": "androidx/compose/material3/carousel/CarouselItemInfo.class", "name": "androidx/compose/material3/carousel/CarouselItemInfo.class", "size": 992, "crc": -**********}, {"key": "androidx/compose/material3/carousel/CarouselItemInfoImpl.class", "name": "androidx/compose/material3/carousel/CarouselItemInfoImpl.class", "size": 5737, "crc": -749399824}, {"key": "androidx/compose/material3/carousel/CarouselItemScope.class", "name": "androidx/compose/material3/carousel/CarouselItemScope.class", "size": 1957, "crc": **********}, {"key": "androidx/compose/material3/carousel/CarouselItemScopeImpl$rememberMaskShape$1$1.class", "name": "androidx/compose/material3/carousel/CarouselItemScopeImpl$rememberMaskShape$1$1.class", "size": 3598, "crc": -370590033}, {"key": "androidx/compose/material3/carousel/CarouselItemScopeImpl.class", "name": "androidx/compose/material3/carousel/CarouselItemScopeImpl.class", "size": 7411, "crc": 1326374235}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$1$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$1$1$1.class", "size": 1537, "crc": -1537076073}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$1$clipShape$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$1$clipShape$1$1.class", "size": 2081, "crc": 2087766114}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$1.class", "size": 13525, "crc": 1130672409}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$2$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$2$1$1.class", "size": 1537, "crc": -1463923065}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$2$clipShape$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$2$clipShape$1$1.class", "size": 2081, "crc": -125618038}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$2.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$2.class", "size": 13523, "crc": 254130578}, {"key": "androidx/compose/material3/carousel/CarouselKt$Carousel$3.class", "name": "androidx/compose/material3/carousel/CarouselKt$Carousel$3.class", "size": 3923, "crc": 195011395}, {"key": "androidx/compose/material3/carousel/CarouselKt$HorizontalMultiBrowseCarousel$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$HorizontalMultiBrowseCarousel$1$1.class", "size": 3072, "crc": -11465943}, {"key": "androidx/compose/material3/carousel/CarouselKt$HorizontalMultiBrowseCarousel$2.class", "name": "androidx/compose/material3/carousel/CarouselKt$HorizontalMultiBrowseCarousel$2.class", "size": 3446, "crc": -1028628816}, {"key": "androidx/compose/material3/carousel/CarouselKt$HorizontalUncontainedCarousel$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$HorizontalUncontainedCarousel$1$1.class", "size": 2406, "crc": -1421942426}, {"key": "androidx/compose/material3/carousel/CarouselKt$HorizontalUncontainedCarousel$2.class", "name": "androidx/compose/material3/carousel/CarouselKt$HorizontalUncontainedCarousel$2.class", "size": 3313, "crc": -1635930184}, {"key": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$1.class", "size": 1755, "crc": -663676311}, {"key": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$2$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$2$1.class", "size": 7921, "crc": -293084723}, {"key": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$2.class", "name": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1$2.class", "size": 3079, "crc": -231972190}, {"key": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1.class", "name": "androidx/compose/material3/carousel/CarouselKt$carouselItem$1.class", "size": 5779, "crc": 1187304152}, {"key": "androidx/compose/material3/carousel/CarouselKt.class", "name": "androidx/compose/material3/carousel/CarouselKt.class", "size": 27594, "crc": -364251332}, {"key": "androidx/compose/material3/carousel/CarouselPageSize.class", "name": "androidx/compose/material3/carousel/CarouselPageSize.class", "size": 5087, "crc": 76096475}, {"key": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$1.class", "name": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$1.class", "size": 2495, "crc": 1544490019}, {"key": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$2$1.class", "name": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$2$1.class", "size": 1642, "crc": -1045666942}, {"key": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$2.class", "name": "androidx/compose/material3/carousel/CarouselState$Companion$Saver$2.class", "size": 2300, "crc": -1603763480}, {"key": "androidx/compose/material3/carousel/CarouselState$Companion.class", "name": "androidx/compose/material3/carousel/CarouselState$Companion.class", "size": 1439, "crc": 994621681}, {"key": "androidx/compose/material3/carousel/CarouselState.class", "name": "androidx/compose/material3/carousel/CarouselState.class", "size": 6122, "crc": -1107225406}, {"key": "androidx/compose/material3/carousel/CarouselStateKt$rememberCarouselState$1$1.class", "name": "androidx/compose/material3/carousel/CarouselStateKt$rememberCarouselState$1$1.class", "size": 1826, "crc": -588187051}, {"key": "androidx/compose/material3/carousel/CarouselStateKt.class", "name": "androidx/compose/material3/carousel/CarouselStateKt.class", "size": 5123, "crc": 1920609103}, {"key": "androidx/compose/material3/carousel/Keyline.class", "name": "androidx/compose/material3/carousel/Keyline.class", "size": 4282, "crc": -1457110530}, {"key": "androidx/compose/material3/carousel/KeylineList$Companion.class", "name": "androidx/compose/material3/carousel/KeylineList$Companion.class", "size": 1188, "crc": -394922767}, {"key": "androidx/compose/material3/carousel/KeylineList.class", "name": "androidx/compose/material3/carousel/KeylineList.class", "size": 16202, "crc": -1549812997}, {"key": "androidx/compose/material3/carousel/KeylineListKt.class", "name": "androidx/compose/material3/carousel/KeylineListKt.class", "size": 6125, "crc": -1504166956}, {"key": "androidx/compose/material3/carousel/KeylineListScope.class", "name": "androidx/compose/material3/carousel/KeylineListScope.class", "size": 927, "crc": -392900114}, {"key": "androidx/compose/material3/carousel/KeylineListScopeImpl$TmpKeyline.class", "name": "androidx/compose/material3/carousel/KeylineListScopeImpl$TmpKeyline.class", "size": 2706, "crc": 1401301647}, {"key": "androidx/compose/material3/carousel/KeylineListScopeImpl.class", "name": "androidx/compose/material3/carousel/KeylineListScopeImpl.class", "size": 8356, "crc": 862515425}, {"key": "androidx/compose/material3/carousel/KeylineSnapPositionKt$KeylineSnapPosition$1.class", "name": "androidx/compose/material3/carousel/KeylineSnapPositionKt$KeylineSnapPosition$1.class", "size": 1727, "crc": -449529587}, {"key": "androidx/compose/material3/carousel/KeylineSnapPositionKt.class", "name": "androidx/compose/material3/carousel/KeylineSnapPositionKt.class", "size": 2871, "crc": 607425006}, {"key": "androidx/compose/material3/carousel/KeylinesKt$createLeftAlignedKeylineList$1.class", "name": "androidx/compose/material3/carousel/KeylinesKt$createLeftAlignedKeylineList$1.class", "size": 3250, "crc": 1104913259}, {"key": "androidx/compose/material3/carousel/KeylinesKt.class", "name": "androidx/compose/material3/carousel/KeylinesKt.class", "size": 6948, "crc": -738319816}, {"key": "androidx/compose/material3/carousel/ShiftPointRange.class", "name": "androidx/compose/material3/carousel/ShiftPointRange.class", "size": 2946, "crc": -287739449}, {"key": "androidx/compose/material3/carousel/Strategy$Companion.class", "name": "androidx/compose/material3/carousel/Strategy$Companion.class", "size": 1170, "crc": -1777483137}, {"key": "androidx/compose/material3/carousel/Strategy.class", "name": "androidx/compose/material3/carousel/Strategy.class", "size": 8402, "crc": 1684477834}, {"key": "androidx/compose/material3/carousel/StrategyKt$createShiftedKeylineListForContentPadding$newKeylines$1.class", "name": "androidx/compose/material3/carousel/StrategyKt$createShiftedKeylineListForContentPadding$newKeylines$1.class", "size": 3543, "crc": 97785826}, {"key": "androidx/compose/material3/carousel/StrategyKt$moveKeylineAndCreateShiftedKeylineList$1.class", "name": "androidx/compose/material3/carousel/StrategyKt$moveKeylineAndCreateShiftedKeylineList$1.class", "size": 3578, "crc": 157895545}, {"key": "androidx/compose/material3/carousel/StrategyKt.class", "name": "androidx/compose/material3/carousel/StrategyKt.class", "size": 13950, "crc": **********}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$1.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$1.class", "size": 1686, "crc": 275143863}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$2.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$2.class", "size": 1316, "crc": 969470452}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$3$1$invoke$$inlined$onDispose$1.class", "size": 2912, "crc": -**********}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$3$1.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$3$1.class", "size": 5174, "crc": -95202997}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$4.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$ObserveState$4.class", "size": 2676, "crc": -**********}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$rememberAccessibilityServiceState$1$1.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$rememberAccessibilityServiceState$1$1.class", "size": 2212, "crc": -**********}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$rememberAccessibilityServiceState$2$1.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt$rememberAccessibilityServiceState$2$1.class", "size": 1764, "crc": 82574737}, {"key": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt.class", "name": "androidx/compose/material3/internal/AccessibilityServiceStateProvider_androidKt.class", "size": 11082, "crc": -912843505}, {"key": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$1$1.class", "name": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$1$1.class", "size": 2116, "crc": 431394747}, {"key": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$1.class", "name": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$1.class", "size": 3127, "crc": **********}, {"key": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$2.class", "name": "androidx/compose/material3/internal/AccessibilityUtilKt$IncreaseHorizontalSemanticsBounds$2.class", "size": 1608, "crc": 530310560}, {"key": "androidx/compose/material3/internal/AccessibilityUtilKt.class", "name": "androidx/compose/material3/internal/AccessibilityUtilKt.class", "size": 3265, "crc": -**********}, {"key": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition$Horizontal.class", "name": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition$Horizontal.class", "size": 4729, "crc": -840100735}, {"key": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition$Vertical.class", "name": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition$Vertical.class", "size": 4369, "crc": 358074013}, {"key": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition.class", "name": "androidx/compose/material3/internal/AnchorAlignmentOffsetPosition.class", "size": 1055, "crc": 2101767798}, {"key": "androidx/compose/material3/internal/AnchoredDragFinishedSignal.class", "name": "androidx/compose/material3/internal/AnchoredDragFinishedSignal.class", "size": 1848, "crc": -2109264116}, {"key": "androidx/compose/material3/internal/AnchoredDragScope.class", "name": "androidx/compose/material3/internal/AnchoredDragScope.class", "size": 950, "crc": -366414705}, {"key": "androidx/compose/material3/internal/AnchoredDraggableDefaults.class", "name": "androidx/compose/material3/internal/AnchoredDraggableDefaults.class", "size": 1422, "crc": -1966251646}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3803, "crc": 870725538}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 4203, "crc": 920525488}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$animateTo$2$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$animateTo$2$1.class", "size": 1918, "crc": -1005892344}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$animateTo$2.class", "size": 5344, "crc": -734290569}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$1.class", "size": 1716, "crc": 1505394070}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4303, "crc": 1186919923}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2180, "crc": -877284693}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2$1.class", "size": 4600, "crc": -1590075367}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$restartable$2.class", "size": 4712, "crc": 1757596231}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt$snapTo$2.class", "size": 4038, "crc": -1522510532}, {"key": "androidx/compose/material3/internal/AnchoredDraggableKt.class", "name": "androidx/compose/material3/internal/AnchoredDraggableKt.class", "size": 10428, "crc": 1339310031}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$1.class", "size": 1633, "crc": -162444688}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$2.class", "size": 1687, "crc": 1503106355}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$Companion$Saver$1.class", "size": 2360, "crc": 763925793}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$Companion$Saver$2.class", "size": 3145, "crc": -850022375}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$Companion.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$Companion.class", "size": 3055, "crc": -1953133993}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$1.class", "size": 2010, "crc": -1721402234}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1861, "crc": 1818198895}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4704, "crc": -1743291023}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$2.class", "size": 4641, "crc": 1310112971}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$3.class", "size": 2029, "crc": -733742553}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 2079, "crc": -154060166}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4924, "crc": -489553407}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDrag$4.class", "size": 4888, "crc": 859560000}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1707, "crc": -1295217785}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$closestValue$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$closestValue$2.class", "size": 2122, "crc": -1121454943}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5670, "crc": 84357908}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 2631, "crc": -1828663660}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$draggableState$1.class", "size": 3952, "crc": -1015275569}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$progress$2.class", "size": 2334, "crc": 557323446}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$targetValue$2.class", "size": 2104, "crc": -290518788}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState$trySnapTo$1.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState$trySnapTo$1.class", "size": 2557, "crc": -871071649}, {"key": "androidx/compose/material3/internal/AnchoredDraggableState.class", "name": "androidx/compose/material3/internal/AnchoredDraggableState.class", "size": 25227, "crc": 1070041913}, {"key": "androidx/compose/material3/internal/BasicTooltipDefaults.class", "name": "androidx/compose/material3/internal/BasicTooltipDefaults.class", "size": 1285, "crc": -2076690352}, {"key": "androidx/compose/material3/internal/BasicTooltipKt.class", "name": "androidx/compose/material3/internal/BasicTooltipKt.class", "size": 4494, "crc": 611226476}, {"key": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$2$1.class", "size": 3599, "crc": -1570819380}, {"key": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$2.class", "size": 4148, "crc": 607818101}, {"key": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/material3/internal/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5356, "crc": -2128962090}, {"key": "androidx/compose/material3/internal/BasicTooltipStateImpl.class", "name": "androidx/compose/material3/internal/BasicTooltipStateImpl.class", "size": 6240, "crc": 1660167241}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "size": 2231, "crc": 2037728009}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "size": 3117, "crc": 403930529}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$3.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$BasicTooltipBox$3.class", "size": 3053, "crc": -541242548}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "size": 3457, "crc": 188517119}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$1$1.class", "size": 2156, "crc": 448224273}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "size": 2199, "crc": 48741063}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$2.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$2.class", "size": 10632, "crc": 1181043023}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$3.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$TooltipPopup$3.class", "size": 2751, "crc": 105702947}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$WrappedAnchor$2.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$WrappedAnchor$2.class", "size": 2536, "crc": -1850797450}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "size": 3819, "crc": -1174232694}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1$1.class", "size": 2049, "crc": 207119}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$anchorSemantics$1.class", "size": 2466, "crc": -1932280360}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "size": 4289, "crc": 133199574}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$2$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$2$1.class", "size": 3795, "crc": -1188893358}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "size": 5104, "crc": 1544931883}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1$1.class", "size": 7717, "crc": 2118117695}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1$1.class", "size": 4208, "crc": 1643512701}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$1.class", "size": 4110, "crc": -1727639109}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "size": 3936, "crc": -1236419296}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1$1.class", "size": 5987, "crc": -81367541}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2$1.class", "size": 4161, "crc": -1457507971}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt$handleGestures$2.class", "size": 4110, "crc": 1025753068}, {"key": "androidx/compose/material3/internal/BasicTooltip_androidKt.class", "name": "androidx/compose/material3/internal/BasicTooltip_androidKt.class", "size": 24474, "crc": 1195295309}, {"key": "androidx/compose/material3/internal/CalendarDate.class", "name": "androidx/compose/material3/internal/CalendarDate.class", "size": 4435, "crc": -224766600}, {"key": "androidx/compose/material3/internal/CalendarModel.class", "name": "androidx/compose/material3/internal/CalendarModel.class", "size": 5928, "crc": 565475882}, {"key": "androidx/compose/material3/internal/CalendarModelImpl$Companion.class", "name": "androidx/compose/material3/internal/CalendarModelImpl$Companion.class", "size": 4878, "crc": -2120729402}, {"key": "androidx/compose/material3/internal/CalendarModelImpl.class", "name": "androidx/compose/material3/internal/CalendarModelImpl.class", "size": 11132, "crc": -1697047060}, {"key": "androidx/compose/material3/internal/CalendarModelKt.class", "name": "androidx/compose/material3/internal/CalendarModelKt.class", "size": 2797, "crc": 111276388}, {"key": "androidx/compose/material3/internal/CalendarModel_androidKt.class", "name": "androidx/compose/material3/internal/CalendarModel_androidKt.class", "size": 3977, "crc": 388051370}, {"key": "androidx/compose/material3/internal/CalendarMonth.class", "name": "androidx/compose/material3/internal/CalendarMonth.class", "size": 4796, "crc": -54871155}, {"key": "androidx/compose/material3/internal/DateInputFormat.class", "name": "androidx/compose/material3/internal/DateInputFormat.class", "size": 3241, "crc": 211569025}, {"key": "androidx/compose/material3/internal/DefaultPlatformTextStyle_androidKt.class", "name": "androidx/compose/material3/internal/DefaultPlatformTextStyle_androidKt.class", "size": 1119, "crc": 2009597539}, {"key": "androidx/compose/material3/internal/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/material3/internal/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2137, "crc": -491000767}, {"key": "androidx/compose/material3/internal/DragGestureDetectorCopyKt.class", "name": "androidx/compose/material3/internal/DragGestureDetectorCopyKt.class", "size": 15815, "crc": 749668523}, {"key": "androidx/compose/material3/internal/DraggableAnchors.class", "name": "androidx/compose/material3/internal/DraggableAnchors.class", "size": 1187, "crc": 641126124}, {"key": "androidx/compose/material3/internal/DraggableAnchorsConfig.class", "name": "androidx/compose/material3/internal/DraggableAnchorsConfig.class", "size": 1689, "crc": -1398080029}, {"key": "androidx/compose/material3/internal/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/internal/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "size": 3459, "crc": -355145688}, {"key": "androidx/compose/material3/internal/DraggableAnchorsElement.class", "name": "androidx/compose/material3/internal/DraggableAnchorsElement.class", "size": 6825, "crc": -**********}, {"key": "androidx/compose/material3/internal/DraggableAnchorsNode$measure$1.class", "name": "androidx/compose/material3/internal/DraggableAnchorsNode$measure$1.class", "size": 3534, "crc": -361757093}, {"key": "androidx/compose/material3/internal/DraggableAnchorsNode.class", "name": "androidx/compose/material3/internal/DraggableAnchorsNode.class", "size": 6677, "crc": -898595028}, {"key": "androidx/compose/material3/internal/DropdownMenuPositionProvider$2.class", "name": "androidx/compose/material3/internal/DropdownMenuPositionProvider$2.class", "size": 1753, "crc": 272523412}, {"key": "androidx/compose/material3/internal/DropdownMenuPositionProvider.class", "name": "androidx/compose/material3/internal/DropdownMenuPositionProvider.class", "size": 11443, "crc": **********}, {"key": "androidx/compose/material3/internal/ElevationDefaults.class", "name": "androidx/compose/material3/internal/ElevationDefaults.class", "size": 2416, "crc": 237900593}, {"key": "androidx/compose/material3/internal/ElevationKt.class", "name": "androidx/compose/material3/internal/ElevationKt.class", "size": 4665, "crc": -**********}, {"key": "androidx/compose/material3/internal/InputPhase.class", "name": "androidx/compose/material3/internal/InputPhase.class", "size": 1517, "crc": 257314725}, {"key": "androidx/compose/material3/internal/InternalMutatorMutex$Mutator.class", "name": "androidx/compose/material3/internal/InternalMutatorMutex$Mutator.class", "size": 2093, "crc": -826066147}, {"key": "androidx/compose/material3/internal/InternalMutatorMutex$mutate$2.class", "name": "androidx/compose/material3/internal/InternalMutatorMutex$mutate$2.class", "size": 7524, "crc": -301377378}, {"key": "androidx/compose/material3/internal/InternalMutatorMutex$mutateWith$2.class", "name": "androidx/compose/material3/internal/InternalMutatorMutex$mutateWith$2.class", "size": 7698, "crc": -1054592171}, {"key": "androidx/compose/material3/internal/InternalMutatorMutex.class", "name": "androidx/compose/material3/internal/InternalMutatorMutex.class", "size": 6918, "crc": -239391298}, {"key": "androidx/compose/material3/internal/InternalMutatorMutex_jvmKt.class", "name": "androidx/compose/material3/internal/InternalMutatorMutex_jvmKt.class", "size": 574, "crc": 113142814}, {"key": "androidx/compose/material3/internal/LegacyCalendarModelImpl$Companion.class", "name": "androidx/compose/material3/internal/LegacyCalendarModelImpl$Companion.class", "size": 4547, "crc": 902395749}, {"key": "androidx/compose/material3/internal/LegacyCalendarModelImpl.class", "name": "androidx/compose/material3/internal/LegacyCalendarModelImpl.class", "size": 10845, "crc": 1386680295}, {"key": "androidx/compose/material3/internal/Listener$Api33Impl.class", "name": "androidx/compose/material3/internal/Listener$Api33Impl.class", "size": 2158, "crc": 74207929}, {"key": "androidx/compose/material3/internal/Listener$switchAccessListener$1.class", "name": "androidx/compose/material3/internal/Listener$switchAccessListener$1.class", "size": 3935, "crc": -55877108}, {"key": "androidx/compose/material3/internal/Listener$touchExplorationListener$1.class", "name": "androidx/compose/material3/internal/Listener$touchExplorationListener$1.class", "size": 3453, "crc": -711117896}, {"key": "androidx/compose/material3/internal/Listener.class", "name": "androidx/compose/material3/internal/Listener.class", "size": 8694, "crc": -1767168551}, {"key": "androidx/compose/material3/internal/MapDraggableAnchors.class", "name": "androidx/compose/material3/internal/MapDraggableAnchors.class", "size": 5445, "crc": -564639445}, {"key": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1$2$1.class", "name": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1$2$1.class", "size": 2198, "crc": -157649944}, {"key": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1$2.class", "name": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1$2.class", "size": 5348, "crc": -994364899}, {"key": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1.class", "name": "androidx/compose/material3/internal/MappedInteractionSource$special$$inlined$map$1.class", "size": 3464, "crc": -364764422}, {"key": "androidx/compose/material3/internal/MappedInteractionSource.class", "name": "androidx/compose/material3/internal/MappedInteractionSource.class", "size": 5332, "crc": 543630091}, {"key": "androidx/compose/material3/internal/MenuPosition$Horizontal.class", "name": "androidx/compose/material3/internal/MenuPosition$Horizontal.class", "size": 1141, "crc": -1246263234}, {"key": "androidx/compose/material3/internal/MenuPosition$Vertical.class", "name": "androidx/compose/material3/internal/MenuPosition$Vertical.class", "size": 1003, "crc": 1333159728}, {"key": "androidx/compose/material3/internal/MenuPosition.class", "name": "androidx/compose/material3/internal/MenuPosition.class", "size": 5311, "crc": 385065811}, {"key": "androidx/compose/material3/internal/MutableWindowInsets.class", "name": "androidx/compose/material3/internal/MutableWindowInsets.class", "size": 4191, "crc": 1812242769}, {"key": "androidx/compose/material3/internal/PredictiveBack.class", "name": "androidx/compose/material3/internal/PredictiveBack.class", "size": 1230, "crc": 1290660397}, {"key": "androidx/compose/material3/internal/PredictiveBack_androidKt.class", "name": "androidx/compose/material3/internal/PredictiveBack_androidKt.class", "size": 895, "crc": 785510901}, {"key": "androidx/compose/material3/internal/ProvideContentColorTextStyleKt$ProvideContentColorTextStyle$1.class", "name": "androidx/compose/material3/internal/ProvideContentColorTextStyleKt$ProvideContentColorTextStyle$1.class", "size": 2349, "crc": -393427415}, {"key": "androidx/compose/material3/internal/ProvideContentColorTextStyleKt.class", "name": "androidx/compose/material3/internal/ProvideContentColorTextStyleKt.class", "size": 5463, "crc": 1226759862}, {"key": "androidx/compose/material3/internal/Strings$Companion.class", "name": "androidx/compose/material3/internal/Strings$Companion.class", "size": 17674, "crc": -477145584}, {"key": "androidx/compose/material3/internal/Strings.class", "name": "androidx/compose/material3/internal/Strings.class", "size": 2566, "crc": 927452433}, {"key": "androidx/compose/material3/internal/Strings_androidKt.class", "name": "androidx/compose/material3/internal/Strings_androidKt.class", "size": 5012, "crc": 1369202801}, {"key": "androidx/compose/material3/internal/SystemBarsDefaultInsets_androidKt.class", "name": "androidx/compose/material3/internal/SystemBarsDefaultInsets_androidKt.class", "size": 2086, "crc": -127405905}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "size": 2871, "crc": 1296234924}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1$1.class", "size": 1407, "crc": 1285289707}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1.class", "size": 10800, "crc": -355000813}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$containerWithId$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$containerWithId$1.class", "size": 9877, "crc": -1774720903}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "size": 5763, "crc": -1967533532}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "size": 3461, "crc": 1099689055}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1$1$1.class", "size": 2071, "crc": 190679742}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "size": 11933, "crc": 1387112591}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1$1$1.class", "size": 2027, "crc": 618526183}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1.class", "size": 11653, "crc": -1589919978}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1$1$1.class", "size": 2027, "crc": 1678309848}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1.class", "size": 11653, "crc": -555524036}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSupporting$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedSupporting$1$1.class", "size": 3693, "crc": -563921172}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "size": 3464, "crc": 1785851142}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$showPlaceholder$2$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$showPlaceholder$2$1.class", "size": 2274, "crc": -1480263380}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$showPrefixSuffix$2$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$3$showPrefixSuffix$2$1.class", "size": 2277, "crc": -267781}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$4.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$CommonDecorationBox$4.class", "size": 5520, "crc": 171231864}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$Decoration$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$Decoration$1.class", "size": 2268, "crc": -327625065}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$Decoration$2.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$Decoration$2.class", "size": 2070, "crc": -1483836468}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelContentColor$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelContentColor$1.class", "size": 4007, "crc": 1575193208}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelProgress$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelProgress$1.class", "size": 3917, "crc": -1896425235}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelTextStyleColor$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$labelTextStyleColor$1.class", "size": 4015, "crc": 926322623}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$placeholderOpacity$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$placeholderOpacity$1.class", "size": 4564, "crc": -122390258}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$prefixSuffixOpacity$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$TextFieldTransitionScope$prefixSuffixOpacity$1.class", "size": 3941, "crc": -1460934709}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$WhenMappings.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$WhenMappings.class", "size": 1245, "crc": -104380109}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$defaultErrorSemantics$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$defaultErrorSemantics$1.class", "size": 1864, "crc": -1403121852}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$textFieldBackground$1$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$textFieldBackground$1$1.class", "size": 2238, "crc": -1907921806}, {"key": "androidx/compose/material3/internal/TextFieldImplKt$textFieldBackground$1.class", "name": "androidx/compose/material3/internal/TextFieldImplKt$textFieldBackground$1.class", "size": 2668, "crc": -987988509}, {"key": "androidx/compose/material3/internal/TextFieldImplKt.class", "name": "androidx/compose/material3/internal/TextFieldImplKt.class", "size": 64141, "crc": 1807056236}, {"key": "androidx/compose/material3/internal/TextFieldType.class", "name": "androidx/compose/material3/internal/TextFieldType.class", "size": 1457, "crc": 612922460}, {"key": "androidx/compose/material3/internal/WindowAlignmentMarginPosition$Horizontal.class", "name": "androidx/compose/material3/internal/WindowAlignmentMarginPosition$Horizontal.class", "size": 4363, "crc": -2015742105}, {"key": "androidx/compose/material3/internal/WindowAlignmentMarginPosition$Vertical.class", "name": "androidx/compose/material3/internal/WindowAlignmentMarginPosition$Vertical.class", "size": 4136, "crc": -747824514}, {"key": "androidx/compose/material3/internal/WindowAlignmentMarginPosition.class", "name": "androidx/compose/material3/internal/WindowAlignmentMarginPosition.class", "size": 1055, "crc": 507606876}, {"key": "androidx/compose/material3/pulltorefresh/ArrowValues.class", "name": "androidx/compose/material3/pulltorefresh/ArrowValues.class", "size": 1305, "crc": 49568568}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1$1$1.class", "size": 1556, "crc": -2069205211}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1.class", "size": 5939, "crc": 1442428150}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$2.class", "size": 2465, "crc": -1871899160}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults.class", "size": 14539, "crc": -511798108}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshElement.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshElement.class", "size": 7922, "crc": 302943551}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$1$1.class", "size": 2462, "crc": -1283432473}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$2$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$2$1.class", "size": 6379, "crc": 1990084990}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$3.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$3.class", "size": 2057, "crc": 526930628}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$targetAlpha$2$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$targetAlpha$2$1.class", "size": 1726, "crc": -32893812}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshBox$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshBox$1.class", "size": 3965, "crc": -650164887}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshBox$3.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshBox$3.class", "size": 3384, "crc": -564164857}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$pullToRefreshIndicator$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$pullToRefreshIndicator$1.class", "size": 4817, "crc": 1616010500}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$pullToRefreshIndicator$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$pullToRefreshIndicator$2.class", "size": 2772, "crc": -1299609432}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$1.class", "size": 1603, "crc": 742167283}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt.class", "size": 32197, "crc": 2121508901}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$animateToHidden$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$animateToHidden$1.class", "size": 2076, "crc": -2076516593}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$animateToThreshold$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$animateToThreshold$1.class", "size": 2091, "crc": 769588991}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onAttach$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onAttach$1.class", "size": 3891, "crc": 1968151113}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onPostScroll$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onPostScroll$1.class", "size": 4042, "crc": 677367289}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onPreFling$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onPreFling$1.class", "size": 1908, "crc": 1492744756}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onRelease$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$onRelease$1.class", "size": 2113, "crc": 1629868879}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$update$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode$update$1.class", "size": 3838, "crc": -43130743}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshModifierNode.class", "size": 15968, "crc": -121413974}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshState.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshState.class", "size": 1586, "crc": 282507491}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$1.class", "size": 2150, "crc": -273262117}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$2.class", "size": 2259, "crc": -1951278912}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion.class", "size": 1466, "crc": -885665067}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl.class", "size": 5861, "crc": -1488088720}, {"key": "androidx/compose/material3/tokens/AssistChipTokens.class", "name": "androidx/compose/material3/tokens/AssistChipTokens.class", "size": 9184, "crc": -633445015}, {"key": "androidx/compose/material3/tokens/BadgeTokens.class", "name": "androidx/compose/material3/tokens/BadgeTokens.class", "size": 3729, "crc": 99073294}, {"key": "androidx/compose/material3/tokens/BottomAppBarTokens.class", "name": "androidx/compose/material3/tokens/BottomAppBarTokens.class", "size": 3040, "crc": 394157763}, {"key": "androidx/compose/material3/tokens/CheckboxTokens.class", "name": "androidx/compose/material3/tokens/CheckboxTokens.class", "size": 13810, "crc": 57308406}, {"key": "androidx/compose/material3/tokens/ColorDarkTokens.class", "name": "androidx/compose/material3/tokens/ColorDarkTokens.class", "size": 10454, "crc": 1612432069}, {"key": "androidx/compose/material3/tokens/ColorLightTokens.class", "name": "androidx/compose/material3/tokens/ColorLightTokens.class", "size": 10606, "crc": 2009655128}, {"key": "androidx/compose/material3/tokens/ColorSchemeKeyTokens.class", "name": "androidx/compose/material3/tokens/ColorSchemeKeyTokens.class", "size": 4723, "crc": 2078108095}, {"key": "androidx/compose/material3/tokens/DateInputModalTokens.class", "name": "androidx/compose/material3/tokens/DateInputModalTokens.class", "size": 5092, "crc": -1734599586}, {"key": "androidx/compose/material3/tokens/DatePickerModalTokens.class", "name": "androidx/compose/material3/tokens/DatePickerModalTokens.class", "size": 12847, "crc": -2124649129}, {"key": "androidx/compose/material3/tokens/DialogTokens.class", "name": "androidx/compose/material3/tokens/DialogTokens.class", "size": 5168, "crc": -2034354506}, {"key": "androidx/compose/material3/tokens/DividerTokens.class", "name": "androidx/compose/material3/tokens/DividerTokens.class", "size": 2227, "crc": -416672709}, {"key": "androidx/compose/material3/tokens/ElevatedButtonTokens.class", "name": "androidx/compose/material3/tokens/ElevatedButtonTokens.class", "size": 7316, "crc": -1070394000}, {"key": "androidx/compose/material3/tokens/ElevatedCardTokens.class", "name": "androidx/compose/material3/tokens/ElevatedCardTokens.class", "size": 4857, "crc": 1514066594}, {"key": "androidx/compose/material3/tokens/ElevationTokens.class", "name": "androidx/compose/material3/tokens/ElevationTokens.class", "size": 3022, "crc": 328040878}, {"key": "androidx/compose/material3/tokens/ExtendedFabPrimaryTokens.class", "name": "androidx/compose/material3/tokens/ExtendedFabPrimaryTokens.class", "size": 6692, "crc": 1166156927}, {"key": "androidx/compose/material3/tokens/FabPrimaryLargeTokens.class", "name": "androidx/compose/material3/tokens/FabPrimaryLargeTokens.class", "size": 5772, "crc": 1332240998}, {"key": "androidx/compose/material3/tokens/FabPrimarySmallTokens.class", "name": "androidx/compose/material3/tokens/FabPrimarySmallTokens.class", "size": 5768, "crc": 1598094843}, {"key": "androidx/compose/material3/tokens/FabPrimaryTokens.class", "name": "androidx/compose/material3/tokens/FabPrimaryTokens.class", "size": 5702, "crc": 99761523}, {"key": "androidx/compose/material3/tokens/FabSecondaryTokens.class", "name": "androidx/compose/material3/tokens/FabSecondaryTokens.class", "size": 5732, "crc": -1697364313}, {"key": "androidx/compose/material3/tokens/FilledAutocompleteTokens.class", "name": "androidx/compose/material3/tokens/FilledAutocompleteTokens.class", "size": 16680, "crc": -100796962}, {"key": "androidx/compose/material3/tokens/FilledButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledButtonTokens.class", "size": 6733, "crc": -502553108}, {"key": "androidx/compose/material3/tokens/FilledCardTokens.class", "name": "androidx/compose/material3/tokens/FilledCardTokens.class", "size": 4842, "crc": -332038959}, {"key": "androidx/compose/material3/tokens/FilledIconButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledIconButtonTokens.class", "size": 6855, "crc": 729548042}, {"key": "androidx/compose/material3/tokens/FilledTextFieldTokens.class", "name": "androidx/compose/material3/tokens/FilledTextFieldTokens.class", "size": 15284, "crc": -937999980}, {"key": "androidx/compose/material3/tokens/FilledTonalButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledTonalButtonTokens.class", "size": 6820, "crc": 136280046}, {"key": "androidx/compose/material3/tokens/FilledTonalIconButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledTonalIconButtonTokens.class", "size": 6971, "crc": 1798833145}, {"key": "androidx/compose/material3/tokens/FilterChipTokens.class", "name": "androidx/compose/material3/tokens/FilterChipTokens.class", "size": 16643, "crc": -1176315517}, {"key": "androidx/compose/material3/tokens/IconButtonTokens.class", "name": "androidx/compose/material3/tokens/IconButtonTokens.class", "size": 4600, "crc": -1990604044}, {"key": "androidx/compose/material3/tokens/InputChipTokens.class", "name": "androidx/compose/material3/tokens/InputChipTokens.class", "size": 13218, "crc": 588637819}, {"key": "androidx/compose/material3/tokens/ListTokens.class", "name": "androidx/compose/material3/tokens/ListTokens.class", "size": 14986, "crc": -388089834}, {"key": "androidx/compose/material3/tokens/MenuTokens.class", "name": "androidx/compose/material3/tokens/MenuTokens.class", "size": 3124, "crc": 748397145}, {"key": "androidx/compose/material3/tokens/MotionTokens.class", "name": "androidx/compose/material3/tokens/MotionTokens.class", "size": 4324, "crc": -1327106094}, {"key": "androidx/compose/material3/tokens/NavigationBarTokens.class", "name": "androidx/compose/material3/tokens/NavigationBarTokens.class", "size": 8021, "crc": -1329915809}, {"key": "androidx/compose/material3/tokens/NavigationDrawerTokens.class", "name": "androidx/compose/material3/tokens/NavigationDrawerTokens.class", "size": 9668, "crc": -909855751}, {"key": "androidx/compose/material3/tokens/NavigationRailTokens.class", "name": "androidx/compose/material3/tokens/NavigationRailTokens.class", "size": 9251, "crc": -845514495}, {"key": "androidx/compose/material3/tokens/OutlinedAutocompleteTokens.class", "name": "androidx/compose/material3/tokens/OutlinedAutocompleteTokens.class", "size": 16073, "crc": 562506333}, {"key": "androidx/compose/material3/tokens/OutlinedButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedButtonTokens.class", "size": 6397, "crc": 393324429}, {"key": "androidx/compose/material3/tokens/OutlinedCardTokens.class", "name": "androidx/compose/material3/tokens/OutlinedCardTokens.class", "size": 5728, "crc": 1530296202}, {"key": "androidx/compose/material3/tokens/OutlinedIconButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedIconButtonTokens.class", "size": 5878, "crc": -808671986}, {"key": "androidx/compose/material3/tokens/OutlinedSegmentedButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedSegmentedButtonTokens.class", "size": 8213, "crc": -1744396673}, {"key": "androidx/compose/material3/tokens/OutlinedTextFieldTokens.class", "name": "androidx/compose/material3/tokens/OutlinedTextFieldTokens.class", "size": 14132, "crc": -2030643182}, {"key": "androidx/compose/material3/tokens/PaletteTokens.class", "name": "androidx/compose/material3/tokens/PaletteTokens.class", "size": 16917, "crc": 1816169298}, {"key": "androidx/compose/material3/tokens/PlainTooltipTokens.class", "name": "androidx/compose/material3/tokens/PlainTooltipTokens.class", "size": 2233, "crc": -1179624714}, {"key": "androidx/compose/material3/tokens/PrimaryNavigationTabTokens.class", "name": "androidx/compose/material3/tokens/PrimaryNavigationTabTokens.class", "size": 8211, "crc": -1100785954}, {"key": "androidx/compose/material3/tokens/ProgressIndicatorTokens.class", "name": "androidx/compose/material3/tokens/ProgressIndicatorTokens.class", "size": 4476, "crc": 865671071}, {"key": "androidx/compose/material3/tokens/RadioButtonTokens.class", "name": "androidx/compose/material3/tokens/RadioButtonTokens.class", "size": 4533, "crc": 2146955788}, {"key": "androidx/compose/material3/tokens/RichTooltipTokens.class", "name": "androidx/compose/material3/tokens/RichTooltipTokens.class", "size": 4014, "crc": 10549993}, {"key": "androidx/compose/material3/tokens/ScrimTokens.class", "name": "androidx/compose/material3/tokens/ScrimTokens.class", "size": 1302, "crc": -1824485867}, {"key": "androidx/compose/material3/tokens/SearchBarTokens.class", "name": "androidx/compose/material3/tokens/SearchBarTokens.class", "size": 5366, "crc": 1531630007}, {"key": "androidx/compose/material3/tokens/SearchViewTokens.class", "name": "androidx/compose/material3/tokens/SearchViewTokens.class", "size": 5172, "crc": 658025972}, {"key": "androidx/compose/material3/tokens/SecondaryNavigationTabTokens.class", "name": "androidx/compose/material3/tokens/SecondaryNavigationTabTokens.class", "size": 6106, "crc": -66623455}, {"key": "androidx/compose/material3/tokens/ShapeKeyTokens.class", "name": "androidx/compose/material3/tokens/ShapeKeyTokens.class", "size": 2087, "crc": -1445594464}, {"key": "androidx/compose/material3/tokens/ShapeTokens.class", "name": "androidx/compose/material3/tokens/ShapeTokens.class", "size": 5622, "crc": -42297426}, {"key": "androidx/compose/material3/tokens/SheetBottomTokens.class", "name": "androidx/compose/material3/tokens/SheetBottomTokens.class", "size": 4227, "crc": -1660621486}, {"key": "androidx/compose/material3/tokens/SliderTokens.class", "name": "androidx/compose/material3/tokens/SliderTokens.class", "size": 12912, "crc": -1275452435}, {"key": "androidx/compose/material3/tokens/SnackbarTokens.class", "name": "androidx/compose/material3/tokens/SnackbarTokens.class", "size": 5849, "crc": -396295341}, {"key": "androidx/compose/material3/tokens/StateTokens.class", "name": "androidx/compose/material3/tokens/StateTokens.class", "size": 1116, "crc": -1258884749}, {"key": "androidx/compose/material3/tokens/SuggestionChipTokens.class", "name": "androidx/compose/material3/tokens/SuggestionChipTokens.class", "size": 9377, "crc": -2065436801}, {"key": "androidx/compose/material3/tokens/SwitchTokens.class", "name": "androidx/compose/material3/tokens/SwitchTokens.class", "size": 14327, "crc": -1701963486}, {"key": "androidx/compose/material3/tokens/TextButtonTokens.class", "name": "androidx/compose/material3/tokens/TextButtonTokens.class", "size": 5113, "crc": 1488346490}, {"key": "androidx/compose/material3/tokens/TimeInputTokens.class", "name": "androidx/compose/material3/tokens/TimeInputTokens.class", "size": 10565, "crc": -333120342}, {"key": "androidx/compose/material3/tokens/TimePickerTokens.class", "name": "androidx/compose/material3/tokens/TimePickerTokens.class", "size": 14937, "crc": 449073377}, {"key": "androidx/compose/material3/tokens/TopAppBarLargeTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarLargeTokens.class", "size": 4761, "crc": 664538376}, {"key": "androidx/compose/material3/tokens/TopAppBarMediumTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarMediumTokens.class", "size": 4773, "crc": -1826418538}, {"key": "androidx/compose/material3/tokens/TopAppBarSmallCenteredTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarSmallCenteredTokens.class", "size": 5743, "crc": -2010209160}, {"key": "androidx/compose/material3/tokens/TopAppBarSmallTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarSmallTokens.class", "size": 4973, "crc": 1660372946}, {"key": "androidx/compose/material3/tokens/TypeScaleTokens.class", "name": "androidx/compose/material3/tokens/TypeScaleTokens.class", "size": 16257, "crc": -340445254}, {"key": "androidx/compose/material3/tokens/TypefaceTokens.class", "name": "androidx/compose/material3/tokens/TypefaceTokens.class", "size": 2376, "crc": -2023065903}, {"key": "androidx/compose/material3/tokens/TypographyKeyTokens.class", "name": "androidx/compose/material3/tokens/TypographyKeyTokens.class", "size": 2361, "crc": -1318219516}, {"key": "androidx/compose/material3/tokens/TypographyTokens.class", "name": "androidx/compose/material3/tokens/TypographyTokens.class", "size": 9558, "crc": -1961738367}, {"key": "androidx/compose/material3/tokens/TypographyTokensKt.class", "name": "androidx/compose/material3/tokens/TypographyTokensKt.class", "size": 2921, "crc": -128367115}, {"key": "META-INF/androidx.compose.material3_material3.version", "name": "META-INF/androidx.compose.material3_material3.version", "size": 6, "crc": -271906938}, {"key": "META-INF/material3_release.kotlin_module", "name": "META-INF/material3_release.kotlin_module", "size": 1808, "crc": -1730507458}]