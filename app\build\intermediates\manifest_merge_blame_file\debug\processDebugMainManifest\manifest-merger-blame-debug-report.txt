1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="cn.ykload.flowmix"
4    android:versionCode="2"
5    android:versionName="Alpha 2" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="36" />
10
11    <!-- 音频效果权限 -->
12    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
12-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:6:22-77
13
14    <!-- 蓝牙权限 - 用于检测蓝牙音频设备 -->
15    <uses-permission
15-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.BLUETOOTH"
16-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:9:22-65
17        android:maxSdkVersion="30" />
17-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.BLUETOOTH_ADMIN"
19-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:11:22-71
20        android:maxSdkVersion="30" />
20-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- Android 12+ 蓝牙权限 -->
23    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
23-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:5-76
23-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:15:22-73
24    <uses-permission
24-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:5-18:31
25        android:name="android.permission.BLUETOOTH_SCAN"
25-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:16:22-70
26        android:usesPermissionFlags="neverForLocation" />
26-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:17:9-55
27
28    <!-- 网络权限 -->
29    <uses-permission android:name="android.permission.INTERNET" />
29-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:5-67
29-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:21:22-64
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:5-79
30-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:22:22-76
31
32    <!-- 文件读取权限 - Android 12及以下 -->
33    <uses-permission
33-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:5-26:38
34        android:name="android.permission.READ_EXTERNAL_STORAGE"
34-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:25:22-77
35        android:maxSdkVersion="32" />
35-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:26:9-35
36
37    <!-- Android 13+ 媒体文件权限 -->
38    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
38-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:5-75
38-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:29:22-72
39    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
39-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:5-76
39-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:30:22-73
40    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
40-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:5-75
40-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:31:22-72
41
42    <!-- 管理外部存储权限 - 用于访问所有文件 -->
43    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
43-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:5-35:40
43-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:34:22-79
44
45    <!-- 查询其他应用的权限 -->
46    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
46-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:5-39:53
46-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:38:22-74
47
48    <!-- 前台服务权限 - 用于保活服务 -->
49    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
49-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:5-77
49-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:42:22-74
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
50-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:5-92
50-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:43:22-89
51
52    <!-- 通知权限 - Android 13+ -->
53    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
53-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:5-77
53-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:46:22-74
54
55    <!-- 开机自启动权限 -->
56    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
56-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:5-81
56-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:49:22-78
57
58    <!-- 唤醒锁权限 - 用于保持CPU运行 -->
59    <uses-permission android:name="android.permission.WAKE_LOCK" />
59-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:5-68
59-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:52:22-65
60
61    <!-- 声明需要查询的特定应用 -->
62    <queries>
62-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:55:5-57:15
63        <package android:name="cn.ykload.seeq" />
63-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:9-50
63-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:56:18-47
64    </queries>
65
66    <permission
66-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
67        android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
71
72    <application
72-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:59:5-145:19
73        android:allowBackup="true"
73-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:60:9-35
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\223d83a19ec36f69ccc1b09c90f704ed\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
75        android:dataExtractionRules="@xml/data_extraction_rules"
75-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:61:9-65
76        android:debuggable="true"
77        android:extractNativeLibs="false"
78        android:fullBackupContent="@xml/backup_rules"
78-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:62:9-54
79        android:icon="@mipmap/ic_launcher"
79-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:63:9-43
80        android:label="@string/app_name"
80-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:64:9-41
81        android:roundIcon="@mipmap/ic_launcher_round"
81-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:65:9-54
82        android:supportsRtl="true"
82-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:66:9-35
83        android:testOnly="true"
84        android:theme="@style/Theme.Flowmix" >
84-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:67:9-45
85
86        <!-- 应用级元数据：声明为音频效果应用 -->
87        <meta-data
87-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:70:9-72:36
88            android:name="android.media.audio_effect_app"
88-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:71:13-58
89            android:value="true" />
89-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:72:13-33
90        <meta-data
90-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:73:9-75:39
91            android:name="android.media.audio_effect.equalizer"
91-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:74:13-64
92            android:value="Flowmix" />
92-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:75:13-36
93        <meta-data
93-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:76:9-78:61
94            android:name="android.media.audio_effect.description"
94-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:77:13-66
95            android:value="Flowmix - 全局 AutoEq 应用  \awa/" />
95-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:78:13-58
96
97        <activity
97-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:79:9-98:20
98            android:name="cn.ykload.flowmix.MainActivity"
98-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:80:13-41
99            android:exported="true"
99-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:81:13-36
100            android:label="@string/app_name"
100-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:82:13-45
101            android:launchMode="singleTop"
101-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:84:13-43
102            android:theme="@style/Theme.Flowmix" >
102-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:83:13-49
103            <intent-filter>
103-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:85:13-89:29
104                <action android:name="android.intent.action.MAIN" />
104-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:17-69
104-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:86:25-66
105
106                <category android:name="android.intent.category.LAUNCHER" />
106-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:17-77
106-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:88:27-74
107            </intent-filter>
108
109            <!-- 处理通知点击事件 -->
110            <intent-filter>
110-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:92:13-96:29
111                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_TOGGLE_FLOWMIX" />
111-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:17-114
111-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:93:25-111
112                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_OPEN_APP" />
112-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:17-108
112-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:94:25-105
113
114                <category android:name="android.intent.category.DEFAULT" />
114-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:17-76
114-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:27-73
115            </intent-filter>
116        </activity>
117
118        <!-- 均衡器Activity - 处理来自其他应用的音频效果控制请求 -->
119        <activity
119-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:101:9-123:20
120            android:name="cn.ykload.flowmix.EqualizerActivity"
120-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:102:13-46
121            android:exported="true"
121-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:103:13-36
122            android:label="Flowmix - 全局 AutoEq 应用  \awa/"
122-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:104:13-58
123            android:launchMode="singleTop"
123-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:106:13-43
124            android:theme="@style/Theme.Flowmix" >
124-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:105:13-49
125
126            <!-- 音频效果控制会话意图过滤器 - 注册为系统均衡器 -->
127            <intent-filter>
127-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:109:13-114:29
128                <action android:name="android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION" />
128-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:17-97
128-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:110:25-94
129                <action android:name="android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION" />
129-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:17-98
129-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:111:25-95
130                <action android:name="android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL" />
130-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:17-98
130-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:112:25-95
131
132                <category android:name="android.intent.category.DEFAULT" />
132-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:17-76
132-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:95:27-73
133            </intent-filter>
134
135            <!-- 元数据：标识为音频效果应用 -->
136            <meta-data
136-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:117:13-119:40
137                android:name="android.media.audio_effect"
137-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:118:17-58
138                android:value="true" />
138-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:119:17-37
139            <meta-data
139-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:73:9-75:39
140                android:name="android.media.audio_effect.equalizer"
140-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:74:13-64
141                android:value="true" />
141-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:75:13-36
142        </activity>
143
144        <!-- Flowmix 保活前台服务 -->
145        <service
145-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:126:9-131:44
146            android:name="cn.ykload.flowmix.service.FlowmixKeepAliveService"
146-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:127:13-60
147            android:enabled="true"
147-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:128:13-35
148            android:exported="false"
148-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:129:13-37
149            android:foregroundServiceType="mediaPlayback"
149-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:130:13-58
150            android:stopWithTask="false" />
150-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:131:13-41
151
152        <!-- 开机自启动接收器 -->
153        <receiver
153-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:134:9-144:20
154            android:name="cn.ykload.flowmix.receiver.BootReceiver"
154-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:135:13-50
155            android:enabled="true"
155-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:136:13-35
156            android:exported="true" >
156-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:137:13-36
157            <intent-filter android:priority="1000" >
157-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:13-143:29
157-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:138:28-51
158                <action android:name="android.intent.action.BOOT_COMPLETED" />
158-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:17-79
158-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:139:25-76
159                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
159-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:17-84
159-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:140:25-81
160                <action android:name="android.intent.action.PACKAGE_REPLACED" />
160-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:17-81
160-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:141:25-78
161
162                <data android:scheme="package" />
162-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:17-50
162-->D:\Projects\flowmix\app\src\main\AndroidManifest.xml:142:23-47
163            </intent-filter>
164        </receiver>
165
166        <activity
166-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\634dcf0e4a7ce464d6a976b3fe27c15b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
167            android:name="androidx.compose.ui.tooling.PreviewActivity"
167-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\634dcf0e4a7ce464d6a976b3fe27c15b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
168            android:exported="true" />
168-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\634dcf0e4a7ce464d6a976b3fe27c15b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
169
170        <provider
170-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
171            android:name="androidx.startup.InitializationProvider"
171-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
172            android:authorities="cn.ykload.flowmix.androidx-startup"
172-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
173            android:exported="false" >
173-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
174            <meta-data
174-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.emoji2.text.EmojiCompatInitializer"
175-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
176                android:value="androidx.startup" />
176-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c1f85691ee8b64c5100ddce8a67ab6\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
177            <meta-data
177-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
178                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
178-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
179                android:value="androidx.startup" />
179-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77927df24898ef2cabaa745d5d82b527\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
180            <meta-data
180-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
181                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
182                android:value="androidx.startup" />
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
183        </provider>
184
185        <activity
185-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d88f00958178542db2de6e13981279a\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
186            android:name="androidx.activity.ComponentActivity"
186-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d88f00958178542db2de6e13981279a\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
187            android:exported="true"
187-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d88f00958178542db2de6e13981279a\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
188            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
188-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d88f00958178542db2de6e13981279a\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
189
190        <receiver
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
191            android:name="androidx.profileinstaller.ProfileInstallReceiver"
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
192            android:directBootAware="false"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
193            android:enabled="true"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
194            android:exported="true"
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
195            android:permission="android.permission.DUMP" >
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
197                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
200                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
203                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
206                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd7babaf15786ff5035152134ab6effb\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
207            </intent-filter>
208        </receiver>
209    </application>
210
211</manifest>
