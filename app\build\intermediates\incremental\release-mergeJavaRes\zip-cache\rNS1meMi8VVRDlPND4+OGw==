[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 112, "crc": -636089375}, {"key": "META-INF/kotlinx-serialization-core.kotlin_module", "name": "META-INF/kotlinx-serialization-core.kotlin_module", "size": 844, "crc": 1796891801}, {"key": "kotlinx/serialization/BinaryFormat.class", "name": "kotlinx/serialization/BinaryFormat.class", "size": 1302, "crc": 794156890}, {"key": "kotlinx/serialization/Contextual.class", "name": "kotlinx/serialization/Contextual.class", "size": 828, "crc": -1657719908}, {"key": "kotlinx/serialization/ContextualSerializer.class", "name": "kotlinx/serialization/ContextualSerializer.class", "size": 6927, "crc": 1298493107}, {"key": "kotlinx/serialization/DeserializationStrategy.class", "name": "kotlinx/serialization/DeserializationStrategy.class", "size": 1042, "crc": -2132553441}, {"key": "kotlinx/serialization/EncodeDefault$Mode.class", "name": "kotlinx/serialization/EncodeDefault$Mode.class", "size": 2019, "crc": -1030155464}, {"key": "kotlinx/serialization/EncodeDefault.class", "name": "kotlinx/serialization/EncodeDefault.class", "size": 1100, "crc": 231847713}, {"key": "kotlinx/serialization/ExperimentalSerializationApi.class", "name": "kotlinx/serialization/ExperimentalSerializationApi.class", "size": 1119, "crc": 131036278}, {"key": "kotlinx/serialization/InheritableSerialInfo.class", "name": "kotlinx/serialization/InheritableSerialInfo.class", "size": 995, "crc": -323741846}, {"key": "kotlinx/serialization/InternalSerializationApi.class", "name": "kotlinx/serialization/InternalSerializationApi.class", "size": 1109, "crc": -953998344}, {"key": "kotlinx/serialization/KSerializer.class", "name": "kotlinx/serialization/KSerializer.class", "size": 1048, "crc": -734361883}, {"key": "kotlinx/serialization/KeepGeneratedSerializer.class", "name": "kotlinx/serialization/KeepGeneratedSerializer.class", "size": 887, "crc": -1344076332}, {"key": "kotlinx/serialization/MetaSerializable.class", "name": "kotlinx/serialization/MetaSerializable.class", "size": 893, "crc": 1573172525}, {"key": "kotlinx/serialization/MissingFieldException.class", "name": "kotlinx/serialization/MissingFieldException.class", "size": 3256, "crc": 2004157313}, {"key": "kotlinx/serialization/Polymorphic.class", "name": "kotlinx/serialization/Polymorphic.class", "size": 756, "crc": -925848346}, {"key": "kotlinx/serialization/PolymorphicSerializer.class", "name": "kotlinx/serialization/PolymorphicSerializer.class", "size": 6799, "crc": -493435780}, {"key": "kotlinx/serialization/PolymorphicSerializerKt.class", "name": "kotlinx/serialization/PolymorphicSerializerKt.class", "size": 3446, "crc": 1603631748}, {"key": "kotlinx/serialization/Required.class", "name": "kotlinx/serialization/Required.class", "size": 750, "crc": 707287693}, {"key": "kotlinx/serialization/SealedClassSerializer$special$$inlined$groupingBy$1.class", "name": "kotlinx/serialization/SealedClassSerializer$special$$inlined$groupingBy$1.class", "size": 3028, "crc": -1132313131}, {"key": "kotlinx/serialization/SealedClassSerializer.class", "name": "kotlinx/serialization/SealedClassSerializer.class", "size": 14119, "crc": -909863643}, {"key": "kotlinx/serialization/SerialFormat.class", "name": "kotlinx/serialization/SerialFormat.class", "size": 656, "crc": 1116875190}, {"key": "kotlinx/serialization/SerialFormatKt.class", "name": "kotlinx/serialization/SerialFormatKt.class", "size": 5534, "crc": -442010774}, {"key": "kotlinx/serialization/SerialInfo.class", "name": "kotlinx/serialization/SerialInfo.class", "size": 973, "crc": -435805041}, {"key": "kotlinx/serialization/SerialName.class", "name": "kotlinx/serialization/SerialName.class", "size": 889, "crc": 777763779}, {"key": "kotlinx/serialization/Serializable.class", "name": "kotlinx/serialization/Serializable.class", "size": 1129, "crc": 1203752277}, {"key": "kotlinx/serialization/SerializationException.class", "name": "kotlinx/serialization/SerializationException.class", "size": 1321, "crc": -126440947}, {"key": "kotlinx/serialization/SerializationStrategy.class", "name": "kotlinx/serialization/SerializationStrategy.class", "size": 1070, "crc": -759699021}, {"key": "kotlinx/serialization/Serializer.class", "name": "kotlinx/serialization/Serializer.class", "size": 1105, "crc": -2081033695}, {"key": "kotlinx/serialization/SerializersCacheKt.class", "name": "kotlinx/serialization/SerializersCacheKt.class", "size": 8451, "crc": 153486823}, {"key": "kotlinx/serialization/SerializersKt.class", "name": "kotlinx/serialization/SerializersKt.class", "size": 7217, "crc": -1899719293}, {"key": "kotlinx/serialization/SerializersKt__SerializersJvmKt.class", "name": "kotlinx/serialization/SerializersKt__SerializersJvmKt.class", "size": 13957, "crc": -810404211}, {"key": "kotlinx/serialization/SerializersKt__SerializersKt.class", "name": "kotlinx/serialization/SerializersKt__SerializersKt.class", "size": 20988, "crc": 589251661}, {"key": "kotlinx/serialization/StringFormat.class", "name": "kotlinx/serialization/StringFormat.class", "size": 1361, "crc": 410817885}, {"key": "kotlinx/serialization/Transient.class", "name": "kotlinx/serialization/Transient.class", "size": 752, "crc": -1670263372}, {"key": "kotlinx/serialization/UnknownFieldException.class", "name": "kotlinx/serialization/UnknownFieldException.class", "size": 1262, "crc": 1472820300}, {"key": "kotlinx/serialization/UseContextualSerialization.class", "name": "kotlinx/serialization/UseContextualSerialization.class", "size": 988, "crc": 1010609125}, {"key": "kotlinx/serialization/UseSerializers.class", "name": "kotlinx/serialization/UseSerializers.class", "size": 1078, "crc": -885752350}, {"key": "kotlinx/serialization/builtins/BuiltinSerializersKt.class", "name": "kotlinx/serialization/builtins/BuiltinSerializersKt.class", "size": 18236, "crc": -819719004}, {"key": "kotlinx/serialization/builtins/LongAsStringSerializer.class", "name": "kotlinx/serialization/builtins/LongAsStringSerializer.class", "size": 3202, "crc": -635883191}, {"key": "kotlinx/serialization/descriptors/ClassSerialDescriptorBuilder.class", "name": "kotlinx/serialization/descriptors/ClassSerialDescriptorBuilder.class", "size": 6206, "crc": 2052217199}, {"key": "kotlinx/serialization/descriptors/ContextAwareKt.class", "name": "kotlinx/serialization/descriptors/ContextAwareKt.class", "size": 5951, "crc": 1328911864}, {"key": "kotlinx/serialization/descriptors/ContextDescriptor.class", "name": "kotlinx/serialization/descriptors/ContextDescriptor.class", "size": 4576, "crc": 1698690694}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind$OPEN.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind$OPEN.class", "size": 917, "crc": 2037863098}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind$SEALED.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind$SEALED.class", "size": 923, "crc": -1605028953}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind.class", "size": 1223, "crc": 743590832}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$BOOLEAN.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$BOOLEAN.class", "size": 918, "crc": -1670094439}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$BYTE.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$BYTE.class", "size": 909, "crc": 1367341145}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$CHAR.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$CHAR.class", "size": 909, "crc": -100310250}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$DOUBLE.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$DOUBLE.class", "size": 915, "crc": 890787507}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$FLOAT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$FLOAT.class", "size": 912, "crc": -1685743027}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$INT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$INT.class", "size": 906, "crc": 983654539}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$LONG.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$LONG.class", "size": 909, "crc": -674713143}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$SHORT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$SHORT.class", "size": 912, "crc": 1747524284}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$STRING.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$STRING.class", "size": 915, "crc": -1912109695}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind.class", "size": 2164, "crc": -1721380448}, {"key": "kotlinx/serialization/descriptors/SerialDescriptor$DefaultImpls.class", "name": "kotlinx/serialization/descriptors/SerialDescriptor$DefaultImpls.class", "size": 1636, "crc": -832769909}, {"key": "kotlinx/serialization/descriptors/SerialDescriptor.class", "name": "kotlinx/serialization/descriptors/SerialDescriptor.class", "size": 2172, "crc": -429578165}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorImpl.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorImpl.class", "size": 12565, "crc": 1305969739}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementDescriptors$1$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementDescriptors$1$1.class", "size": 2126, "crc": -1969786419}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementNames$1$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementNames$1$1.class", "size": 1994, "crc": -1515620471}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$1.class", "size": 2408, "crc": -1029968910}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$2.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$2.class", "size": 2316, "crc": -1655719707}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt.class", "size": 2195, "crc": -187512816}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorsKt.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorsKt.class", "size": 13193, "crc": 1695240962}, {"key": "kotlinx/serialization/descriptors/SerialKind$CONTEXTUAL.class", "name": "kotlinx/serialization/descriptors/SerialKind$CONTEXTUAL.class", "size": 978, "crc": -590233879}, {"key": "kotlinx/serialization/descriptors/SerialKind$ENUM.class", "name": "kotlinx/serialization/descriptors/SerialKind$ENUM.class", "size": 960, "crc": -2035809990}, {"key": "kotlinx/serialization/descriptors/SerialKind.class", "name": "kotlinx/serialization/descriptors/SerialKind.class", "size": 1958, "crc": 1084275255}, {"key": "kotlinx/serialization/descriptors/StructureKind$CLASS.class", "name": "kotlinx/serialization/descriptors/StructureKind$CLASS.class", "size": 912, "crc": 1728379533}, {"key": "kotlinx/serialization/descriptors/StructureKind$LIST.class", "name": "kotlinx/serialization/descriptors/StructureKind$LIST.class", "size": 909, "crc": -1900179827}, {"key": "kotlinx/serialization/descriptors/StructureKind$MAP.class", "name": "kotlinx/serialization/descriptors/StructureKind$MAP.class", "size": 906, "crc": -1907627828}, {"key": "kotlinx/serialization/descriptors/StructureKind$OBJECT.class", "name": "kotlinx/serialization/descriptors/StructureKind$OBJECT.class", "size": 915, "crc": 1658210404}, {"key": "kotlinx/serialization/descriptors/StructureKind.class", "name": "kotlinx/serialization/descriptors/StructureKind.class", "size": 1495, "crc": -327333744}, {"key": "kotlinx/serialization/descriptors/WrappedSerialDescriptor.class", "name": "kotlinx/serialization/descriptors/WrappedSerialDescriptor.class", "size": 3210, "crc": -498094741}, {"key": "kotlinx/serialization/encoding/AbstractDecoder.class", "name": "kotlinx/serialization/encoding/AbstractDecoder.class", "size": 11359, "crc": 2080028132}, {"key": "kotlinx/serialization/encoding/AbstractEncoder.class", "name": "kotlinx/serialization/encoding/AbstractEncoder.class", "size": 10287, "crc": 1906557929}, {"key": "kotlinx/serialization/encoding/ChunkedDecoder.class", "name": "kotlinx/serialization/encoding/ChunkedDecoder.class", "size": 891, "crc": -223113041}, {"key": "kotlinx/serialization/encoding/CompositeDecoder$Companion.class", "name": "kotlinx/serialization/encoding/CompositeDecoder$Companion.class", "size": 905, "crc": 1248734077}, {"key": "kotlinx/serialization/encoding/CompositeDecoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/CompositeDecoder$DefaultImpls.class", "size": 2193, "crc": 1821342722}, {"key": "kotlinx/serialization/encoding/CompositeDecoder.class", "name": "kotlinx/serialization/encoding/CompositeDecoder.class", "size": 4010, "crc": 1217616912}, {"key": "kotlinx/serialization/encoding/CompositeEncoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/CompositeEncoder$DefaultImpls.class", "size": 1051, "crc": 838424464}, {"key": "kotlinx/serialization/encoding/CompositeEncoder.class", "name": "kotlinx/serialization/encoding/CompositeEncoder.class", "size": 3592, "crc": 2043126991}, {"key": "kotlinx/serialization/encoding/Decoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/Decoder$DefaultImpls.class", "size": 2811, "crc": 1220084348}, {"key": "kotlinx/serialization/encoding/Decoder.class", "name": "kotlinx/serialization/encoding/Decoder.class", "size": 2641, "crc": -1531129898}, {"key": "kotlinx/serialization/encoding/DecodingKt.class", "name": "kotlinx/serialization/encoding/DecodingKt.class", "size": 3367, "crc": -890958758}, {"key": "kotlinx/serialization/encoding/Encoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/Encoder$DefaultImpls.class", "size": 2635, "crc": 320704621}, {"key": "kotlinx/serialization/encoding/Encoder.class", "name": "kotlinx/serialization/encoding/Encoder.class", "size": 2940, "crc": 643710307}, {"key": "kotlinx/serialization/encoding/EncodingKt.class", "name": "kotlinx/serialization/encoding/EncodingKt.class", "size": 5726, "crc": -1956870334}, {"key": "kotlinx/serialization/internal/AbstractCollectionSerializer.class", "name": "kotlinx/serialization/internal/AbstractCollectionSerializer.class", "size": 5562, "crc": 1963387192}, {"key": "kotlinx/serialization/internal/AbstractPolymorphicSerializer.class", "name": "kotlinx/serialization/internal/AbstractPolymorphicSerializer.class", "size": 10225, "crc": -1092842901}, {"key": "kotlinx/serialization/internal/AbstractPolymorphicSerializerKt.class", "name": "kotlinx/serialization/internal/AbstractPolymorphicSerializerKt.class", "size": 2685, "crc": 225505578}, {"key": "kotlinx/serialization/internal/ArrayClassDesc.class", "name": "kotlinx/serialization/internal/ArrayClassDesc.class", "size": 1335, "crc": 485232064}, {"key": "kotlinx/serialization/internal/ArrayListClassDesc.class", "name": "kotlinx/serialization/internal/ArrayListClassDesc.class", "size": 1359, "crc": -107163319}, {"key": "kotlinx/serialization/internal/ArrayListSerializer.class", "name": "kotlinx/serialization/internal/ArrayListSerializer.class", "size": 4570, "crc": -2033478393}, {"key": "kotlinx/serialization/internal/BooleanArrayBuilder.class", "name": "kotlinx/serialization/internal/BooleanArrayBuilder.class", "size": 2476, "crc": 2105152048}, {"key": "kotlinx/serialization/internal/BooleanArraySerializer.class", "name": "kotlinx/serialization/internal/BooleanArraySerializer.class", "size": 4624, "crc": 823619746}, {"key": "kotlinx/serialization/internal/BooleanSerializer.class", "name": "kotlinx/serialization/internal/BooleanSerializer.class", "size": 3015, "crc": 494075385}, {"key": "kotlinx/serialization/internal/ByteArrayBuilder.class", "name": "kotlinx/serialization/internal/ByteArrayBuilder.class", "size": 2470, "crc": -2123475485}, {"key": "kotlinx/serialization/internal/ByteArraySerializer.class", "name": "kotlinx/serialization/internal/ByteArraySerializer.class", "size": 4596, "crc": 255137837}, {"key": "kotlinx/serialization/internal/ByteSerializer.class", "name": "kotlinx/serialization/internal/ByteSerializer.class", "size": 2998, "crc": 99712469}, {"key": "kotlinx/serialization/internal/CacheEntry.class", "name": "kotlinx/serialization/internal/CacheEntry.class", "size": 1073, "crc": 1993260259}, {"key": "kotlinx/serialization/internal/CachedNames.class", "name": "kotlinx/serialization/internal/CachedNames.class", "size": 646, "crc": -1506700635}, {"key": "kotlinx/serialization/internal/CachingKt.class", "name": "kotlinx/serialization/internal/CachingKt.class", "size": 2940, "crc": -102045910}, {"key": "kotlinx/serialization/internal/CharArrayBuilder.class", "name": "kotlinx/serialization/internal/CharArrayBuilder.class", "size": 2470, "crc": -1495482974}, {"key": "kotlinx/serialization/internal/CharArraySerializer.class", "name": "kotlinx/serialization/internal/CharArraySerializer.class", "size": 4601, "crc": 1638733829}, {"key": "kotlinx/serialization/internal/CharSerializer.class", "name": "kotlinx/serialization/internal/CharSerializer.class", "size": 2996, "crc": 1734895545}, {"key": "kotlinx/serialization/internal/ClassValueCache$get$$inlined$getOrSet$1.class", "name": "kotlinx/serialization/internal/ClassValueCache$get$$inlined$getOrSet$1.class", "size": 2157, "crc": -209604039}, {"key": "kotlinx/serialization/internal/ClassValueCache.class", "name": "kotlinx/serialization/internal/ClassValueCache.class", "size": 4856, "crc": -221361455}, {"key": "kotlinx/serialization/internal/ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1.class", "name": "kotlinx/serialization/internal/ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1.class", "size": 1791, "crc": -1519320093}, {"key": "kotlinx/serialization/internal/ClassValueParametrizedCache.class", "name": "kotlinx/serialization/internal/ClassValueParametrizedCache.class", "size": 7922, "crc": 1736335599}, {"key": "kotlinx/serialization/internal/ClassValueReferences$getOrSet$2.class", "name": "kotlinx/serialization/internal/ClassValueReferences$getOrSet$2.class", "size": 1121, "crc": -1519234343}, {"key": "kotlinx/serialization/internal/ClassValueReferences.class", "name": "kotlinx/serialization/internal/ClassValueReferences.class", "size": 3515, "crc": 840133653}, {"key": "kotlinx/serialization/internal/CollectionDescriptorsKt.class", "name": "kotlinx/serialization/internal/CollectionDescriptorsKt.class", "size": 1056, "crc": 1708112939}, {"key": "kotlinx/serialization/internal/CollectionLikeSerializer.class", "name": "kotlinx/serialization/internal/CollectionLikeSerializer.class", "size": 6932, "crc": 1735901369}, {"key": "kotlinx/serialization/internal/CollectionSerializer.class", "name": "kotlinx/serialization/internal/CollectionSerializer.class", "size": 2349, "crc": -1450988816}, {"key": "kotlinx/serialization/internal/ConcurrentHashMapCache.class", "name": "kotlinx/serialization/internal/ConcurrentHashMapCache.class", "size": 4420, "crc": 133577576}, {"key": "kotlinx/serialization/internal/ConcurrentHashMapParametrizedCache.class", "name": "kotlinx/serialization/internal/ConcurrentHashMapParametrizedCache.class", "size": 7396, "crc": 1702082136}, {"key": "kotlinx/serialization/internal/CoreFriendModuleApi.class", "name": "kotlinx/serialization/internal/CoreFriendModuleApi.class", "size": 787, "crc": 1851392917}, {"key": "kotlinx/serialization/internal/DoubleArrayBuilder.class", "name": "kotlinx/serialization/internal/DoubleArrayBuilder.class", "size": 2474, "crc": -2123469042}, {"key": "kotlinx/serialization/internal/DoubleArraySerializer.class", "name": "kotlinx/serialization/internal/DoubleArraySerializer.class", "size": 4622, "crc": -1954036781}, {"key": "kotlinx/serialization/internal/DoubleSerializer.class", "name": "kotlinx/serialization/internal/DoubleSerializer.class", "size": 3024, "crc": 980019039}, {"key": "kotlinx/serialization/internal/DurationSerializer.class", "name": "kotlinx/serialization/internal/DurationSerializer.class", "size": 3356, "crc": 687339965}, {"key": "kotlinx/serialization/internal/ElementMarker$Companion.class", "name": "kotlinx/serialization/internal/ElementMarker$Companion.class", "size": 909, "crc": -303789083}, {"key": "kotlinx/serialization/internal/ElementMarker.class", "name": "kotlinx/serialization/internal/ElementMarker.class", "size": 4136, "crc": 761579933}, {"key": "kotlinx/serialization/internal/EnumDescriptor.class", "name": "kotlinx/serialization/internal/EnumDescriptor.class", "size": 7570, "crc": -1675810622}, {"key": "kotlinx/serialization/internal/EnumSerializer.class", "name": "kotlinx/serialization/internal/EnumSerializer.class", "size": 7247, "crc": 1292668172}, {"key": "kotlinx/serialization/internal/EnumsKt.class", "name": "kotlinx/serialization/internal/EnumsKt.class", "size": 6448, "crc": -832778246}, {"key": "kotlinx/serialization/internal/FloatArrayBuilder.class", "name": "kotlinx/serialization/internal/FloatArrayBuilder.class", "size": 2472, "crc": 2084029154}, {"key": "kotlinx/serialization/internal/FloatArraySerializer.class", "name": "kotlinx/serialization/internal/FloatArraySerializer.class", "size": 4609, "crc": 64840391}, {"key": "kotlinx/serialization/internal/FloatSerializer.class", "name": "kotlinx/serialization/internal/FloatSerializer.class", "size": 3011, "crc": 1383285068}, {"key": "kotlinx/serialization/internal/GeneratedSerializer$DefaultImpls.class", "name": "kotlinx/serialization/internal/GeneratedSerializer$DefaultImpls.class", "size": 1084, "crc": -256813556}, {"key": "kotlinx/serialization/internal/GeneratedSerializer.class", "name": "kotlinx/serialization/internal/GeneratedSerializer.class", "size": 1121, "crc": -1819292613}, {"key": "kotlinx/serialization/internal/HashMapClassDesc.class", "name": "kotlinx/serialization/internal/HashMapClassDesc.class", "size": 1324, "crc": 1025233315}, {"key": "kotlinx/serialization/internal/HashMapSerializer.class", "name": "kotlinx/serialization/internal/HashMapSerializer.class", "size": 5997, "crc": -1801053734}, {"key": "kotlinx/serialization/internal/HashSetClassDesc.class", "name": "kotlinx/serialization/internal/HashSetClassDesc.class", "size": 1353, "crc": -209863777}, {"key": "kotlinx/serialization/internal/HashSetSerializer.class", "name": "kotlinx/serialization/internal/HashSetSerializer.class", "size": 4443, "crc": -1657948966}, {"key": "kotlinx/serialization/internal/InlineClassDescriptor.class", "name": "kotlinx/serialization/internal/InlineClassDescriptor.class", "size": 3929, "crc": -1743255310}, {"key": "kotlinx/serialization/internal/InlineClassDescriptorKt$InlinePrimitiveDescriptor$1.class", "name": "kotlinx/serialization/internal/InlineClassDescriptorKt$InlinePrimitiveDescriptor$1.class", "size": 3102, "crc": 1059173198}, {"key": "kotlinx/serialization/internal/InlineClassDescriptorKt.class", "name": "kotlinx/serialization/internal/InlineClassDescriptorKt.class", "size": 1768, "crc": 1407770817}, {"key": "kotlinx/serialization/internal/IntArrayBuilder.class", "name": "kotlinx/serialization/internal/IntArrayBuilder.class", "size": 2446, "crc": -149049191}, {"key": "kotlinx/serialization/internal/IntArraySerializer.class", "name": "kotlinx/serialization/internal/IntArraySerializer.class", "size": 4576, "crc": -1539310246}, {"key": "kotlinx/serialization/internal/IntSerializer.class", "name": "kotlinx/serialization/internal/IntSerializer.class", "size": 3001, "crc": -1927530498}, {"key": "kotlinx/serialization/internal/InternalHexConverter.class", "name": "kotlinx/serialization/internal/InternalHexConverter.class", "size": 4383, "crc": 1789325135}, {"key": "kotlinx/serialization/internal/JsonInternalDependenciesKt.class", "name": "kotlinx/serialization/internal/JsonInternalDependenciesKt.class", "size": 1205, "crc": -1080766866}, {"key": "kotlinx/serialization/internal/KTypeWrapper.class", "name": "kotlinx/serialization/internal/KTypeWrapper.class", "size": 3098, "crc": -1643131767}, {"key": "kotlinx/serialization/internal/KeyValueSerializer.class", "name": "kotlinx/serialization/internal/KeyValueSerializer.class", "size": 7003, "crc": 22600597}, {"key": "kotlinx/serialization/internal/LinkedHashMapClassDesc.class", "name": "kotlinx/serialization/internal/LinkedHashMapClassDesc.class", "size": 1342, "crc": -1034399856}, {"key": "kotlinx/serialization/internal/LinkedHashMapSerializer.class", "name": "kotlinx/serialization/internal/LinkedHashMapSerializer.class", "size": 6111, "crc": 913030226}, {"key": "kotlinx/serialization/internal/LinkedHashSetClassDesc.class", "name": "kotlinx/serialization/internal/LinkedHashSetClassDesc.class", "size": 1371, "crc": 753519541}, {"key": "kotlinx/serialization/internal/LinkedHashSetSerializer.class", "name": "kotlinx/serialization/internal/LinkedHashSetSerializer.class", "size": 4561, "crc": -150313233}, {"key": "kotlinx/serialization/internal/ListLikeDescriptor.class", "name": "kotlinx/serialization/internal/ListLikeDescriptor.class", "size": 6369, "crc": -1844560544}, {"key": "kotlinx/serialization/internal/LongArrayBuilder.class", "name": "kotlinx/serialization/internal/LongArrayBuilder.class", "size": 2470, "crc": 1821286961}, {"key": "kotlinx/serialization/internal/LongArraySerializer.class", "name": "kotlinx/serialization/internal/LongArraySerializer.class", "size": 4596, "crc": 1256299406}, {"key": "kotlinx/serialization/internal/LongSerializer.class", "name": "kotlinx/serialization/internal/LongSerializer.class", "size": 2998, "crc": -1310924434}, {"key": "kotlinx/serialization/internal/MapEntrySerializer$MapEntry.class", "name": "kotlinx/serialization/internal/MapEntrySerializer$MapEntry.class", "size": 3625, "crc": 1322796044}, {"key": "kotlinx/serialization/internal/MapEntrySerializer.class", "name": "kotlinx/serialization/internal/MapEntrySerializer.class", "size": 5368, "crc": -1253836297}, {"key": "kotlinx/serialization/internal/MapLikeDescriptor.class", "name": "kotlinx/serialization/internal/MapLikeDescriptor.class", "size": 6886, "crc": -1612889726}, {"key": "kotlinx/serialization/internal/MapLikeSerializer.class", "name": "kotlinx/serialization/internal/MapLikeSerializer.class", "size": 10308, "crc": 221714883}, {"key": "kotlinx/serialization/internal/MutableSoftReference.class", "name": "kotlinx/serialization/internal/MutableSoftReference.class", "size": 2209, "crc": -1428143238}, {"key": "kotlinx/serialization/internal/NamedCompanion.class", "name": "kotlinx/serialization/internal/NamedCompanion.class", "size": 886, "crc": 481925906}, {"key": "kotlinx/serialization/internal/NamedValueDecoder.class", "name": "kotlinx/serialization/internal/NamedValueDecoder.class", "size": 3358, "crc": 458427482}, {"key": "kotlinx/serialization/internal/NamedValueEncoder.class", "name": "kotlinx/serialization/internal/NamedValueEncoder.class", "size": 2760, "crc": -1934161453}, {"key": "kotlinx/serialization/internal/NoOpEncoder.class", "name": "kotlinx/serialization/internal/NoOpEncoder.class", "size": 3105, "crc": 931113894}, {"key": "kotlinx/serialization/internal/NothingSerialDescriptor.class", "name": "kotlinx/serialization/internal/NothingSerialDescriptor.class", "size": 4321, "crc": 397105069}, {"key": "kotlinx/serialization/internal/NothingSerializer.class", "name": "kotlinx/serialization/internal/NothingSerializer.class", "size": 2629, "crc": -1967785888}, {"key": "kotlinx/serialization/internal/NullableSerializer.class", "name": "kotlinx/serialization/internal/NullableSerializer.class", "size": 3699, "crc": -1914869410}, {"key": "kotlinx/serialization/internal/ObjectSerializer.class", "name": "kotlinx/serialization/internal/ObjectSerializer.class", "size": 7840, "crc": -991880261}, {"key": "kotlinx/serialization/internal/PairSerializer.class", "name": "kotlinx/serialization/internal/PairSerializer.class", "size": 4860, "crc": 1000566743}, {"key": "kotlinx/serialization/internal/ParametrizedCacheEntry.class", "name": "kotlinx/serialization/internal/ParametrizedCacheEntry.class", "size": 5610, "crc": -35718377}, {"key": "kotlinx/serialization/internal/ParametrizedSerializerCache$DefaultImpls.class", "name": "kotlinx/serialization/internal/ParametrizedSerializerCache$DefaultImpls.class", "size": 1026, "crc": -924964078}, {"key": "kotlinx/serialization/internal/ParametrizedSerializerCache.class", "name": "kotlinx/serialization/internal/ParametrizedSerializerCache.class", "size": 1218, "crc": 1623524862}, {"key": "kotlinx/serialization/internal/PlatformKt.class", "name": "kotlinx/serialization/internal/PlatformKt.class", "size": 21681, "crc": -1078845853}, {"key": "kotlinx/serialization/internal/Platform_commonKt.class", "name": "kotlinx/serialization/internal/Platform_commonKt.class", "size": 9318, "crc": -124754095}, {"key": "kotlinx/serialization/internal/PluginExceptionsKt.class", "name": "kotlinx/serialization/internal/PluginExceptionsKt.class", "size": 2176, "crc": 1679667813}, {"key": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptor.class", "name": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptor.class", "size": 15103, "crc": 2143932241}, {"key": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptorKt.class", "name": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptorKt.class", "size": 5825, "crc": 1919931721}, {"key": "kotlinx/serialization/internal/PluginHelperInterfacesKt.class", "name": "kotlinx/serialization/internal/PluginHelperInterfacesKt.class", "size": 815, "crc": 458937055}, {"key": "kotlinx/serialization/internal/PrimitiveArrayBuilder.class", "name": "kotlinx/serialization/internal/PrimitiveArrayBuilder.class", "size": 1529, "crc": -345005275}, {"key": "kotlinx/serialization/internal/PrimitiveArrayDescriptor.class", "name": "kotlinx/serialization/internal/PrimitiveArrayDescriptor.class", "size": 1638, "crc": 1060529193}, {"key": "kotlinx/serialization/internal/PrimitiveArraySerializer.class", "name": "kotlinx/serialization/internal/PrimitiveArraySerializer.class", "size": 7849, "crc": 974197954}, {"key": "kotlinx/serialization/internal/PrimitiveArraysSerializersKt.class", "name": "kotlinx/serialization/internal/PrimitiveArraysSerializersKt.class", "size": 437, "crc": 1256894}, {"key": "kotlinx/serialization/internal/PrimitiveSerialDescriptor.class", "name": "kotlinx/serialization/internal/PrimitiveSerialDescriptor.class", "size": 4584, "crc": 2076460214}, {"key": "kotlinx/serialization/internal/PrimitivesKt.class", "name": "kotlinx/serialization/internal/PrimitivesKt.class", "size": 5079, "crc": -475643628}, {"key": "kotlinx/serialization/internal/ReferenceArraySerializer.class", "name": "kotlinx/serialization/internal/ReferenceArraySerializer.class", "size": 5983, "crc": 666360811}, {"key": "kotlinx/serialization/internal/SerialDescriptorForNullable.class", "name": "kotlinx/serialization/internal/SerialDescriptorForNullable.class", "size": 4785, "crc": 427603781}, {"key": "kotlinx/serialization/internal/SerializationConstructorMarker.class", "name": "kotlinx/serialization/internal/SerializationConstructorMarker.class", "size": 782, "crc": 1021992326}, {"key": "kotlinx/serialization/internal/SerializerCache$DefaultImpls.class", "name": "kotlinx/serialization/internal/SerializerCache$DefaultImpls.class", "size": 1006, "crc": -728964818}, {"key": "kotlinx/serialization/internal/SerializerCache.class", "name": "kotlinx/serialization/internal/SerializerCache.class", "size": 1204, "crc": 469753811}, {"key": "kotlinx/serialization/internal/SerializerFactory.class", "name": "kotlinx/serialization/internal/SerializerFactory.class", "size": 1061, "crc": 17188796}, {"key": "kotlinx/serialization/internal/ShortArrayBuilder.class", "name": "kotlinx/serialization/internal/ShortArrayBuilder.class", "size": 2472, "crc": 222740510}, {"key": "kotlinx/serialization/internal/ShortArraySerializer.class", "name": "kotlinx/serialization/internal/ShortArraySerializer.class", "size": 4609, "crc": -1693852902}, {"key": "kotlinx/serialization/internal/ShortSerializer.class", "name": "kotlinx/serialization/internal/ShortSerializer.class", "size": 3011, "crc": -1031370792}, {"key": "kotlinx/serialization/internal/StringSerializer.class", "name": "kotlinx/serialization/internal/StringSerializer.class", "size": 2999, "crc": -1577265570}, {"key": "kotlinx/serialization/internal/SuppressAnimalSniffer.class", "name": "kotlinx/serialization/internal/SuppressAnimalSniffer.class", "size": 851, "crc": -1190926369}, {"key": "kotlinx/serialization/internal/TaggedDecoder.class", "name": "kotlinx/serialization/internal/TaggedDecoder.class", "size": 18001, "crc": 2007908463}, {"key": "kotlinx/serialization/internal/TaggedEncoder.class", "name": "kotlinx/serialization/internal/TaggedEncoder.class", "size": 15733, "crc": 780711108}, {"key": "kotlinx/serialization/internal/TripleSerializer.class", "name": "kotlinx/serialization/internal/TripleSerializer.class", "size": 8218, "crc": 1541485686}, {"key": "kotlinx/serialization/internal/TuplesKt.class", "name": "kotlinx/serialization/internal/TuplesKt.class", "size": 918, "crc": -380096717}, {"key": "kotlinx/serialization/internal/UByteArrayBuilder.class", "name": "kotlinx/serialization/internal/UByteArrayBuilder.class", "size": 3038, "crc": 1726868826}, {"key": "kotlinx/serialization/internal/UByteArraySerializer.class", "name": "kotlinx/serialization/internal/UByteArraySerializer.class", "size": 5497, "crc": 1450244943}, {"key": "kotlinx/serialization/internal/UByteSerializer.class", "name": "kotlinx/serialization/internal/UByteSerializer.class", "size": 3395, "crc": -1000482198}, {"key": "kotlinx/serialization/internal/UIntArrayBuilder.class", "name": "kotlinx/serialization/internal/UIntArrayBuilder.class", "size": 3020, "crc": 860339019}, {"key": "kotlinx/serialization/internal/UIntArraySerializer.class", "name": "kotlinx/serialization/internal/UIntArraySerializer.class", "size": 5489, "crc": 1125734488}, {"key": "kotlinx/serialization/internal/UIntSerializer.class", "name": "kotlinx/serialization/internal/UIntSerializer.class", "size": 3383, "crc": -624433220}, {"key": "kotlinx/serialization/internal/ULongArrayBuilder.class", "name": "kotlinx/serialization/internal/ULongArrayBuilder.class", "size": 3038, "crc": 1281643955}, {"key": "kotlinx/serialization/internal/ULongArraySerializer.class", "name": "kotlinx/serialization/internal/ULongArraySerializer.class", "size": 5497, "crc": -1126355638}, {"key": "kotlinx/serialization/internal/ULongSerializer.class", "name": "kotlinx/serialization/internal/ULongSerializer.class", "size": 3395, "crc": -481793014}, {"key": "kotlinx/serialization/internal/UShortArrayBuilder.class", "name": "kotlinx/serialization/internal/UShortArrayBuilder.class", "size": 3045, "crc": 957884435}, {"key": "kotlinx/serialization/internal/UShortArraySerializer.class", "name": "kotlinx/serialization/internal/UShortArraySerializer.class", "size": 5517, "crc": 749300962}, {"key": "kotlinx/serialization/internal/UShortSerializer.class", "name": "kotlinx/serialization/internal/UShortSerializer.class", "size": 3407, "crc": -828211758}, {"key": "kotlinx/serialization/internal/UnitSerializer.class", "name": "kotlinx/serialization/internal/UnitSerializer.class", "size": 2579, "crc": -597984280}, {"key": "kotlinx/serialization/internal/UuidSerializer.class", "name": "kotlinx/serialization/internal/UuidSerializer.class", "size": 3232, "crc": **********}, {"key": "kotlinx/serialization/modules/ContextualProvider$Argless.class", "name": "kotlinx/serialization/modules/ContextualProvider$Argless.class", "size": 2338, "crc": -**********}, {"key": "kotlinx/serialization/modules/ContextualProvider$WithTypeArguments.class", "name": "kotlinx/serialization/modules/ContextualProvider$WithTypeArguments.class", "size": 2465, "crc": **********}, {"key": "kotlinx/serialization/modules/ContextualProvider.class", "name": "kotlinx/serialization/modules/ContextualProvider.class", "size": 1574, "crc": -**********}, {"key": "kotlinx/serialization/modules/PolymorphicModuleBuilder.class", "name": "kotlinx/serialization/modules/PolymorphicModuleBuilder.class", "size": 7596, "crc": -**********}, {"key": "kotlinx/serialization/modules/PolymorphicModuleBuilderKt.class", "name": "kotlinx/serialization/modules/PolymorphicModuleBuilderKt.class", "size": 2183, "crc": -925239072}, {"key": "kotlinx/serialization/modules/SerialModuleImpl.class", "name": "kotlinx/serialization/modules/SerialModuleImpl.class", "size": 11811, "crc": **********}, {"key": "kotlinx/serialization/modules/SerializerAlreadyRegisteredException.class", "name": "kotlinx/serialization/modules/SerializerAlreadyRegisteredException.class", "size": 1672, "crc": -258061215}, {"key": "kotlinx/serialization/modules/SerializersModule.class", "name": "kotlinx/serialization/modules/SerializersModule.class", "size": 4106, "crc": 767422611}, {"key": "kotlinx/serialization/modules/SerializersModuleBuilder.class", "name": "kotlinx/serialization/modules/SerializersModuleBuilder.class", "size": 14575, "crc": 402589463}, {"key": "kotlinx/serialization/modules/SerializersModuleBuildersKt$polymorphic$1.class", "name": "kotlinx/serialization/modules/SerializersModuleBuildersKt$polymorphic$1.class", "size": 1753, "crc": -875147788}, {"key": "kotlinx/serialization/modules/SerializersModuleBuildersKt.class", "name": "kotlinx/serialization/modules/SerializersModuleBuildersKt.class", "size": 6166, "crc": -365614948}, {"key": "kotlinx/serialization/modules/SerializersModuleCollector$DefaultImpls.class", "name": "kotlinx/serialization/modules/SerializersModuleCollector$DefaultImpls.class", "size": 3041, "crc": 1435049988}, {"key": "kotlinx/serialization/modules/SerializersModuleCollector.class", "name": "kotlinx/serialization/modules/SerializersModuleCollector.class", "size": 3373, "crc": 1041054847}, {"key": "kotlinx/serialization/modules/SerializersModuleKt$overwriteWith$1$1.class", "name": "kotlinx/serialization/modules/SerializersModuleKt$overwriteWith$1$1.class", "size": 5447, "crc": -1523638319}, {"key": "kotlinx/serialization/modules/SerializersModuleKt.class", "name": "kotlinx/serialization/modules/SerializersModuleKt.class", "size": 4481, "crc": 143351990}, {"key": "META-INF/proguard/kotlinx-serialization-common.pro", "name": "META-INF/proguard/kotlinx-serialization-common.pro", "size": 1801, "crc": -709341024}, {"key": "META-INF/com.android.tools/proguard/kotlinx-serialization-common.pro", "name": "META-INF/com.android.tools/proguard/kotlinx-serialization-common.pro", "size": 1801, "crc": -709341024}, {"key": "META-INF/com.android.tools/r8/kotlinx-serialization-common.pro", "name": "META-INF/com.android.tools/r8/kotlinx-serialization-common.pro", "size": 1801, "crc": -709341024}, {"key": "META-INF/com.android.tools/r8/kotlinx-serialization-r8.pro", "name": "META-INF/com.android.tools/r8/kotlinx-serialization-r8.pro", "size": 667, "crc": -2058155282}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 435, "crc": -921077919}]